const express = require("express");
const router = express.Router();
const { ensureAuthenticated } = require("../config/auth");
const PayRun = require("../models/payRun");
const Company = require("../models/Company");
const nodeFetch = require("node-fetch");
const csrf = require("csurf");
const axios = require("axios");
const PDFDocument = require("pdfkit-table");
const ExcelJS = require("exceljs");
const path = require("path");
const fs = require("fs");
const moment = require("moment");
const Payslip = require("../models/Payslip");

// 🚨 UTILITY FUNCTION: Calculate custom income dynamically like employeeProfile.ejs
function calculateCustomIncomeTotal(customIncomeItems) {
  if (!customIncomeItems || !Array.isArray(customIncomeItems) || customIncomeItems.length === 0) {
    return 0;
  }

  return customIncomeItems.reduce((total, item) => {
    let amount = 0;
    if (item.calculatedAmount && item.calculatedAmount > 0) {
      amount = Number(item.calculatedAmount);
    } else if (typeof item.amount === 'number') {
      amount = Number(item.amount);
    } else if (typeof item.monthlyAmount === 'number') {
      amount = Number(item.monthlyAmount);
    }
    return total + amount;
  }, 0);
}

// Initialize CSRF protection
const csrfProtection = csrf({ cookie: true });

// Simple logging middleware
router.use((req, res, next) => {
  next();
});

// Client-facing route for payrun details
router.get("/:companyCode/payruns/:id", [ensureAuthenticated, csrfProtection], async (req, res) => {

  try {
    // Get the company code - try URL param first, then session, then user's current company
    let companyCode = req.params.companyCode;
    
    // If companyCode is undefined in URL, try to get it from session or user's current company
    if (!companyCode || companyCode === 'undefined') {
      // Try to get from session
      if (req.session && req.session.companyCode) {
        companyCode = req.session.companyCode;
        
        // Redirect to the correct URL with the company code
        const correctUrl = `/clients/${companyCode}/payruns/${req.params.id}`;
        return res.redirect(correctUrl);
      }
      
      // If still no company code, try to get from user's current company
      if (req.user && req.user.currentCompany) {
        // Find the company by ID to get its code
        const userCompany = await Company.findById(req.user.currentCompany);
        if (userCompany && userCompany.companyCode) {
          companyCode = userCompany.companyCode;
          
          // Redirect to the correct URL with the company code
          const correctUrl = `/clients/${companyCode}/payruns/${req.params.id}`;
          return res.redirect(correctUrl);
        }
      }
    }
    
    if (!companyCode || companyCode === 'undefined') {
      return res.status(404).render('error', { 
        message: 'Company code not found',
        error: { status: 404, stack: '' }
      });
    }
    
    
    // Find company
    const company = await Company.findOne({ companyCode });

    if (!company) {
      return res.status(404).render('error', { 
        message: 'Company not found',
        error: { status: 404, stack: '' }
      });
    }


    // Find payrun and populate payslips and employee data
    const payRun = await PayRun.findOne({
      _id: req.params.id,
      company: company._id
    }).populate({
      path: 'payslips',
      populate: [
        {
          path: 'employee',
          select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
        },
        {
          path: 'payrollPeriod',
          select: 'basicSalary grossPay totalDeductions netPay PAYE UIF SDL customIncomeItems'
        }
      ]
    }).populate({
      path: 'payrollPeriods',
      populate: {
        path: 'employee',
        select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
      }
    });


    // If pay run not found, return 404
    if (!payRun) {
      return res.status(404).render('error', {
        message: 'Pay run not found',
        error: { status: 404 }
      });
    }

    console.log(`🔍 Pay Run Details Debug:`, {
      payRunId: payRun._id,
      frequency: payRun.frequency,
      payslipsCount: payRun.payslips?.length || 0,
      payrollPeriodsCount: payRun.payrollPeriods?.length || 0,
      startDate: payRun.startDate,
      endDate: payRun.endDate
    });

    // If no payroll periods found, try to find them separately
    if (!payRun.payrollPeriods || payRun.payrollPeriods.length === 0) {
      
      // Find payroll periods for this pay run's date range AND frequency
      const PayrollPeriod = require('../models/PayrollPeriod');
      const payrollPeriods = await PayrollPeriod.find({
        company: company._id,
        frequency: payRun.frequency, // ✅ CRITICAL: Filter by Pay Run's frequency
        startDate: { $lte: payRun.endDate },
        endDate: { $gte: payRun.startDate }
      }).populate({
        path: 'employee',
        select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
      });

      console.log(`🔍 Found ${payrollPeriods.length} payroll periods for ${payRun.frequency} frequency in Pay Run ${payRun._id}`);

      // Debug: Check if any periods have different frequencies
      if (payrollPeriods.length > 0) {
        const frequencyCheck = payrollPeriods.map(p => ({
          id: p._id,
          frequency: p.frequency,
          employee: `${p.employee?.firstName} ${p.employee?.lastName}`
        }));
        console.log(`📋 Payroll periods frequency check:`, frequencyCheck);
      }

      // Save the IDs to the pay run
      if (payrollPeriods.length > 0) {
        try {
          payRun.payrollPeriods = payrollPeriods.map(p => p._id);
          await payRun.save();
        } catch (error) {
          console.error('Error updating pay run with payroll periods:', error);
        }
      }

      // Attach the populated payroll periods to the pay run object for rendering
      // This is important because we need the populated data for the template
      payRun.payrollPeriods = payrollPeriods;
    } else {
      // If payrollPeriods exist but aren't populated, populate them
      if (payRun.payrollPeriods.length > 0 &&
          (!payRun.payrollPeriods[0].employee || typeof payRun.payrollPeriods[0].employee === 'string' ||
           !payRun.payrollPeriods[0].employee.firstName)) {
        await payRun.populate({
          path: 'payrollPeriods',
          populate: {
            path: 'employee',
            select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
          }
        });
      }
    }

    // Debug: Check payslips for frequency consistency
    if (payRun.payslips && payRun.payslips.length > 0) {
      const payslipFrequencyCheck = payRun.payslips.map(p => ({
        id: p._id,
        employee: `${p.employee?.firstName} ${p.employee?.lastName}`,
        employeeFrequency: p.employee?.payFrequency?.frequency || p.employee?.payFrequency || 'unknown'
      }));
      console.log(`💰 Payslips frequency check:`, payslipFrequencyCheck);

      // Filter payslips to match Pay Run frequency if needed
      const filteredPayslips = payRun.payslips.filter(payslip => {
        let payslipFreq = 'monthly'; // default
        if (payslip.employee?.payFrequency) {
          if (typeof payslip.employee.payFrequency === 'object' && payslip.employee.payFrequency.frequency) {
            payslipFreq = payslip.employee.payFrequency.frequency;
          } else if (typeof payslip.employee.payFrequency === 'string') {
            payslipFreq = payslip.employee.payFrequency;
          }
        }
        return payslipFreq === payRun.frequency;
      });

      if (filteredPayslips.length !== payRun.payslips.length) {
        console.warn(`⚠️ Filtered payslips from ${payRun.payslips.length} to ${filteredPayslips.length} to match ${payRun.frequency} frequency`);
        payRun.payslips = filteredPayslips;
      }
    }

    // Calculate totals - use payslips if available, otherwise use payroll periods
    let totals;
    
    console.log('🔍 PAYRUNS.JS: PayRun data structure debug:', {
      payRunId: payRun._id,
      hasPayslips: !!(payRun.payslips && payRun.payslips.length > 0),
      payslipsCount: payRun.payslips?.length || 0,
      hasPayrollPeriods: !!(payRun.payrollPeriods && payRun.payrollPeriods.length > 0),
      payrollPeriodsCount: payRun.payrollPeriods?.length || 0
    });

    // 🚨 CRITICAL FIX: Fetch Payroll data with custom income items for accurate calculations
    // PayRun data doesn't include custom income, so we need to fetch it from Payroll records
    const Payroll = require("../models/Payroll");

    if (payRun.payrollPeriods && payRun.payrollPeriods.length > 0) {
      console.log('🔍 PAYRUNS.JS: Enhancing payrollPeriods with Payroll data for custom income...');

      for (let period of payRun.payrollPeriods) {
        if (period.employee && period.employee._id) {
          try {
            // 🚨 CRITICAL FIX: Use exact PayRun period to prevent finalized data from changing
            // For finalized PayRuns, we must use the exact month to preserve historical data integrity
            const payRunMonth = moment(payRun.startDate).format('YYYY-MM-DD');

            console.log(`🔍 Fetching Payroll data for ${period.employee.firstName} ${period.employee.lastName} for exact month: ${payRunMonth}`);

            const payrollData = await Payroll.findOne({
              employee: period.employee._id,
              company: company._id,
              month: payRunMonth  // Use exact month, not range
            }).select('customIncomeItems basicSalary month').lean();

            if (payrollData && payrollData.customIncomeItems) {
              period.customIncomeItems = payrollData.customIncomeItems;
              console.log(`✅ PERIOD-SPECIFIC: Enhanced ${period.employee.firstName} ${period.employee.lastName} with ${payrollData.customIncomeItems.length} custom income items from month ${payrollData.month}`);
            } else {
              // 🚨 IMPORTANT: For finalized PayRuns, we should NOT use fallback data from other periods
              // This preserves historical data integrity
              period.customIncomeItems = [];
              console.log(`⚠️ PERIOD-SPECIFIC: No custom income found for ${period.employee.firstName} ${period.employee.lastName} in month ${payRunMonth} - preserving historical accuracy`);
            }
          } catch (error) {
            console.error(`❌ Error fetching custom income for ${period.employee.firstName} ${period.employee.lastName}:`, error);
            period.customIncomeItems = [];
          }
        }
      }
    }

    if (payRun.payslips && payRun.payslips.length > 0) {
      // Calculate from payslips with comprehensive calculations
      totals = payRun.payslips.reduce((acc, payslip) => {
        // 🚨 SURGICAL FIX: Apply comprehensive wage component calculations like employeeProfile.ejs
        const basicSalary = payslip.basicSalary || 0;

        // 🔧 DYNAMIC CUSTOM INCOME CALCULATION (like employeeProfile.ejs)
        const customIncomeForUIF = calculateCustomIncomeTotal(payslip.customIncomeItems);

        console.log('🎯 PAYRUNS.JS PAYSLIP: Dynamic custom income calculation:', {
          basicSalary,
          customIncomeItems: payslip.customIncomeItems?.length || 0,
          customIncomeForUIF,
          employee: payslip.employee ? `${payslip.employee.firstName} ${payslip.employee.lastName}` : 'Unknown'
        });

        const comprehensiveGrossIncome = basicSalary + customIncomeForUIF;
        const comprehensiveUIF = Math.min(comprehensiveGrossIncome * 0.01, 177.12);
        const comprehensiveTotalDeductions = (payslip.PAYE || 0) + comprehensiveUIF;
        const comprehensiveNetPay = comprehensiveGrossIncome - comprehensiveTotalDeductions;

        console.log('🔍 PAYRUNS.JS PAYSLIP CALCULATION:', {
          employee: payslip.employee ? `${payslip.employee.firstName} ${payslip.employee.lastName}` : 'Unknown',
          basicSalary,
          customIncome: customIncomeForUIF,
          comprehensiveGross: comprehensiveGrossIncome,
          storedGross: payslip.grossPay,
          storedUIF: payslip.UIF,
          comprehensiveUIF,
          comprehensiveNetPay,
          storedNetPay: payslip.netPay
        });

        acc.totalGrossPay += comprehensiveGrossIncome;
        acc.totalNetPay += comprehensiveNetPay;
        acc.totalPAYE += payslip.PAYE || 0;
        acc.totalSDL += comprehensiveGrossIncome * 0.01;
        acc.totalUIF += comprehensiveUIF;
        return acc;
      }, {
        totalGrossPay: 0,
        totalNetPay: 0,
        totalPAYE: 0,
        totalSDL: 0,
        totalUIF: 0
      });
    } else if (payRun.payrollPeriods && payRun.payrollPeriods.length > 0) {
      // Calculate from payroll periods with comprehensive calculations
      totals = payRun.payrollPeriods.reduce((acc, period) => {
        // 🚨 SURGICAL FIX: Apply comprehensive wage component calculations like employeeProfile.ejs
        const basicSalary = period.basicSalary || 0;

        // 🔧 DYNAMIC CUSTOM INCOME CALCULATION (like employeeProfile.ejs)
        const customIncomeForUIF = calculateCustomIncomeTotal(period.customIncomeItems);

        console.log('🎯 PAYRUNS.JS PERIOD: Dynamic custom income calculation:', {
          basicSalary,
          customIncomeItems: period.customIncomeItems?.length || 0,
          customIncomeForUIF,
          employee: period.employee ? `${period.employee.firstName} ${period.employee.lastName}` : 'Unknown'
        });

        const comprehensiveGrossIncome = basicSalary + customIncomeForUIF;
        const comprehensiveUIF = Math.min(comprehensiveGrossIncome * 0.01, 177.12);
        const comprehensiveTotalDeductions = (period.PAYE || 0) + comprehensiveUIF;
        const comprehensiveNetPay = comprehensiveGrossIncome - comprehensiveTotalDeductions;

        console.log('🔍 PAYRUNS.JS PERIOD CALCULATION:', {
          employee: period.employee ? `${period.employee.firstName} ${period.employee.lastName}` : 'Unknown',
          basicSalary,
          customIncome: customIncomeForUIF,
          comprehensiveGross: comprehensiveGrossIncome,
          storedGross: period.grossPay,
          storedUIF: period.UIF,
          comprehensiveUIF,
          comprehensiveNetPay,
          storedNetPay: period.netPay
        });

        acc.totalGrossPay += comprehensiveGrossIncome;
        acc.totalNetPay += comprehensiveNetPay;
        acc.totalPAYE += period.PAYE || 0;
        acc.totalSDL += comprehensiveGrossIncome * 0.01;
        acc.totalUIF += comprehensiveUIF;
        return acc;
      }, {
        totalGrossPay: 0,
        totalNetPay: 0,
        totalPAYE: 0,
        totalSDL: 0,
        totalUIF: 0
      });
    } else {
      // Default to zeros if neither payslips nor payroll periods
      totals = {
        totalGrossPay: 0,
        totalNetPay: 0,
        totalPAYE: 0,
        totalSDL: 0,
        totalUIF: 0
      };
    }

    console.log('🔍 PAYRUNS.JS FINAL TOTALS:', {
      totalGrossPay: totals.totalGrossPay,
      totalNetPay: totals.totalNetPay,
      totalUIF: totals.totalUIF,
      totalSDL: totals.totalSDL,
      totalPAYE: totals.totalPAYE,
      payRunId: payRun._id
    });


    // Render the payrun details page
    res.render('payRunDetails', {
      title: 'Pay Run Details',
      company,
      payRun,
      user: req.user,
      csrfToken: req.csrfToken(),
      moment: require('moment'),
      ...totals
    });

  } catch (error) {
    console.error('\n=== Error in Pay Run Details Route ===');
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    
    res.status(500).render('error', {
      message: 'Error fetching pay run details',
      error: { 
        status: 500, 
        stack: process.env.NODE_ENV === 'development' ? error.stack : '' 
      }
    });
  }
});

// Process a pay run
router.post("/:companyCode/payruns/:id/process", [ensureAuthenticated, csrfProtection], async (req, res) => {

  try {
    // Find the company
    const company = await Company.findOne({ companyCode: req.params.companyCode });
    if (!company) {
      return res.status(404).render('error', { 
        message: 'Company not found',
        error: { status: 404, stack: '' }
      });
    }

    // Find the pay run
    const payRun = await PayRun.findOne({
      _id: req.params.id,
      company: company._id
    });

    if (!payRun) {
      return res.status(404).render('error', { 
        message: 'Pay run not found',
        error: { status: 404, stack: '' }
      });
    }

    // Update the pay run status to processing
    payRun.status = 'processing';
    await payRun.save();


    // Redirect back to the pay run details page
    res.redirect(`/clients/${company.companyCode}/payruns/${payRun._id}`);

  } catch (error) {
    console.error('\n=== Error in Process Pay Run Route ===');
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    
    res.status(500).render('error', {
      message: 'Error processing pay run',
      error: { status: 500, stack: process.env.NODE_ENV === 'development' ? error.stack : '' }
    });
  }
});

// Finalize a pay run
router.post("/:companyCode/payruns/:id/finalize", [ensureAuthenticated, csrfProtection], async (req, res) => {

  try {
    // Validate company
    const company = await Company.findOne({ companyCode: req.params.companyCode });
    if (!company) {
      throw new Error('Company not found');
    }

    // Find the pay run
    const payRun = await PayRun.findOne({
      _id: req.params.id,
      company: company._id
    }).populate({
      path: 'payrollPeriods',
      populate: {
        path: 'employee',
        select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
      }
    });

    if (!payRun) {
      throw new Error('Pay run not found');
    }

    // If no payroll periods found, try to find them separately
    if (!payRun.payrollPeriods || payRun.payrollPeriods.length === 0) {
      
      // Find payroll periods for this pay run's date range
      const PayrollPeriod = require('../models/PayrollPeriod');
      const payrollPeriods = await PayrollPeriod.find({
        company: company._id,
        startDate: { $lte: payRun.endDate },
        endDate: { $gte: payRun.startDate }
      }).populate({
        path: 'employee',
        select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
      });
      
      
      // Attach them to the pay run
      payRun.payrollPeriods = payrollPeriods;
      
      // Save the updated pay run with the payroll periods
      if (payrollPeriods.length > 0) {
        try {
          payRun.payrollPeriods = payrollPeriods.map(p => p._id);
          await payRun.save();
          
          // Re-fetch the pay run with populated payroll periods
          await payRun.populate({
            path: 'payrollPeriods',
            populate: {
              path: 'employee',
              select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
            }
          });
        } catch (error) {
          console.error('Error updating pay run with payroll periods:', error);
        }
      }
    }

    // Check if payslips need to be created
    if ((!payRun.payslips || payRun.payslips.length === 0) && payRun.payrollPeriods && payRun.payrollPeriods.length > 0) {
      
      const Payslip = require('../models/Payslip');
      
      // Create payslips from payroll periods
      const payslips = await Promise.all(
        payRun.payrollPeriods.map(async (period) => {
          
          const payslip = new Payslip({
            company: company._id,
            employee: period.employee._id,
            payrollPeriod: period._id,
            payRun: payRun._id,
            basicSalary: period.basicSalary || 0,
            grossPay: period.grossPay || 0,
            totalDeductions: period.totalDeductions || 0,
            netPay: period.netPay || 0,
            status: 'approved',
            paymentMethod: period.employee.paymentMethod || 'EFT',
            bankDetails: period.employee.bankDetails || {}
          });
          
          const savedPayslip = await payslip.save();
          return savedPayslip;
        })
      );
      
      
      // Update payRun with the newly created payslips
      payRun.payslips = [...(payRun.payslips || []), ...payslips.map(p => p._id)];
      payRun.status = 'finalized';
      payRun.finalizedAt = new Date();
      payRun.finalizedBy = req.user._id;
      
      await payRun.save();
    }

    res.json({
      success: true,
      message: 'Pay run finalized successfully',
      payRunId: payRun._id
    });

  } catch (error) {
    console.error('\n=== Error in Finalize Pay Run Route ===');
    console.error(error);
    console.error('=== End of Error ===\n');

    // Send a JSON response with the error details
    res.status(500).json({
      success: false,
      message: 'Failed to finalize pay run',
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// GET /api/pay-runs
router.get("/", ensureAuthenticated, async (req, res) => {
  try {
    const payRuns = await PayRun.find();
    res.json({ success: true, data: payRuns });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// GET /api/pay-runs/:id
router.get("/:id", ensureAuthenticated, async (req, res) => {
  try {
    const payRun = await PayRun.findById(req.params.id);
    if (!payRun) {
      return res
        .status(404)
        .json({ success: false, message: "Pay run not found" });
    }
    res.json({ success: true, data: payRun });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// POST /api/pay-runs/:id/xero
router.post(
  "/:id/xero",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {

      const payRun = await PayRun.findById(req.params.id);
      if (!payRun) {
        return res.status(404).json({
          success: false,
          message: "Pay run not found",
        });
      }

      // Get the company for this pay run
      const company = await Company.findById(payRun.company);
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Get CSRF token
      const csrfToken = req.csrfToken();

      // Get base URL from environment variables with fallback
      const protocol = process.env.PROTOCOL || "http";
      const host = process.env.HOST || "localhost:3002";
      const baseUrl = `${protocol}://${host}`;

      // Include company code in URL path
      const syncUrl = `${baseUrl}/xero/${company.companyCode}/sync-payroll`;

      // Use axios instead of fetch
      const xeroResponse = await axios({
        method: "post",
        url: syncUrl,
        headers: {
          "Content-Type": "application/json",
          "CSRF-Token": csrfToken,
          Cookie: req.headers.cookie,
        },
        data: {
          payrollId: payRun._id,
          payrollData: {
            basicSalary: payRun.totalAmount,
            employeeCount: payRun.payslips?.length || 0,
            period: {
              startDate: payRun.startDate,
              endDate: payRun.endDate,
            },
          },
          description: `Payroll sync for period ${payRun.startDate} to ${payRun.endDate}`,
        },
        withCredentials: true,
        timeout: 30000, // 30 second timeout
        validateStatus: function (status) {
          return status >= 200 && status < 500; // Don't reject if status is 4xx
        },
      });


      if (xeroResponse.status === 404) {
        throw new Error(
          "Xero sync endpoint not found. Please check the server configuration."
        );
      }

      if (xeroResponse.status !== 200) {
        console.error("Xero sync failed:", xeroResponse.data);
        throw new Error(
          xeroResponse.data.message || "Failed to sync with Xero"
        );
      }

      const result = xeroResponse.data;

      // Update pay run with Xero sync status
      await PayRun.findByIdAndUpdate(payRun._id, {
        $set: {
          xeroSynced: true,
          xeroSyncedAt: new Date(),
          xeroSyncStatus: result.status,
        },
      });

      res.json({
        success: true,
        message: "Successfully initiated Xero sync",
        payRunId: payRun._id,
        syncStatus: result,
      });
    } catch (error) {
      console.error("Xero sync error:", error);
      res.status(error.response?.status || 500).json({
        success: false,
        message: "Failed to process Xero integration",
        error: error.message || "Unknown error occurred",
        details: error.response?.data || {},
      });
    }
  }
);

// GET /api/pay-runs/:id/report/pdf
router.get("/:id/report/pdf", ensureAuthenticated, async (req, res) => {

  try {
    // Validate company code
    if (!req.query.companyCode) {
      throw new Error('Company code is required');
    }

    // Find company
    const company = await Company.findOne({ companyCode: req.query.companyCode });
    if (!company) {
      throw new Error('Company not found');
    }

    // Find the pay run with populated data
    const payRun = await PayRun.findOne({
      _id: req.params.id,
      company: company._id
    }).populate({
      path: 'payslips',
      populate: [
        {
          path: 'employee',
          select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
        },
        {
          path: 'payrollPeriod',
          select: 'basicSalary grossPay totalDeductions netPay PAYE UIF SDL'
        }
      ]
    }).populate({
      path: 'payrollPeriods',
      populate: {
        path: 'employee',
        select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
      }
    });

    if (!payRun) {
      throw new Error('Pay run not found');
    }

    // Create a new PDF document
    const doc = new PDFDocument({ margin: 50, size: 'A4' });
    
    // Set response headers for PDF download
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename=payrun-${payRun._id}-${moment().format('YYYY-MM-DD')}.pdf`);
    
    // Pipe the PDF document to the response
    doc.pipe(res);

    // Add company logo if available (placeholder for now)
    // doc.image('path/to/logo.png', 50, 45, { width: 100 });

    // Add document title
    doc.fontSize(20).text(`${company.name} - Pay Run Report`, { align: 'center' });
    doc.moveDown();

    // Add pay run details
    doc.fontSize(12).text(`Pay Run Period: ${moment(payRun.startDate).format('DD MMM YYYY')} - ${moment(payRun.endDate).format('DD MMM YYYY')}`, { align: 'center' });
    doc.fontSize(12).text(`Pay Run Reference: ${payRun.reference || payRun._id}`, { align: 'center' });
    doc.fontSize(12).text(`Status: ${payRun.status}`, { align: 'center' });
    doc.moveDown(2);

    // Add summary section
    doc.fontSize(16).text('Summary', { underline: true });
    doc.moveDown();

    // Calculate totals
    let totals = {
      totalGrossPay: 0,
      totalNetPay: 0,
      totalPAYE: 0,
      totalSDL: 0,
      totalUIF: 0
    };

    if (payRun.payslips && payRun.payslips.length > 0) {
      // 🚨 SURGICAL FIX: Calculate from payslips with comprehensive calculations
      totals = payRun.payslips.reduce((acc, payslip) => {
        const basicSalary = payslip.basicSalary || 0;
        const customIncomeForUIF = calculateCustomIncomeTotal(payslip.customIncomeItems);
        const comprehensiveGrossIncome = basicSalary + customIncomeForUIF;
        const comprehensiveUIF = Math.min(comprehensiveGrossIncome * 0.01, 177.12);
        const comprehensiveTotalDeductions = (payslip.PAYE || 0) + comprehensiveUIF;
        const comprehensiveNetPay = comprehensiveGrossIncome - comprehensiveTotalDeductions;

        acc.totalGrossPay += comprehensiveGrossIncome;
        acc.totalNetPay += comprehensiveNetPay;
        acc.totalPAYE += payslip.PAYE || 0;
        acc.totalSDL += comprehensiveGrossIncome * 0.01;
        acc.totalUIF += comprehensiveUIF;
        return acc;
      }, totals);
    } else if (payRun.payrollPeriods && payRun.payrollPeriods.length > 0) {
      // 🚨 SURGICAL FIX: Calculate from payroll periods with comprehensive calculations
      totals = payRun.payrollPeriods.reduce((acc, period) => {
        const basicSalary = period.basicSalary || 0;
        const customIncomeForUIF = calculateCustomIncomeTotal(period.customIncomeItems);
        const comprehensiveGrossIncome = basicSalary + customIncomeForUIF;
        const comprehensiveUIF = Math.min(comprehensiveGrossIncome * 0.01, 177.12);
        const comprehensiveTotalDeductions = (period.PAYE || 0) + comprehensiveUIF;
        const comprehensiveNetPay = comprehensiveGrossIncome - comprehensiveTotalDeductions;

        acc.totalGrossPay += comprehensiveGrossIncome;
        acc.totalNetPay += comprehensiveNetPay;
        acc.totalPAYE += period.PAYE || 0;
        acc.totalSDL += comprehensiveGrossIncome * 0.01;
        acc.totalUIF += comprehensiveUIF;
        return acc;
      }, totals);
    }

    // Add summary table
    const summaryTable = {
      title: "Financial Summary",
      headers: ["Description", "Amount (R)"],
      rows: [
        ["Total Gross Pay", totals.totalGrossPay.toFixed(2)],
        ["Total PAYE", totals.totalPAYE.toFixed(2)],
        ["Total UIF", totals.totalUIF.toFixed(2)],
        ["Total SDL", totals.totalSDL.toFixed(2)],
        ["Total Net Pay", totals.totalNetPay.toFixed(2)]
      ]
    };

    await doc.table(summaryTable, { 
      prepareHeader: () => doc.font('Helvetica-Bold').fontSize(10),
      prepareRow: () => doc.font('Helvetica').fontSize(10)
    });
    
    doc.moveDown(2);

    // Add detailed section
    doc.fontSize(16).text('Employee Details', { underline: true });
    doc.moveDown();

    // Determine which data source to use
    const dataSource = payRun.payslips && payRun.payslips.length > 0 
      ? payRun.payslips 
      : payRun.payrollPeriods;

    if (dataSource && dataSource.length > 0) {
      // Prepare table data
      const tableData = {
        title: "Employee Payments",
        headers: ["Employee #", "Name", "Gross Pay (R)", "PAYE (R)", "UIF (R)", "SDL (R)", "Net Pay (R)"],
        rows: dataSource.map(item => {
          const employee = item.employee;
          // 🚨 SURGICAL FIX: Apply comprehensive wage component calculations like employeeProfile.ejs
          const basicSalary = item.basicSalary || 0;
          const customIncomeForUIF = calculateCustomIncomeTotal(item.customIncomeItems);
          const comprehensiveGrossIncome = basicSalary + customIncomeForUIF;
          const comprehensiveUIF = Math.min(comprehensiveGrossIncome * 0.01, 177.12);
          const comprehensiveTotalDeductions = (item.PAYE || 0) + comprehensiveUIF;
          const comprehensiveNetPay = comprehensiveGrossIncome - comprehensiveTotalDeductions;
          const comprehensiveSDL = comprehensiveGrossIncome * 0.01;

          return [
            employee.companyEmployeeNumber || 'N/A',
            `${employee.firstName} ${employee.lastName}`,
            comprehensiveGrossIncome.toFixed(2),
            (item.PAYE || 0).toFixed(2),
            comprehensiveUIF.toFixed(2),
            comprehensiveSDL.toFixed(2),
            comprehensiveNetPay.toFixed(2)
          ];
        })
      };

      await doc.table(tableData, { 
        prepareHeader: () => doc.font('Helvetica-Bold').fontSize(10),
        prepareRow: () => doc.font('Helvetica').fontSize(10)
      });
    } else {
      doc.text('No employee data available for this pay run.');
    }

    // Add footer
    doc.moveDown(2);
    doc.fontSize(10).text(`Generated on ${moment().format('DD MMM YYYY HH:mm:ss')}`, { align: 'center' });
    doc.fontSize(10).text(`Panda Software Solutions Group`, { align: 'center' });

    // Finalize the PDF
    doc.end();

  } catch (error) {
    console.error('\n=== Error in Generate PDF Report Route ===');
    console.error(error);
    
    res.status(500).send({
      success: false,
      message: 'Failed to generate PDF report',
      error: error.message
    });
  }
});

// GET /api/pay-runs/:id/report/excel
router.get("/:id/report/excel", ensureAuthenticated, async (req, res) => {

  try {
    // Validate company code
    if (!req.query.companyCode) {
      throw new Error('Company code is required');
    }

    // Find company
    const company = await Company.findOne({ companyCode: req.query.companyCode });
    if (!company) {
      throw new Error('Company not found');
    }

    // Find the pay run with populated data
    const payRun = await PayRun.findOne({
      _id: req.params.id,
      company: company._id
    }).populate({
      path: 'payslips',
      populate: [
        {
          path: 'employee',
          select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
        },
        {
          path: 'payrollPeriod',
          select: 'basicSalary grossPay totalDeductions netPay PAYE UIF SDL'
        }
      ]
    }).populate({
      path: 'payrollPeriods',
      populate: {
        path: 'employee',
        select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
      }
    });

    if (!payRun) {
      throw new Error('Pay run not found');
    }

    // Create a new Excel workbook
    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'Panda Software Solutions Group';
    workbook.lastModifiedBy = 'Panda Software Solutions Group';
    workbook.created = new Date();
    workbook.modified = new Date();

    // Add a summary worksheet
    const summarySheet = workbook.addWorksheet('Summary');
    
    // Add title and header information
    summarySheet.mergeCells('A1:G1');
    summarySheet.getCell('A1').value = `${company.name} - Pay Run Report`;
    summarySheet.getCell('A1').font = { size: 16, bold: true };
    summarySheet.getCell('A1').alignment = { horizontal: 'center' };
    
    summarySheet.mergeCells('A2:G2');
    summarySheet.getCell('A2').value = `Period: ${moment(payRun.startDate).format('DD MMM YYYY')} - ${moment(payRun.endDate).format('DD MMM YYYY')}`;
    summarySheet.getCell('A2').alignment = { horizontal: 'center' };
    
    summarySheet.mergeCells('A3:G3');
    summarySheet.getCell('A3').value = `Reference: ${payRun.reference || payRun._id}`;
    summarySheet.getCell('A3').alignment = { horizontal: 'center' };
    
    summarySheet.mergeCells('A4:G4');
    summarySheet.getCell('A4').value = `Status: ${payRun.status}`;
    summarySheet.getCell('A4').alignment = { horizontal: 'center' };

    // Calculate totals
    let totals = {
      totalGrossPay: 0,
      totalNetPay: 0,
      totalPAYE: 0,
      totalSDL: 0,
      totalUIF: 0
    };

    if (payRun.payslips && payRun.payslips.length > 0) {
      // 🚨 SURGICAL FIX: Calculate from payslips with comprehensive calculations
      totals = payRun.payslips.reduce((acc, payslip) => {
        const basicSalary = payslip.basicSalary || 0;
        const customIncomeForUIF = calculateCustomIncomeTotal(payslip.customIncomeItems);
        const comprehensiveGrossIncome = basicSalary + customIncomeForUIF;
        const comprehensiveUIF = Math.min(comprehensiveGrossIncome * 0.01, 177.12);
        const comprehensiveTotalDeductions = (payslip.PAYE || 0) + comprehensiveUIF;
        const comprehensiveNetPay = comprehensiveGrossIncome - comprehensiveTotalDeductions;

        acc.totalGrossPay += comprehensiveGrossIncome;
        acc.totalNetPay += comprehensiveNetPay;
        acc.totalPAYE += payslip.PAYE || 0;
        acc.totalSDL += comprehensiveGrossIncome * 0.01;
        acc.totalUIF += comprehensiveUIF;
        return acc;
      }, totals);
    } else if (payRun.payrollPeriods && payRun.payrollPeriods.length > 0) {
      // 🚨 SURGICAL FIX: Calculate from payroll periods with comprehensive calculations
      totals = payRun.payrollPeriods.reduce((acc, period) => {
        const basicSalary = period.basicSalary || 0;
        const customIncomeForUIF = calculateCustomIncomeTotal(period.customIncomeItems);
        const comprehensiveGrossIncome = basicSalary + customIncomeForUIF;
        const comprehensiveUIF = Math.min(comprehensiveGrossIncome * 0.01, 177.12);
        const comprehensiveTotalDeductions = (period.PAYE || 0) + comprehensiveUIF;
        const comprehensiveNetPay = comprehensiveGrossIncome - comprehensiveTotalDeductions;

        acc.totalGrossPay += comprehensiveGrossIncome;
        acc.totalNetPay += comprehensiveNetPay;
        acc.totalPAYE += period.PAYE || 0;
        acc.totalSDL += comprehensiveGrossIncome * 0.01;
        acc.totalUIF += comprehensiveUIF;
        return acc;
      }, totals);
    }

    // Add summary data
    summarySheet.addRow([]);
    summarySheet.addRow(['Financial Summary']);
    summarySheet.lastRow.font = { bold: true, size: 14 };
    
    summarySheet.addRow(['Description', 'Amount (R)']);
    summarySheet.lastRow.font = { bold: true };
    
    summarySheet.addRow(['Total Gross Pay', totals.totalGrossPay.toFixed(2)]);
    summarySheet.addRow(['Total PAYE', totals.totalPAYE.toFixed(2)]);
    summarySheet.addRow(['Total UIF', totals.totalUIF.toFixed(2)]);
    summarySheet.addRow(['Total SDL', totals.totalSDL.toFixed(2)]);
    summarySheet.addRow(['Total Net Pay', totals.totalNetPay.toFixed(2)]);
    
    // Format the currency cells
    for (let i = 4; i <= 8; i++) {
      summarySheet.getCell(`B${i}`).numFmt = '#,##0.00';
    }

    // Add a details worksheet
    const detailsSheet = workbook.addWorksheet('Employee Details');
    
    // Add headers
    detailsSheet.addRow(['Employee #', 'First Name', 'Last Name', 'Gross Pay (R)', 'PAYE (R)', 'UIF (R)', 'SDL (R)', 'Net Pay (R)']);
    detailsSheet.getRow(1).font = { bold: true };
    
    // Determine which data source to use
    const dataSource = payRun.payslips && payRun.payslips.length > 0 
      ? payRun.payslips 
      : payRun.payrollPeriods;

    if (dataSource && dataSource.length > 0) {
      // 🚨 SURGICAL FIX: Add employee data with comprehensive calculations
      dataSource.forEach(item => {
        const employee = item.employee;
        // Apply comprehensive wage component calculations like employeeProfile.ejs
        const basicSalary = item.basicSalary || 0;
        const customIncomeForUIF = calculateCustomIncomeTotal(item.customIncomeItems);
        const comprehensiveGrossIncome = basicSalary + customIncomeForUIF;
        const comprehensiveUIF = Math.min(comprehensiveGrossIncome * 0.01, 177.12);
        const comprehensiveTotalDeductions = (item.PAYE || 0) + comprehensiveUIF;
        const comprehensiveNetPay = comprehensiveGrossIncome - comprehensiveTotalDeductions;
        const comprehensiveSDL = comprehensiveGrossIncome * 0.01;

        detailsSheet.addRow([
          employee.companyEmployeeNumber || 'N/A',
          employee.firstName,
          employee.lastName,
          comprehensiveGrossIncome,
          item.PAYE || 0,
          comprehensiveUIF,
          comprehensiveSDL,
          comprehensiveNetPay
        ]);
      });
      
      // Format the currency columns
      for (let i = 2; i <= dataSource.length + 1; i++) {
        for (let j = 4; j <= 8; j++) {
          const cell = detailsSheet.getCell(i, j);
          cell.numFmt = '#,##0.00';
        }
      }
    } else {
      detailsSheet.addRow(['No employee data available for this pay run.']);
    }

    // Auto-size columns for better readability
    detailsSheet.columns.forEach(column => {
      let maxLength = 0;
      column.eachCell({ includeEmpty: true }, cell => {
        const columnLength = cell.value ? cell.value.toString().length : 10;
        if (columnLength > maxLength) {
          maxLength = columnLength;
        }
      });
      column.width = maxLength < 10 ? 10 : maxLength + 2;
    });

    // Set response headers for Excel download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename=payrun-${payRun._id}-${moment().format('YYYY-MM-DD')}.xlsx`);

    // Write to response
    await workbook.xlsx.write(res);

  } catch (error) {
    console.error('\n=== Error in Generate Excel Report Route ===');
    console.error(error);
    
    res.status(500).send({
      success: false,
      message: 'Failed to generate Excel report',
      error: error.message
    });
  }
});

// GET /api/pay-runs/:id/report/pdf - API endpoint for PDF reports
router.get("/api/pay-runs/:id/report/pdf", ensureAuthenticated, async (req, res) => {
  
  try {
    // Validate company code
    if (!req.query.companyCode) {
      throw new Error('Company code is required');
    }
    
    // Find company
    const company = await Company.findOne({ companyCode: req.query.companyCode });
    if (!company) {
      throw new Error('Company not found');
    }
    
    // Find pay run
    const payRun = await PayRun.findOne({
      _id: req.params.id,
      company: company._id
    }).populate({
      path: 'payslips',
      populate: [
        {
          path: 'employee',
          select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
        },
        {
          path: 'payrollPeriod',
          select: 'basicSalary grossPay totalDeductions netPay PAYE UIF SDL'
        }
      ]
    }).populate({
      path: 'payrollPeriods',
      populate: {
        path: 'employee',
        select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
      }
    });
    
    if (!payRun) {
      throw new Error('Pay run not found');
    }
    
    // Create a new PDF document
    const doc = new PDFDocument({ margin: 50 });
    
    // Set response headers for PDF download
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename=PayRun_${payRun._id}_Report.pdf`);
    
    // Pipe the PDF document to the response
    doc.pipe(res);
    
    // Add company header
    doc.fontSize(20).text(`${company.name} - Pay Run Report`, { align: 'center' });
    doc.moveDown();
    doc.fontSize(12).text(`Period: ${moment(payRun.startDate).format('DD MMM YYYY')} to ${moment(payRun.endDate).format('DD MMM YYYY')}`, { align: 'center' });
    doc.moveDown();
    
    // Add summary section
    doc.fontSize(16).text('Summary', { underline: true });
    doc.moveDown();
    
    // Determine data source based on what's available
    let dataSource = [];
    let totals = {
      totalGrossPay: 0,
      totalNetPay: 0,
      totalPAYE: 0,
      totalUIF: 0,
      totalSDL: 0
    };
    
    if (payRun.payslips && payRun.payslips.length > 0) {
      dataSource = payRun.payslips;
      // 🚨 SURGICAL FIX: Calculate totals from payslips with comprehensive calculations
      totals = payRun.payslips.reduce((acc, payslip) => {
        const basicSalary = payslip.basicSalary || 0;
        const customIncomeForUIF = calculateCustomIncomeTotal(payslip.customIncomeItems);
        const comprehensiveGrossIncome = basicSalary + customIncomeForUIF;
        const comprehensiveUIF = Math.min(comprehensiveGrossIncome * 0.01, 177.12);
        const comprehensiveTotalDeductions = (payslip.PAYE || 0) + comprehensiveUIF;
        const comprehensiveNetPay = comprehensiveGrossIncome - comprehensiveTotalDeductions;

        acc.totalGrossPay += comprehensiveGrossIncome;
        acc.totalNetPay += comprehensiveNetPay;
        acc.totalPAYE += payslip.PAYE || 0;
        acc.totalSDL += comprehensiveGrossIncome * 0.01;
        acc.totalUIF += comprehensiveUIF;
        return acc;
      }, totals);
    } else if (payRun.payrollPeriods && payRun.payrollPeriods.length > 0) {
      dataSource = payRun.payrollPeriods;
      // 🚨 SURGICAL FIX: Calculate totals from payroll periods with comprehensive calculations
      totals = payRun.payrollPeriods.reduce((acc, period) => {
        const basicSalary = period.basicSalary || 0;
        const customIncomeForUIF = calculateCustomIncomeTotal(period.customIncomeItems);
        const comprehensiveGrossIncome = basicSalary + customIncomeForUIF;
        const comprehensiveUIF = Math.min(comprehensiveGrossIncome * 0.01, 177.12);
        const comprehensiveTotalDeductions = (period.PAYE || 0) + comprehensiveUIF;
        const comprehensiveNetPay = comprehensiveGrossIncome - comprehensiveTotalDeductions;

        acc.totalGrossPay += comprehensiveGrossIncome;
        acc.totalNetPay += comprehensiveNetPay;
        acc.totalPAYE += period.PAYE || 0;
        acc.totalSDL += comprehensiveGrossIncome * 0.01;
        acc.totalUIF += comprehensiveUIF;
        return acc;
      }, totals);
    }
    
    // Add summary table
    const summaryTable = {
      headers: ['Description', 'Amount'],
      rows: [
        ['Total Employees', dataSource.length],
        ['Total Gross Pay', `R ${totals.totalGrossPay.toFixed(2)}`],
        ['Total Net Pay', `R ${totals.totalNetPay.toFixed(2)}`],
        ['Total PAYE', `R ${totals.totalPAYE.toFixed(2)}`],
        ['Total UIF', `R ${totals.totalUIF.toFixed(2)}`],
        ['Total SDL', `R ${totals.totalSDL.toFixed(2)}`]
      ]
    };
    
    doc.table(summaryTable, {
      prepareHeader: () => doc.font('Helvetica-Bold').fontSize(10),
      prepareRow: () => doc.font('Helvetica').fontSize(10)
    });
    
    doc.moveDown(2);
    
    // Add detailed section
    doc.fontSize(16).text('Employee Details', { underline: true });
    doc.moveDown();
    
    if (dataSource && dataSource.length > 0) {
      // Add employee data
      const employeeTable = {
        headers: ['Employee #', 'Name', 'Gross Pay', 'Deductions', 'Net Pay'],
        rows: dataSource.map(item => {
          const employee = item.employee;
          // 🚨 SURGICAL FIX: Apply comprehensive wage component calculations like employeeProfile.ejs
          const basicSalary = item.basicSalary || 0;
          const customIncomeForUIF = calculateCustomIncomeTotal(item.customIncomeItems);
          const comprehensiveGrossIncome = basicSalary + customIncomeForUIF;
          const comprehensiveUIF = Math.min(comprehensiveGrossIncome * 0.01, 177.12);
          const comprehensiveTotalDeductions = (item.PAYE || 0) + comprehensiveUIF;
          const comprehensiveNetPay = comprehensiveGrossIncome - comprehensiveTotalDeductions;

          return [
            employee.companyEmployeeNumber || 'N/A',
            `${employee.firstName} ${employee.lastName}`,
            `R ${comprehensiveGrossIncome.toFixed(2)}`,
            `R ${comprehensiveTotalDeductions.toFixed(2)}`,
            `R ${comprehensiveNetPay.toFixed(2)}`
          ];
        })
      };
      
      doc.table(employeeTable, {
        prepareHeader: () => doc.font('Helvetica-Bold').fontSize(10),
        prepareRow: () => doc.font('Helvetica').fontSize(10)
      });
    } else {
      doc.text('No employee data available for this pay run.');
    }
    
    // Finalize the PDF
    doc.end();
    
  } catch (error) {
    console.error('\n=== Error in Generate PDF Report Route ===');
    console.error(error);
    
    res.status(500).send({
      success: false,
      message: error.message || 'Error generating PDF report'
    });
  }
});

// GET /api/pay-runs/:id/report/excel - API endpoint for Excel reports
router.get("/api/pay-runs/:id/report/excel", ensureAuthenticated, async (req, res) => {
  
  try {
    // Validate company code
    if (!req.query.companyCode) {
      throw new Error('Company code is required');
    }
    
    // Find company
    const company = await Company.findOne({ companyCode: req.query.companyCode });
    if (!company) {
      throw new Error('Company not found');
    }
    
    // Find pay run
    const payRun = await PayRun.findOne({
      _id: req.params.id,
      company: company._id
    }).populate({
      path: 'payslips',
      populate: [
        {
          path: 'employee',
          select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
        },
        {
          path: 'payrollPeriod',
          select: 'basicSalary grossPay totalDeductions netPay PAYE UIF SDL'
        }
      ]
    }).populate({
      path: 'payrollPeriods',
      populate: {
        path: 'employee',
        select: 'firstName lastName companyEmployeeNumber paymentMethod bankDetails'
      }
    });
    
    if (!payRun) {
      throw new Error('Pay run not found');
    }
    
    // Create a new workbook
    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'Panda Software Solutions Group';
    workbook.lastModifiedBy = 'Panda Software Solutions Group';
    workbook.created = new Date();
    workbook.modified = new Date();
    
    // Add a summary worksheet
    const summarySheet = workbook.addWorksheet('Summary');
    
    // Add title
    summarySheet.mergeCells('A1:E1');
    const titleCell = summarySheet.getCell('A1');
    titleCell.value = `${company.name} - Pay Run Report`;
    titleCell.font = { size: 16, bold: true };
    titleCell.alignment = { horizontal: 'center' };
    
    // Add period
    summarySheet.mergeCells('A2:E2');
    const periodCell = summarySheet.getCell('A2');
    periodCell.value = `Period: ${moment(payRun.startDate).format('DD MMM YYYY')} to ${moment(payRun.endDate).format('DD MMM YYYY')}`;
    periodCell.font = { size: 12 };
    periodCell.alignment = { horizontal: 'center' };
    
    // Add empty row
    summarySheet.addRow([]);
    
    // Determine data source based on what's available
    let dataSource = [];
    let totals = {
      totalGrossPay: 0,
      totalNetPay: 0,
      totalPAYE: 0,
      totalUIF: 0,
      totalSDL: 0
    };
    
    if (payRun.payslips && payRun.payslips.length > 0) {
      dataSource = payRun.payslips;
      // 🚨 SURGICAL FIX: Calculate totals from payslips with comprehensive calculations
      totals = payRun.payslips.reduce((acc, payslip) => {
        const basicSalary = payslip.basicSalary || 0;
        const customIncomeForUIF = calculateCustomIncomeTotal(payslip.customIncomeItems);
        const comprehensiveGrossIncome = basicSalary + customIncomeForUIF;
        const comprehensiveUIF = Math.min(comprehensiveGrossIncome * 0.01, 177.12);
        const comprehensiveTotalDeductions = (payslip.PAYE || 0) + comprehensiveUIF;
        const comprehensiveNetPay = comprehensiveGrossIncome - comprehensiveTotalDeductions;

        acc.totalGrossPay += comprehensiveGrossIncome;
        acc.totalNetPay += comprehensiveNetPay;
        acc.totalPAYE += payslip.PAYE || 0;
        acc.totalSDL += comprehensiveGrossIncome * 0.01;
        acc.totalUIF += comprehensiveUIF;
        return acc;
      }, totals);
    } else if (payRun.payrollPeriods && payRun.payrollPeriods.length > 0) {
      dataSource = payRun.payrollPeriods;
      // 🚨 SURGICAL FIX: Calculate totals from payroll periods with comprehensive calculations
      totals = payRun.payrollPeriods.reduce((acc, period) => {
        const basicSalary = period.basicSalary || 0;
        const customIncomeForUIF = calculateCustomIncomeTotal(period.customIncomeItems);
        const comprehensiveGrossIncome = basicSalary + customIncomeForUIF;
        const comprehensiveUIF = Math.min(comprehensiveGrossIncome * 0.01, 177.12);
        const comprehensiveTotalDeductions = (period.PAYE || 0) + comprehensiveUIF;
        const comprehensiveNetPay = comprehensiveGrossIncome - comprehensiveTotalDeductions;

        acc.totalGrossPay += comprehensiveGrossIncome;
        acc.totalNetPay += comprehensiveNetPay;
        acc.totalPAYE += period.PAYE || 0;
        acc.totalSDL += comprehensiveGrossIncome * 0.01;
        acc.totalUIF += comprehensiveUIF;
        return acc;
      }, totals);
    }
    
    // Add summary data
    summarySheet.addRow(['Summary Information']);
    summarySheet.addRow(['Description', 'Amount']);
    summarySheet.addRow(['Total Employees', dataSource.length]);
    summarySheet.addRow(['Total Gross Pay', `R ${totals.totalGrossPay.toFixed(2)}`]);
    summarySheet.addRow(['Total Net Pay', `R ${totals.totalNetPay.toFixed(2)}`]);
    summarySheet.addRow(['Total PAYE', `R ${totals.totalPAYE.toFixed(2)}`]);
    summarySheet.addRow(['Total UIF', `R ${totals.totalUIF.toFixed(2)}`]);
    summarySheet.addRow(['Total SDL', `R ${totals.totalSDL.toFixed(2)}`]);
    
    // Format summary table
    const summaryHeaderRow = summarySheet.getRow(5);
    summaryHeaderRow.font = { bold: true };
    summarySheet.getColumn(1).width = 20;
    summarySheet.getColumn(2).width = 15;
    
    // Add a details worksheet
    const detailsSheet = workbook.addWorksheet('Employee Details');
    
    // Add headers
    detailsSheet.addRow(['Employee #', 'Name', 'Gross Pay', 'Deductions', 'Net Pay', 'Payment Method']);
    const headerRow = detailsSheet.getRow(1);
    headerRow.font = { bold: true };
    
    // Add employee data
    if (dataSource && dataSource.length > 0) {
      // 🚨 SURGICAL FIX: Add employee data with comprehensive calculations
      dataSource.forEach(item => {
        const employee = item.employee;
        // Apply comprehensive wage component calculations like employeeProfile.ejs
        const basicSalary = item.basicSalary || 0;
        const customIncomeForUIF = calculateCustomIncomeTotal(item.customIncomeItems);
        const comprehensiveGrossIncome = basicSalary + customIncomeForUIF;
        const comprehensiveUIF = Math.min(comprehensiveGrossIncome * 0.01, 177.12);
        const comprehensiveTotalDeductions = (item.PAYE || 0) + comprehensiveUIF;
        const comprehensiveNetPay = comprehensiveGrossIncome - comprehensiveTotalDeductions;

        detailsSheet.addRow([
          employee.companyEmployeeNumber || 'N/A',
          `${employee.firstName} ${employee.lastName}`,
          `R ${comprehensiveGrossIncome.toFixed(2)}`,
          `R ${comprehensiveTotalDeductions.toFixed(2)}`,
          `R ${comprehensiveNetPay.toFixed(2)}`,
          employee.paymentMethod || 'EFT'
        ]);
      });
    } else {
      detailsSheet.addRow(['No employee data available for this pay run.']);
    }
    
    // Format details table
    detailsSheet.getColumn(1).width = 15;
    detailsSheet.getColumn(2).width = 25;
    detailsSheet.getColumn(3).width = 15;
    detailsSheet.getColumn(4).width = 15;
    detailsSheet.getColumn(5).width = 15;
    detailsSheet.getColumn(6).width = 20;
    
    // Set response headers for Excel download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename=PayRun_${payRun._id}_Report.xlsx`);
    
    // Write to response
    await workbook.xlsx.write(res);
    
  } catch (error) {
    console.error('\n=== Error in Generate Excel Report Route ===');
    console.error(error);
    
    res.status(500).send({
      success: false,
      message: error.message || 'Error generating Excel report'
    });
  }
});

// GET /:companyCode/payruns/:id/download - Client-facing download route
router.get("/:companyCode/payruns/:id/download", [ensureAuthenticated, csrfProtection], async (req, res) => {
  
  try {
    // Get the company code - try URL param first, then session, then user's current company
    let companyCode = req.params.companyCode;
    
    // If companyCode is undefined in URL, try to get it from session or user's current company
    if (!companyCode || companyCode === 'undefined') {
      
      // Try to get from session
      if (req.session && req.session.companyCode) {
        companyCode = req.session.companyCode;
      } 
      // If still no company code, try to get from user's current company
      else if (req.user && req.user.currentCompany) {
        // Find the company by ID to get its code
        const userCompany = await Company.findById(req.user.currentCompany);
        if (userCompany && userCompany.companyCode) {
          companyCode = userCompany.companyCode;
        }
      }
    }
    
    if (!companyCode || companyCode === 'undefined') {
      return res.status(404).render('error', { 
        message: 'Company code not found',
        error: { status: 404, stack: '' }
      });
    }
    
    // Determine format (default to PDF if not specified)
    const format = req.query.format || 'pdf';
    
    // Generate CSRF token for the API request
    const csrfToken = req.csrfToken();
    
    // Redirect to the appropriate report endpoint
    const redirectUrl = `/api/pay-runs/${req.params.id}/report/${format}?_csrf=${csrfToken}&companyCode=${companyCode}`;
    
    return res.redirect(redirectUrl);
    
  } catch (error) {
    console.error('\n=== Error in Pay Run Download Route ===');
    console.error(error);
    
    return res.status(500).render('error', {
      message: 'Error generating report',
      error: { status: 500, stack: process.env.NODE_ENV === 'development' ? error.stack : '' }
    });
  }
});

// POST /payrun/create - Create a new pay run
router.post("/create", ensureAuthenticated, async (req, res) => {
  try {
    const { payslipIds, payRunType = 'regular', description } = req.body;

    // Validate payslips
    const payslips = await Payslip.find({
      _id: { $in: payslipIds },
      company: req.user.currentCompany,
      isFinalized: true,
      payRun: { $exists: false },
    }).populate("employee");

    if (payslips.length === 0) {
      return res.status(400).json({
        success: false,
        message: "No valid payslips found for pay run creation",
      });
    }

    // Group payslips by period AND frequency to create separate pay runs
    // This ensures different frequencies for the same period get separate pay runs
    const payslipsByPeriodAndFrequency = payslips.reduce((acc, payslip) => {
      // Get frequency from employee's payFrequency or fallback to 'monthly'
      let frequency = 'monthly';
      if (payslip.employee && payslip.employee.payFrequency) {
        if (typeof payslip.employee.payFrequency === 'object' && payslip.employee.payFrequency.frequency) {
          frequency = payslip.employee.payFrequency.frequency;
        } else if (typeof payslip.employee.payFrequency === 'string') {
          frequency = payslip.employee.payFrequency;
        }
      } else if (payslip.frequency) {
        frequency = payslip.frequency;
      }

      // Create unique key combining period and frequency
      const groupKey = `${payslip.period}_${frequency}`;

      if (!acc[groupKey]) {
        acc[groupKey] = {
          period: payslip.period,
          frequency: frequency,
          payslips: []
        };
      }
      acc[groupKey].payslips.push(payslip);
      return acc;
    }, {});

    console.log(`📋 Grouped payslips into ${Object.keys(payslipsByPeriodAndFrequency).length} separate pay runs by period+frequency:`,
      Object.keys(payslipsByPeriodAndFrequency).map(key => {
        const group = payslipsByPeriodAndFrequency[key];
        return `${group.period} (${group.frequency}): ${group.payslips.length} payslips`;
      }));

    const results = [];

    // Create pay runs for each period+frequency combination
    for (const [groupKey, group] of Object.entries(payslipsByPeriodAndFrequency)) {
      const { period, frequency, payslips: periodPayslips } = group;
      // Find existing pay runs for this period, frequency and type to determine sequence
      const existingPayRuns = await PayRun.find({
        company: req.user.currentCompany,
        period: period,
        frequency: frequency,
        payRunType: payRunType
      }).sort({ sequence: -1 });

      // Calculate next sequence number
      const nextSequence = existingPayRuns.length > 0 ? existingPayRuns[0].sequence + 1 : 1;

      const totalPayable = periodPayslips.reduce((sum, p) => sum + p.netPay, 0);
      const totalAmount = periodPayslips.reduce((sum, p) => sum + p.grossPay, 0);

      // Extract dates from the first payslip for this group
      const firstPayslip = periodPayslips[0];
      const startDate = firstPayslip.startDate || new Date(period + '-01');
      const endDate = firstPayslip.endDate || new Date(period + '-31');

      const newPayRun = new PayRun({
        company: req.user.currentCompany,
        period,
        frequency, // CRITICAL: Set the frequency for this pay run
        startDate,
        endDate,
        paymentDate: endDate,
        taxPeriod: period,
        monthYear: period,
        payRunType,
        description: description || `${frequency.charAt(0).toUpperCase() + frequency.slice(1)} Pay Run - ${period}`,
        sequence: nextSequence,
        payslips: periodPayslips.map((p) => p._id),
        totalPayable,
        totalAmount,
        status: "created",
        createdBy: req.user._id,
        createdAt: new Date(),
      });

      await newPayRun.save();

      // Update payslips with pay run reference
      await Payslip.updateMany(
        { _id: { $in: periodPayslips.map((p) => p._id) } },
        { $set: { payRun: newPayRun._id } }
      );

      console.log(`✅ Created ${frequency} pay run for period ${period}: ${periodPayslips.length} payslips`);

      results.push({
        period,
        frequency,
        payRunId: newPayRun._id,
        totalPayable,
        totalAmount,
        payslipCount: periodPayslips.length,
        sequence: nextSequence,
        payRunType
      });
    }

    res.json({
      success: true,
      message: "Pay run(s) created successfully",
      results,
    });
  } catch (error) {
    console.error("Error creating pay run:", error);
    res.status(500).json({
      success: false,
      message: error.message || "Failed to create pay run",
    });
  }
});

module.exports = router;
