const express = require("express");
const router = express.Router();
const moment = require("moment-timezone");
const Employee = require("../models/Employee");
const Payroll = require("../models/Payroll");
const PayFrequency = require("../models/PayFrequency");
const payrollCalculations = require("../utils/payrollCalculations");
const { v4: uuidv4 } = require("uuid");
const User = require("../models/user");
const EmployeeNumberSettings = require("../models/employeeNumberSettings");
const Company = require("../models/Company");
const checkCompany = require("../middleware/checkCompany");
const {
  ensureAuthenticated,
  ensureOwner,
} = require("../config/ensureAuthenticated");
const { ensureManagementAccess } = require("../middleware/auth"); // 🔒 NEW: Import management access middleware
const PayrollPeriod = require("../models/PayrollPeriod");
const beneficiaries = require("../models/beneficiaries");
const csrf = require("csurf");
const csrfProtection = csrf({ cookie: true });

// Import our new department access middleware
const { filterEmployeesByDepartment, applyDepartmentFilter } = require("../middleware/departmentAccess");
const RFIConfig = require("../models/rfiConfig");
const mongoose = require("mongoose");
const fs = require("fs");
const dateUtils = require("../utils/dateUtils");
const transporter = require("../config/emailConfig");
const crypto = require("crypto");
const EmployeeCredential = require("../models/employeeCredential");
const ejs = require("ejs");
const path = require("path");
const PayrollService = require("../services/PayrollService");
const RFICalculationService = require("../services/rfiCalculationService");
const EmployeeNumberService = require("../services/employeeNumberService");
const SelfServiceTokenService = require("../services/selfServiceTokenService");
const Role = require("../models/role");
const bcrypt = require("bcrypt");
const EmployeeTemplateGenerator = require("../utils/excel-templates");
const XLSX = require("xlsx");
const PayComponent = require("../models/payComponent");
const AuditLog = require("../models/auditLog");
const PensionFundBeneficiary = require("../models/pensionFundBeneficiary");
const Beneficiary = require("../models/beneficiaries");
const WorkedHours = require("../models/WorkedHours");
const { calculatePeriodTotals } = require("../services/PayrollService");
const ChronologicalValidator = require("../utils/ChronologicalValidator");
const PeriodCalculations = require("../utils/periodCalculations");
const BusinessDate = require("../utils/BusinessDate");
const {
  formatDateForDisplay,
  isProratedMonth,
  getEndOfMonthDate,
  getNextMonthEndDate,
  formatDateForMongoDB,
  isLastDayOfMonth,
  getCurrentOrNextLastDayOfMonth,
  getSundaysInPeriod,
  getCurrentPayPeriod,
} = dateUtils;
const { isValidObjectId } = mongoose;
const ExcelJS = require("exceljs");
const multer = require("multer");
const xlsx = require("xlsx");
const HourlyRate = require("../models/HourlyRate");
const Busboy = require("busboy");
const LeaveType = require("../models/LeaveType");
const LeaveBalance = require("../models/LeaveBalance");
const { getHolidaysForRange } = require("../utils/holidayCalculator");
const timezoneUtils = require("../utils/timezoneUtils");
const payrollPeriodEngine = require("../utils/payrollPeriodEngine");

// Define default timezone for the application
const DEFAULT_TIMEZONE = "Africa/Johannesburg"; // South African timezone

// Validation function for employee essentials
async function validateEmployeeEssentials(data, rowNumber) {
  const errors = [];
  const sanitizedData = {};

  try {
    // Validate and sanitize firstName
    if (data.firstName !== undefined) {
      const firstName = String(data.firstName || '').trim();
      if (firstName.length > 0) {
        if (firstName.length > 50) {
          errors.push(`Row ${rowNumber}: First name cannot exceed 50 characters`);
        } else if (!/^[a-zA-Z\s'-]+$/.test(firstName)) {
          errors.push(`Row ${rowNumber}: First name contains invalid characters`);
        } else {
          sanitizedData.firstName = firstName;
        }
      }
    }

    // Validate and sanitize lastName
    if (data.lastName !== undefined) {
      const lastName = String(data.lastName || '').trim();
      if (lastName.length > 0) {
        if (lastName.length > 50) {
          errors.push(`Row ${rowNumber}: Last name cannot exceed 50 characters`);
        } else if (!/^[a-zA-Z\s'-]+$/.test(lastName)) {
          errors.push(`Row ${rowNumber}: Last name contains invalid characters`);
        } else {
          sanitizedData.lastName = lastName;
        }
      }
    }

    // Validate and sanitize employeeNumber
    if (data.employeeNumber !== undefined) {
      const employeeNumber = String(data.employeeNumber || '').trim();
      if (employeeNumber.length > 0) {
        if (employeeNumber.length > 20) {
          errors.push(`Row ${rowNumber}: Employee number cannot exceed 20 characters`);
        } else {
          sanitizedData.companyEmployeeNumber = employeeNumber;
        }
      }
    }

    // Validate Date of Birth
    if (data.dob !== undefined && data.dob !== '') {
      const dob = moment(data.dob);
      if (!dob.isValid()) {
        errors.push(`Row ${rowNumber}: Invalid date of birth format`);
      } else {
        const age = moment().diff(dob, 'years');
        if (age < 16) {
          errors.push(`Row ${rowNumber}: Employee must be at least 16 years old`);
        } else if (age > 100) {
          errors.push(`Row ${rowNumber}: Invalid date of birth - age cannot exceed 100 years`);
        } else {
          sanitizedData.dob = dob.toDate();
        }
      }
    }

    // Validate Date of Appointment
    if (data.doa !== undefined && data.doa !== '') {
      const doa = moment(data.doa);
      if (!doa.isValid()) {
        errors.push(`Row ${rowNumber}: Invalid date of appointment format`);
      } else if (doa.isAfter(moment())) {
        errors.push(`Row ${rowNumber}: Date of appointment cannot be in the future`);
      } else {
        sanitizedData.doa = doa.toDate();
      }
    }

    // Validate ID Type and ID Number
    if (data.idType !== undefined) {
      const idType = String(data.idType || '').trim();
      if (['None', 'rsa', 'passport'].includes(idType)) {
        sanitizedData.idType = idType;

        // Validate ID Number for RSA ID
        if (idType === 'rsa' && data.idNumber !== undefined) {
          const idNumber = String(data.idNumber || '').trim();
          if (idNumber.length > 0) {
            if (!validateSouthAfricanID(idNumber)) {
              errors.push(`Row ${rowNumber}: Invalid South African ID number format`);
            } else {
              sanitizedData.idNumber = idNumber;
            }
          }
        } else if (data.idNumber !== undefined) {
          const idNumber = String(data.idNumber || '').trim();
          if (idNumber.length > 0 && idNumber.length <= 20) {
            sanitizedData.idNumber = idNumber;
          } else if (idNumber.length > 20) {
            errors.push(`Row ${rowNumber}: ID number cannot exceed 20 characters`);
          }
        }
      } else if (idType.length > 0) {
        errors.push(`Row ${rowNumber}: Invalid ID type. Must be 'None', 'rsa', or 'passport'`);
      }
    }

    // Validate Passport Number
    if (data.passportNumber !== undefined) {
      const passportNumber = String(data.passportNumber || '').trim();
      if (passportNumber.length > 0) {
        if (passportNumber.length > 20) {
          errors.push(`Row ${rowNumber}: Passport number cannot exceed 20 characters`);
        } else if (!/^[A-Z0-9]+$/.test(passportNumber)) {
          errors.push(`Row ${rowNumber}: Passport number can only contain uppercase letters and numbers`);
        } else {
          sanitizedData.passportNumber = passportNumber;
        }
      }
    }

    // Note: passportCountryCode field doesn't exist in Employee model, so we skip this validation

    // Validate Email
    if (data.email !== undefined) {
      const email = String(data.email || '').trim().toLowerCase();
      if (email.length > 0) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
          errors.push(`Row ${rowNumber}: Invalid email format`);
        } else if (email.length > 100) {
          errors.push(`Row ${rowNumber}: Email cannot exceed 100 characters`);
        } else {
          sanitizedData.email = email;
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedData
    };

  } catch (error) {
    console.error(`Validation error for row ${rowNumber}:`, error);
    return {
      isValid: false,
      errors: [`Row ${rowNumber}: Validation error - ${error.message}`],
      sanitizedData: {}
    };
  }
}

// South African ID validation function
function validateSouthAfricanID(idNumber) {
  // Remove any spaces or dashes
  const cleanId = idNumber.replace(/[\s-]/g, '');

  // Check if it's exactly 13 digits
  if (!/^\d{13}$/.test(cleanId)) {
    return false;
  }

  // Extract date components
  const year = parseInt(cleanId.substring(0, 2));
  const month = parseInt(cleanId.substring(2, 4));
  const day = parseInt(cleanId.substring(4, 6));

  // Determine century (00-21 = 2000s, 22-99 = 1900s)
  const fullYear = year <= 21 ? 2000 + year : 1900 + year;

  // Validate date
  const date = new Date(fullYear, month - 1, day);
  if (date.getFullYear() !== fullYear || date.getMonth() !== month - 1 || date.getDate() !== day) {
    return false;
  }

  // Validate checksum using Luhn algorithm
  let sum = 0;
  for (let i = 0; i < 12; i++) {
    let digit = parseInt(cleanId.charAt(i));
    if (i % 2 === 1) {
      digit *= 2;
      if (digit > 9) {
        digit = Math.floor(digit / 10) + (digit % 10);
      }
    }
    sum += digit;
  }

  const checkDigit = (10 - (sum % 10)) % 10;
  return checkDigit === parseInt(cleanId.charAt(12));
}

/**
 * Calculate tax year-aware annual salary using cumulative salary averaging
 * @param {string} employeeId - Employee ID
 * @param {Date|string} currentPeriodDate - Current period date
 * @param {string} frequency - Pay frequency (monthly, weekly, biweekly)
 * @returns {Promise<number>} - Annualized salary based on tax year average
 */
async function calculateTaxYearAnnualSalary(employeeId, currentPeriodDate, frequency = 'monthly') {
  try {
    // 1. Determine current tax year (March 1 to February 28)
    const currentDate = moment(currentPeriodDate);
    const currentYear = currentDate.year();

    // South African tax year: if current month is March or later, tax year starts this year
    // If current month is Jan/Feb, tax year started previous year
    const taxYearStart = currentDate.month() >= 2 ? // March is month 2 (0-based)
      moment(`${currentYear}-03-01`) :
      moment(`${currentYear - 1}-03-01`);

    const taxYearEnd = taxYearStart.clone().add(1, 'year').subtract(1, 'day'); // Feb 28/29

    console.log('=== TAX YEAR CALCULATION DEBUG (employeeManagement) ===');
    console.log('Current period date:', currentDate.format('YYYY-MM-DD'));
    console.log('Tax year start:', taxYearStart.format('YYYY-MM-DD'));
    console.log('Tax year end:', taxYearEnd.format('YYYY-MM-DD'));

    // 2. Get ALL periods within this tax year (both finalized and unfinalized)
    const periodsInTaxYear = await PayrollPeriod.find({
      employee: employeeId,
      endDate: {
        $gte: taxYearStart.toDate(),
        $lte: taxYearEnd.toDate()
      }
    }).sort({ endDate: 1 });

    console.log('Periods found in tax year:', periodsInTaxYear.length);

    if (periodsInTaxYear.length === 0) {
      console.log('No periods found, returning 0');
      return 0;
    }

    // 3. Sum all basic salaries and count periods
    let totalSalaries = 0;
    let periodCount = 0;

    periodsInTaxYear.forEach(period => {
      if (period.basicSalary && period.basicSalary > 0) {
        totalSalaries += period.basicSalary;
        periodCount++;
        console.log(`Period ${moment(period.endDate).format('YYYY-MM-DD')}: R${period.basicSalary}`);
      }
    });

    console.log('Total salaries sum:', totalSalaries);
    console.log('Period count:', periodCount);

    if (periodCount === 0) {
      console.log('No valid salary periods found, returning 0');
      return 0;
    }

    // 4. Calculate average salary
    const averageSalary = totalSalaries / periodCount;
    console.log('Average salary per period:', averageSalary);

    // 5. Multiply by correct frequency multiplier
    const frequencyMultiplier = frequency === 'weekly' ? 52 :
                               frequency === 'biweekly' || frequency === 'bi-weekly' ? 26 : 12;

    const annualSalary = averageSalary * frequencyMultiplier;

    console.log('Frequency multiplier:', frequencyMultiplier);
    console.log('Final annual salary:', annualSalary);
    console.log('=== END TAX YEAR CALCULATION (employeeManagement) ===');

    return annualSalary;

  } catch (error) {
    console.error('Error calculating tax year annual salary:', error);
    // Fallback to 0 if calculation fails
    return 0;
  }
}

/**
 * Calculate YTD PAYE for WEEKLY employees using SARS-compliant cumulative method
 * @param {string} employeeId - Employee ID
 * @param {Date|string} currentPeriodDate - Current period date
 * @param {number} employeeAge - Employee age for tax calculation
 * @returns {Promise<Object>} - YTD PAYE calculation details
 */
async function calculateYTDPAYEForWeekly(employeeId, currentPeriodDate, employeeAge = 35) {
  const frequency = 'weekly';
  try {
    console.log('=== YTD PAYE CALCULATION DEBUG (WEEKLY) ===');
    console.log('Input currentPeriodDate:', currentPeriodDate);
    console.log('Formatted currentPeriodDate:', moment(currentPeriodDate).format('YYYY-MM-DD'));

    // 1. Get tax year boundaries
    const currentDate = moment(currentPeriodDate);
    const currentYear = currentDate.year();

    const taxYearStart = currentDate.month() >= 2 ?
      moment(`${currentYear}-03-01`) :
      moment(`${currentYear - 1}-03-01`);

    const taxYearEnd = taxYearStart.clone().add(1, 'year').subtract(1, 'day');

    console.log('Tax year:', taxYearStart.format('YYYY-MM-DD'), 'to', taxYearEnd.format('YYYY-MM-DD'));

    // 2. Get all periods in tax year (finalized and current)
    const periodsInTaxYear = await PayrollPeriod.find({
      employee: employeeId,
      endDate: {
        $gte: taxYearStart.toDate(),
        $lte: taxYearEnd.toDate()
      }
    }).sort({ endDate: 1 });

    console.log('Total periods in tax year:', periodsInTaxYear.length);

    if (periodsInTaxYear.length === 0) {
      console.log('No periods found in tax year, returning zero values');
      return { ytdTax: 0, taxAlreadyPaid: 0, currentPAYE: 0, periodNumber: 0, annualizedIncome: 0 };
    }

    // Calculate frequency multiplier early (needed for special cases)
    const frequencyMultiplier = 52;

    // Special case: If we only have one period and it's not finalized,
    // this is likely the first period being created - use traditional calculation
    if (periodsInTaxYear.length === 1 && !periodsInTaxYear[0].isFinalized) {
      console.log('⚠️ Single unfinalized period detected - using traditional calculation for first period');
      const basicSalary = periodsInTaxYear[0].basicSalary;
      const traditionalAnnualSalary = basicSalary * frequencyMultiplier;

      const traditionalTaxResult = await payrollCalculations.calculateEnhancedPAYE({
        annualSalary: traditionalAnnualSalary,
        frequency: frequency,
        age: employeeAge,
        periodEndDate: currentPeriodDate
      });

      const traditionalPAYE = traditionalTaxResult.annualPAYE / frequencyMultiplier;

      console.log('Traditional calculation for first period:', {
        basicSalary,
        annualSalary: traditionalAnnualSalary,
        weeklyPAYE: traditionalPAYE
      });

      return {
        ytdIncome: basicSalary,
        annualizedIncome: traditionalAnnualSalary,
        annualTax: traditionalTaxResult.annualPAYE,
        ytdTax: traditionalPAYE,
        taxAlreadyPaid: 0,
        currentPAYE: traditionalPAYE,
        periodNumber: 1,
        frequencyMultiplier
      };
    }

    // 3. Calculate YTD income and period number
    let ytdIncome = 0;
    let currentPeriodNumber = 0;
    let taxAlreadyPaid = 0;

    periodsInTaxYear.forEach((period) => {
      if (period.basicSalary && period.basicSalary > 0) {
        // FIXED: Use actual prorated amount for YTD income calculation
        const proratedPercentage = period.proratedPercentage || 100;
        const actualProratedAmount = (period.basicSalary * proratedPercentage) / 100;

        ytdIncome += actualProratedAmount;
        currentPeriodNumber++;

        console.log(`Period ${moment(period.endDate).format('YYYY-MM-DD')}: Full R${period.basicSalary}, Prorated ${proratedPercentage}% = R${actualProratedAmount}, Finalized: ${period.isFinalized}, PAYE: ${period.PAYE}`);
        console.log(`  - Start: ${moment(period.startDate).format('YYYY-MM-DD')}, End: ${moment(period.endDate).format('YYYY-MM-DD')}`);

        // Sum PAYE from previous finalized periods (exclude current period)
        const periodEndDate = moment(period.endDate);
        const currentEndDate = moment(currentPeriodDate);

        // WEEKLY-SPECIFIC: Use day comparison for weekly periods
        const isBeforeCurrentPeriod = periodEndDate.isBefore(currentEndDate, 'day');

        if (period.isFinalized && period.PAYE && isBeforeCurrentPeriod) {
          // For weekly employees, use actual PAYE (no proration adjustment needed)
          taxAlreadyPaid += period.PAYE;
          console.log(`✓ Adding tax from finalized period ${periodEndDate.format('YYYY-MM-DD')}: R${period.PAYE}`);
        } else if (period.isFinalized && period.PAYE) {
          console.log(`✗ Skipping current period ${periodEndDate.format('YYYY-MM-DD')}: R${period.PAYE} (same as current)`);
        } else if (!period.isFinalized) {
          console.log(`✗ Skipping unfinalized period ${periodEndDate.format('YYYY-MM-DD')}`);
        } else if (!period.PAYE) {
          console.log(`✗ Skipping period with no PAYE ${periodEndDate.format('YYYY-MM-DD')}`);
        }
      }
    });

    console.log('YTD Income:', ytdIncome);
    console.log('Current period number:', currentPeriodNumber);
    console.log('Tax already paid:', taxAlreadyPaid);

    // 4. Calculate fractional periods and annualized income (SARS-compliant method)
    let fractionalPeriods = 0;

    periodsInTaxYear.forEach((period) => {
      if (period.basicSalary && period.basicSalary > 0) {
        const proratedPercentage = period.proratedPercentage || 100;
        const fractionalPeriod = proratedPercentage / 100;
        fractionalPeriods += fractionalPeriod;
        console.log(`Period ${moment(period.endDate).format('YYYY-MM-DD')}: ${fractionalPeriod.toFixed(4)} fractional periods`);
      }
    });

    // FIXED: Use fractional periods for annualization (SARS method)
    const annualizedIncome = (ytdIncome / fractionalPeriods) * frequencyMultiplier;

    console.log('Total fractional periods:', fractionalPeriods.toFixed(4));
    console.log('YTD Income:', ytdIncome);
    console.log('Annualized income (SARS method):', annualizedIncome);

    // 5. Calculate annual tax on annualized income
    const annualTaxResult = await payrollCalculations.calculateEnhancedPAYE({
      annualSalary: annualizedIncome,
      frequency: frequency,
      age: employeeAge,
      periodEndDate: currentPeriodDate
    });

    // Extract annual tax - use the correct property name from calculateEnhancedPAYE
    const annualTax = annualTaxResult.annualPAYE || annualTaxResult.annualTax || (annualTaxResult.weeklyPAYE * 52) || 0;

    console.log('Annual tax calculation result:', annualTaxResult);
    console.log('Extracted annual tax:', annualTax);

    // 6. Calculate YTD tax using fractional periods (SARS-compliant method)
    const ytdTax = (annualTax * fractionalPeriods) / frequencyMultiplier;

    console.log('YTD tax calculation:');
    console.log(`  Annual tax: R${annualTax}`);
    console.log(`  Fractional periods: ${fractionalPeriods.toFixed(4)}`);
    console.log(`  Frequency multiplier: ${frequencyMultiplier}`);
    console.log(`  YTD tax should be: R${ytdTax}`);

    // 7. Calculate current period PAYE
    let currentPAYE = Math.max(0, ytdTax - taxAlreadyPaid);

    console.log('Current period PAYE:', currentPAYE);
    console.log('=== END YTD PAYE CALCULATION (WEEKLY) ===');

    return {
      ytdIncome,
      annualizedIncome,
      annualTax: annualTax,
      ytdTax,
      taxAlreadyPaid,
      currentPAYE,
      periodNumber: currentPeriodNumber,
      frequencyMultiplier
    };

  } catch (error) {
    console.error('Error calculating YTD PAYE (weekly):', error);
    return { ytdTax: 0, taxAlreadyPaid: 0, currentPAYE: 0, periodNumber: 0 };
  }
}

/**
 * Calculate YTD PAYE for MONTHLY employees using SARS-compliant cumulative method
 * @param {string} employeeId - Employee ID
 * @param {Date|string} currentPeriodDate - Current period date
 * @param {number} employeeAge - Employee age for tax calculation
 * @returns {Promise<Object>} - YTD PAYE calculation details
 */
async function calculateYTDPAYEForMonthly(employeeId, currentPeriodDate, employeeAge = 35) {
  const frequency = 'monthly';
  try {
    console.log('=== YTD PAYE CALCULATION DEBUG (MONTHLY) ===');
    console.log('Input currentPeriodDate:', currentPeriodDate);
    console.log('Formatted currentPeriodDate:', moment(currentPeriodDate).format('YYYY-MM-DD'));

    // 1. Get tax year boundaries
    const currentDate = moment(currentPeriodDate);
    const currentYear = currentDate.year();

    const taxYearStart = currentDate.month() >= 2 ?
      moment(`${currentYear}-03-01`) :
      moment(`${currentYear - 1}-03-01`);

    const taxYearEnd = taxYearStart.clone().add(1, 'year').subtract(1, 'day');

    console.log('Tax year:', taxYearStart.format('YYYY-MM-DD'), 'to', taxYearEnd.format('YYYY-MM-DD'));

    // 2. Get all periods in tax year (finalized and current)
    const periodsInTaxYear = await PayrollPeriod.find({
      employee: employeeId,
      endDate: {
        $gte: taxYearStart.toDate(),
        $lte: taxYearEnd.toDate()
      }
    }).sort({ endDate: 1 });

    console.log('Total periods in tax year:', periodsInTaxYear.length);

    if (periodsInTaxYear.length === 0) {
      console.log('No periods found in tax year, returning zero values');
      return { ytdTax: 0, taxAlreadyPaid: 0, currentPAYE: 0, periodNumber: 0, annualizedIncome: 0 };
    }

    // Calculate frequency multiplier early (needed for special cases)
    const frequencyMultiplier = 12;

    // Special case: If we only have one period and it's not finalized,
    // this is likely the first period being created - use traditional calculation
    if (periodsInTaxYear.length === 1 && !periodsInTaxYear[0].isFinalized) {
      console.log('⚠️ Single unfinalized period detected - using traditional calculation for first period');
      const basicSalary = periodsInTaxYear[0].basicSalary;
      const traditionalAnnualSalary = basicSalary * frequencyMultiplier;

      const traditionalTaxResult = await payrollCalculations.calculateEnhancedPAYE({
        annualSalary: traditionalAnnualSalary,
        frequency: frequency,
        age: employeeAge,
        periodEndDate: currentPeriodDate
      });

      const traditionalPAYE = traditionalTaxResult.annualPAYE / frequencyMultiplier;

      console.log('Traditional calculation for first period:', {
        basicSalary,
        annualSalary: traditionalAnnualSalary,
        monthlyPAYE: traditionalPAYE
      });

      return {
        ytdIncome: basicSalary,
        annualizedIncome: traditionalAnnualSalary,
        annualTax: traditionalTaxResult.annualPAYE,
        ytdTax: traditionalPAYE,
        taxAlreadyPaid: 0,
        currentPAYE: traditionalPAYE,
        periodNumber: 1,
        frequencyMultiplier
      };
    }

    // 3. Calculate YTD income and period number
    let ytdIncome = 0;
    let currentPeriodNumber = 0;
    let taxAlreadyPaid = 0;

    periodsInTaxYear.forEach((period) => {
      if (period.basicSalary && period.basicSalary > 0) {
        // FIXED: Use actual prorated amount for YTD income calculation
        const proratedPercentage = period.proratedPercentage || 100;
        const actualProratedAmount = (period.basicSalary * proratedPercentage) / 100;

        ytdIncome += actualProratedAmount;
        currentPeriodNumber++;

        console.log(`Period ${moment(period.endDate).format('YYYY-MM-DD')}: Salary R${period.basicSalary}, Finalized: ${period.isFinalized}, PAYE: ${period.PAYE}`);
        console.log(`  - Start: ${moment(period.startDate).format('YYYY-MM-DD')}, End: ${moment(period.endDate).format('YYYY-MM-DD')}`);

        // Sum PAYE from previous finalized periods (exclude current period)
        const periodEndDate = moment(period.endDate);
        const currentEndDate = moment(currentPeriodDate);

        // MONTHLY-SPECIFIC: Use month comparison for monthly periods
        const isBeforeCurrentPeriod = periodEndDate.isBefore(currentEndDate, 'month');

        if (period.isFinalized && period.PAYE && isBeforeCurrentPeriod) {
          // FIXED: For YTD calculations, use actual PAYE paid (not full-period equivalent)
          // The YTD method should use actual amounts paid, not theoretical full amounts
          let taxToAdd = period.PAYE;

          // Check if this period was prorated
          const periodWasProrated = period.proratedPercentage && period.proratedPercentage < 100;

          if (periodWasProrated) {
            // FIXED: Use actual PAYE paid, not full-period equivalent
            console.log(`📊 Period was prorated (${period.proratedPercentage.toFixed(2)}%): Using actual PAYE paid R${period.PAYE}`);
            taxToAdd = period.PAYE; // Use actual amount paid
          }

          taxAlreadyPaid += taxToAdd;
          console.log(`✓ Adding tax from finalized period ${periodEndDate.format('YYYY-MM-DD')}: R${taxToAdd}`);
        } else if (period.isFinalized && period.PAYE) {
          console.log(`✗ Skipping current period ${periodEndDate.format('YYYY-MM-DD')}: R${period.PAYE} (same as current)`);
        } else if (!period.isFinalized) {
          console.log(`✗ Skipping unfinalized period ${periodEndDate.format('YYYY-MM-DD')}`);
        } else if (!period.PAYE) {
          console.log(`✗ Skipping period with no PAYE ${periodEndDate.format('YYYY-MM-DD')}`);
        }
      }
    });

    console.log('YTD Income:', ytdIncome);
    console.log('Current period number:', currentPeriodNumber);
    console.log('Tax already paid:', taxAlreadyPaid);

    // 4. Calculate fractional periods and annualized income (SARS-compliant method)
    let fractionalPeriods = 0;

    periodsInTaxYear.forEach((period) => {
      if (period.basicSalary && period.basicSalary > 0) {
        const proratedPercentage = period.proratedPercentage || 100;
        const fractionalPeriod = proratedPercentage / 100;
        fractionalPeriods += fractionalPeriod;
        console.log(`Period ${moment(period.endDate).format('YYYY-MM-DD')}: ${fractionalPeriod.toFixed(4)} fractional periods`);
      }
    });

    // FIXED: Use fractional periods for annualization (SARS method)
    const annualizedIncome = (ytdIncome / fractionalPeriods) * frequencyMultiplier;

    console.log('Total fractional periods:', fractionalPeriods.toFixed(4));
    console.log('YTD Income:', ytdIncome);
    console.log('Annualized income (SARS method):', annualizedIncome);

    // 5. Calculate annual tax on annualized income
    const annualTaxResult = await payrollCalculations.calculateEnhancedPAYE({
      annualSalary: annualizedIncome,
      frequency: frequency,
      age: employeeAge,
      periodEndDate: currentPeriodDate
    });

    // Extract annual tax - use the correct property name from calculateEnhancedPAYE
    const annualTax = annualTaxResult.annualPAYE || annualTaxResult.annualTax || (annualTaxResult.monthlyPAYE * 12) || 0;

    console.log('Annual tax calculation result:', annualTaxResult);
    console.log('Extracted annual tax:', annualTax);

    // 6. Calculate YTD tax using fractional periods (SARS-compliant method)
    const ytdTax = (annualTax * fractionalPeriods) / frequencyMultiplier;

    console.log('YTD tax calculation:');
    console.log(`  Annual tax: R${annualTax}`);
    console.log(`  Fractional periods: ${fractionalPeriods.toFixed(4)}`);
    console.log(`  Frequency multiplier: ${frequencyMultiplier}`);
    console.log(`  YTD tax should be: R${ytdTax}`);

    // 7. Calculate current period PAYE
    let currentPAYE = Math.max(0, ytdTax - taxAlreadyPaid);

    console.log('Current period PAYE:', currentPAYE);
    console.log('=== END YTD PAYE CALCULATION (MONTHLY) ===');

    return {
      ytdIncome,
      annualizedIncome,
      annualTax: annualTax,
      ytdTax,
      taxAlreadyPaid,
      currentPAYE,
      periodNumber: currentPeriodNumber,
      frequencyMultiplier
    };

  } catch (error) {
    console.error('Error calculating YTD PAYE (monthly):', error);
    return { ytdTax: 0, taxAlreadyPaid: 0, currentPAYE: 0, periodNumber: 0 };
  }
}

/**
 * Router function for YTD PAYE calculations - dispatches to frequency-specific functions
 * @param {string} employeeId - Employee ID
 * @param {Date|string} currentPeriodDate - Current period date
 * @param {string} frequency - Pay frequency
 * @param {number} employeeAge - Employee age for tax calculation
 * @returns {Promise<Object>} - YTD PAYE calculation details
 */
async function calculateYTDPAYEForRoute(employeeId, currentPeriodDate, frequency = 'monthly', employeeAge = 35) {
  console.log(`🎯 Routing YTD calculation to ${frequency}-specific function`);

  try {
    switch(frequency) {
      case 'weekly':
        return await calculateYTDPAYEForWeekly(employeeId, currentPeriodDate, employeeAge);
      case 'monthly':
        return await calculateYTDPAYEForMonthly(employeeId, currentPeriodDate, employeeAge);
      case 'biweekly':
      case 'bi-weekly':
        console.log('⚠️ Bi-weekly calculation not fully implemented, using monthly logic');
        return await calculateYTDPAYEForMonthly(employeeId, currentPeriodDate, employeeAge);
      default:
        console.warn(`⚠️ Unknown frequency: ${frequency}, defaulting to monthly`);
        return await calculateYTDPAYEForMonthly(employeeId, currentPeriodDate, employeeAge);
    }
  } catch (error) {
    console.error('Error in YTD PAYE router:', error);
    return { ytdTax: 0, taxAlreadyPaid: 0, currentPAYE: 0, periodNumber: 0 };
  }
}

// SIMPLE FIX: Remove complex business rule engine - use only database periods
function findPeriodByDate(databasePeriods, targetDate) {
  const targetDateString = moment.utc(targetDate).format('YYYY-MM-DD');

  // Find in database periods only
  const dbPeriod = databasePeriods.find(period => {
    if (!period.endDate) return false;
    return moment.utc(period.endDate).format('YYYY-MM-DD') === targetDateString;
  });

  if (dbPeriod) {
    return dbPeriod;
  }

  return null;
}

/**
 * Calculate employee-specific first payroll period end date based on DOA and pay frequency
 * @param {moment} employeeDOA - Employee's date of appointment
 * @param {Object} payFrequency - Pay frequency object with frequency and lastDayOfPeriod
 * @returns {moment} - Calculated first period end date
 */
function calculateEmployeeSpecificPeriodEndDate(employeeDOA, payFrequency) {
  const doa = employeeDOA.clone();

  switch (payFrequency.frequency.toLowerCase()) {
    case "monthly":
      // SURGICAL FIX: Use firstPayrollPeriodEndDate unless DOA is after it
      if (payFrequency.firstPayrollPeriodEndDate) {
        const configuredFirstPeriodEnd = moment(payFrequency.firstPayrollPeriodEndDate);
        const shouldUseDOA = doa.isAfter(configuredFirstPeriodEnd);

        if (!shouldUseDOA) {
          // Use configured first period end date
          return configuredFirstPeriodEnd;
        }
      }

      // Continue with original DOA-based logic
      if (payFrequency.lastDayOfPeriod === "monthend") {
        // For month-end pay frequency, first period ends at the end of DOA month
        return doa.clone().endOf("month");
      } else {
        // For specific day of month (e.g., 15th)
        const targetDay = parseInt(payFrequency.lastDayOfPeriod);
        let periodEnd = doa.clone().date(targetDay).endOf("day");

        // If DOA is after the target day, move to next month's target day
        if (doa.date() > targetDay) {
          periodEnd = doa.clone().add(1, "month").date(targetDay).endOf("day");
        }

        return periodEnd;
      }

    case "weekly":
      // Map day names to numbers (0 = Sunday, 6 = Saturday)
      const daysOfWeek = {
        sunday: 0, monday: 1, tuesday: 2, wednesday: 3,
        thursday: 4, friday: 5, saturday: 6
      };

      const targetDay = daysOfWeek[payFrequency.lastDayOfPeriod.toLowerCase()];
      const currentDay = doa.day();

      // Calculate days to add to reach the next target day
      let daysToAdd = (targetDay - currentDay + 7) % 7;

      // If we're already on the target day, use DOA as the end date
      if (daysToAdd === 0) {
        return doa.clone().endOf("day");
      } else {
        // Go to the next occurrence of the target day
        return doa.clone().add(daysToAdd, "days").endOf("day");
      }

    case "bi-weekly":
    case "biweekly":
      // For bi-weekly, first period is 14 days from DOA
      return doa.clone().add(13, "days").endOf("day");

    default:
      // Default to end of month for unknown frequencies
      return doa.clone().endOf("month");
  }
}

// Import the upload router
const uploadRouter = require("./upload");

// Configure multer with detailed logging
const storage = multer.memoryStorage();

// Initialize multer with unified configuration
const uploadMiddleware = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
    files: 1,
    fields: 10,
    fieldSize: 10 * 1024 * 1024 // 10MB for field size
  },
  fileFilter: function (req, file, cb) {

    // Accept any Excel-like MIME type to be more permissive
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'application/octet-stream',
      'application/binary'
    ];

    const isValidMimeType = allowedTypes.includes(file.mimetype) ||
                           file.mimetype.includes('excel') ||
                           file.mimetype.includes('spreadsheet');

    if (!isValidMimeType) {
      console.error('Invalid mime type:', file.mimetype);
      return cb(new Error('Invalid file type. Only Excel files (.xlsx, .xls) are allowed.'));
    }

    const validExtension = /\.(xlsx|xls)$/i.test(file.originalname);
    if (!validExtension) {
      console.error('Invalid file extension:', file.originalname);
      return cb(new Error('Invalid file extension. Only .xlsx and .xls files are allowed.'));
    }

    cb(null, true);
  }
});

// Configure busboy options
const busboyOptions = {
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
    fields: 5, // Allow up to 5 non-file fields
    files: 1, // Allow only 1 file
    parts: 6, // fields + files + potential metadata
  },
  preservePath: false,
};

// Add debug middleware
router.use((req, res, next) => {
  next();
});

// Add debug middleware for request tracking
router.use((req, res, next) => {
  next();
});

// Debug logging middleware
router.use((req, res, next) => {
  const requestId = uuidv4();
  req.requestId = requestId;
  const startTime = Date.now();

  // Log request details
  if (req.body && Object.keys(req.body).length > 0) {
    const sanitizedBody = { ...req.body };
    // Remove sensitive data
    delete sanitizedBody.password;
    delete sanitizedBody.token;
  }

  // Track response
  res.on("finish", () => {
    const duration = Date.now() - startTime;
  });

  // Track errors
  res.on("error", (error) => {
    console.error(`[${requestId}] ❌ Response error:`, error);
  });

  next();
});

// Global error handler middleware
const handleError = (error, req, res, next) => {
  const errorId = uuidv4();
  console.error(
    `[${req.requestId || "NO_REQ_ID"}] [ErrorID: ${errorId}] ❌ Error:`,
    {
      message: error.message,
      stack: error.stack,
      code: error.code,
      path: req.path,
      method: req.method,
      params: req.params,
      query: req.query,
      user: req.user ? { id: req.user.id, email: req.user.email } : null,
    }
  );

  // Send appropriate error response
  res.status(error.status || 500).json({
    error: error.message,
    errorId,
    requestId: req.requestId,
  });
};

// Database operation logging middleware
const logDatabaseOperation = (operation) => async (req, res, next) => {
  next();
};

// Transaction logging middleware
const logTransaction = (operation) => async (req, res, next) => {
  next();
};

// Add this near the top of the file with other middleware imports
const checkAuth = (req, res, next) => {
  if (!req.isAuthenticated()) {
    req.flash("error", "Please log in to access this page");
    return res.redirect("/login");
  }
  next();
};

function isNewEmployee(doa, currentMonth) {
  const doaMoment = moment(doa, "YYYY-MM-DD");
  const currentMoment = moment(currentMonth, "YYYY-MM-DD");

  if (doaMoment.isValid() && currentMoment.isValid()) {
    return doaMoment.isSame(currentMoment, "month");
  }

  console.error("Invalid date format:", { doa, currentMonth });
  return false;
}

// Add age calculation utility function
const calculateAge = (dob) => {
  if (!dob) return null;
  const birthDate = new Date(dob);
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  if (
    monthDiff < 0 ||
    (monthDiff === 0 && today.getDate() < birthDate.getDate())
  ) {
    age--;
  }
  return age;
};

// Add this middleware to check if the user is authenticated
router.use(ensureAuthenticated);

// Apply the middleware to all routes in this file
router.use(ensureAuthenticated);
router.use(checkCompany);

// Add this route before other routes that might conflict
router.get(
  "/:companyCode/employeeManagement/api/holidays",
  ensureAuthenticated,
  async (req, res) => {
    try {
      // Validate query parameters
      const startYear = parseInt(req.query.startYear) || new Date().getFullYear();
      const endYear = parseInt(req.query.endYear) || startYear;

      // Validate year range
      if (startYear > endYear) {
        return res.status(400).json({
          error: "Invalid date range",
          details: "Start year cannot be greater than end year"
        });
      }

      // Validate reasonable year range (e.g., not too far in the past or future)
      const currentYear = new Date().getFullYear();
      if (startYear < currentYear - 1 || endYear > currentYear + 2) {
        return res.status(400).json({
          error: "Invalid year range",
          details: "Years must be within a reasonable range"
        });
      }

      // Get holidays using the utility function
      const holidays = getHolidaysForRange(startYear, endYear);

      // No cache headers - always fetch fresh data
      res.set({
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      });

      res.json(holidays);
    } catch (error) {
      console.error("Error fetching public holidays:", error);
      res.status(500).json({
        error: "Failed to fetch public holidays",
        details: error.message,
      });
    }
  }
);

// Leave Overview Route
router.get(
  "/:companyCode/employeeManagement/leaveOverview",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error_msg", "Company not found");
        return res.redirect("/dashboard");
      }

      // Get the authenticated user
      const user = await User.findById(req.user._id);

      res.render("leaveOverview", {
        user,
        company,
        page: "leaveOverview",
      });
    } catch (error) {
      console.error("Error in leave overview route:", error);
      req.flash(
        "error_msg",
        "An error occurred while loading the leave overview"
      );
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// Employee Leave Route
router.get(
  "/:companyCode/employeeManagement/employee/:employeeId/leave",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const companyCode = req.params.companyCode;
      const employeeId = req.params.employeeId;

      // Get company details
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error_msg", "Company not found");
        return res.redirect("/dashboard");
      }

      // Get employee details (populate payFrequency for consistency with employeeProfile)
      const employee = await Employee.findById(employeeId).populate("payFrequency");
      if (!employee) {
        req.flash("error_msg", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Get leave types with full details
      const leaveTypes = await LeaveType.find({
        company: company._id,
        active: true,
        $or: [{ gender: "all" }, { gender: employee.gender }],
      });

      // Get leave balances with accrual calculations
      const leaveBalances = await LeaveBalance.find({
        employee: employeeId,
        year: new Date().getFullYear(),
      }).populate("leaveType");

      // Get company leave year settings for proper accrual calculation
      const leaveYearStart = company?.leaveYearStart || 'january';

      // Calculate actual available balance considering accrual
      const leaveTypesWithBalances = leaveTypes.map((type) => {
        const balance = leaveBalances.find(
          (b) =>
            b.leaveType && b.leaveType._id.toString() === type._id.toString()
        );

        let availableBalance = 0;
        let monthlyAmount = 0;

        // Check for employee-specific allocation
        const specificAllocation = type.employeeAllocations?.find(
          (a) => a.employee.toString() === employeeId
        );

        const totalAllocation = specificAllocation
          ? specificAllocation.daysPerYear
          : type.daysPerYear;

        if (balance) {
          if (type.accrualRate === "yearly") {
            // Recalculate remaining balance to ensure accuracy
            availableBalance = balance.calculateRemaining();
          } else if (type.accrualRate === "monthly") {
            monthlyAmount = Number((totalAllocation / 12).toFixed(2));
            // CRITICAL FIX: Use employee.doa instead of employee.startDate and include leaveYearStart
            if (employee.doa) {
              availableBalance = type.calculateAccruedDays(
                employee.doa,
                new Date(),
                leaveYearStart
              );
            } else {
              console.warn(`Employee ${employee.firstName} ${employee.lastName} has no DOA (Date of Appointment) - defaulting to 0 balance`);
              availableBalance = 0;
            }
          }
        } else {
          // If no balance record exists, create default values
          if (type.accrualRate === "yearly") {
            availableBalance = totalAllocation;
          } else if (type.accrualRate === "monthly") {
            // CRITICAL FIX: Use employee.doa instead of employee.startDate and include leaveYearStart
            if (employee.doa) {
              availableBalance = type.calculateAccruedDays(
                employee.doa,
                new Date(),
                leaveYearStart
              );
            } else {
              console.warn(`Employee ${employee.firstName} ${employee.lastName} has no DOA (Date of Appointment) - defaulting to 0 balance`);
              availableBalance = 0;
            }
          }
        }

        return {
          _id: type._id,
          name: type.name,
          icon: type.icon || "ph-calendar",
          balance: availableBalance,
          total: totalAllocation,
          accrualRate: type.accrualRate,
          monthlyAmount,
          minDaysNotice: type.minDaysNotice,
          requiresDocument: type.requiresDocument,
          documentRequiredAfterDays: type.documentRequiredAfterDays,
          gender: type.gender,
        };
      });

      // Calculate date of birth formatting (consistent with employeeProfile)
      const dobFormatted = employee.dob
        ? moment.tz(employee.dob, DEFAULT_TIMEZONE).format("DD MMMM YYYY")
        : null;

      // Calculate pay frequency display (consistent with employeeProfile)
      const payFrequency = employee.payFrequency?.name || "N/A";

      // Render the leave page
      res.render("employeeLeave", {
        company,
        employee,
        user: req.user,
        leaveTypes: leaveTypesWithBalances,
        dobFormatted,
        payFrequency,
        title: "Employee Leave Management",
      });
    } catch (error) {
      console.error("Error in employee leave route:", error);
      req.flash("error_msg", "Error loading employee leave page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// Make sure this route comes after the holidays API route
router.get(
  "/:companyCode/employeeManagement",
  ensureAuthenticated,
  filterEmployeesByDepartment, // 🔒 NEW: Add department filtering middleware
  async (req, res) => {

    try {
      // Add cache-busting headers to ensure fresh data
      res.set({
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      });

      // Assuming the user has a currentCompany field
      const company = await Company.findById(req.user.currentCompany);

      if (!company) {
        return res.status(404).send("Company not found");
      }

      // 🔒 NEW: Build employee query with department filtering
      let employeeQuery = { company: company._id };

      // Apply department filter if admin has restrictions
      if (req.departmentFilter) {
        employeeQuery = applyDepartmentFilter(employeeQuery, req.departmentFilter);
        console.log(`🔍 Filtering employees by departments: ${req.departmentFilter.join(', ')}`);
      }

      // Fetch employees with department filtering applied
      const employees = await Employee.find(employeeQuery)
        .select("companyEmployeeNumber firstName lastName costCentre status dob doa payFrequency department") // 🔒 NEW: Include department field
        .populate("payFrequency") // Populate the payFrequency reference
        .lean(); // Use lean() for better performance and to ensure fresh data

      // 🔒 NEW: Add debug info for testing
      console.log(`📊 Found ${employees.length} employees for user ${req.user.email} (role: ${req.user.roleName})`);
      if (req.departmentFilter) {
        console.log(`🏢 Departments shown: ${[...new Set(employees.map(e => e.department).filter(Boolean))].join(', ')}`);
      }

      res.render("employeeManagement", {
        companyCode: company.companyCode,
        company: company,
        user: req.user,
        employees: employees,
        moment: moment, // Add this line
        departmentFilter: req.departmentFilter, // 🔒 NEW: Pass filter info to view
      });
    } catch (error) {
      console.error("Error rendering employee management page:", error);
      res
        .status(500)
        .send("An error occurred while loading the employee management page");
    }
  }
);

// Keep the existing route for /:companyIdentifier/employeeManagement
router.get(
  "/:companyIdentifier/employeeManagement",
  ensureAuthenticated,
  ensureManagementAccess, // 🔒 FIXED: Allow admin access
  async (req, res) => {

    try {
      // Enhanced cache-busting headers to ensure fresh data
      const timestamp = Date.now();
      res.set({
        'Cache-Control': 'no-cache, no-store, must-revalidate, proxy-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Surrogate-Control': 'no-store',
        'Last-Modified': new Date().toUTCString(),
        'ETag': `"${timestamp}"`,
        'Vary': 'Accept-Encoding, User-Agent'
      });

      const companyIdentifier = req.params.companyIdentifier;
      const refreshParam = req.query.refresh;
      let company;

      if (mongoose.Types.ObjectId.isValid(companyIdentifier)) {
        company = await Company.findById(companyIdentifier);
      } else {
        company = await Company.findOne({ companyCode: companyIdentifier });
      }

      if (!company) {
        return res.status(404).send("Company not found");
      }

      // If this is a refresh after an operation, add a small delay to ensure DB consistency
      if (refreshParam) {
        await new Promise(resolve => setTimeout(resolve, 100));
        console.log(`Employee management refreshed at ${new Date().toISOString()} with param: ${refreshParam}`);
      }

      // Force fresh data by using a new connection and explicit read preference
      const employees = await Employee.find({ company: company._id })
        .select("companyEmployeeNumber firstName lastName costCentre status dob doa")
        .sort({ companyEmployeeNumber: 1, lastName: 1, firstName: 1 }) // Consistent ordering
        .lean()
        .read('primary'); // Force read from primary to avoid replica lag

      console.log(`Employee management loaded: ${employees.length} employees found for company ${company.companyCode}`);

      res.render("employeeManagement", {
        companyCode: company.companyCode,
        company: company,
        user: req.user,
        employees: employees,
        moment: moment,
        refreshTimestamp: timestamp,
        isRefresh: !!refreshParam
      });
    } catch (error) {
      console.error("Error rendering employee management page:", error);
      res
        .status(500)
        .send("An error occurred while loading the employee management page");
    }
  }
);

router.get(
  "/:companyCode/employeeProfile/:employeeId",
  ensureAuthenticated,
  async (req, res) => {
    try {
      console.log("=== EMPLOYEE PROFILE ROUTE CALLED ===", req.params);
      const { companyCode, employeeId } = req.params;
      const { selectedMonth, nextPeriod, viewFinalized } = req.query;

      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).send("Company not found");
      }

      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      }).populate("payFrequency");

      if (!employee) {
        return res.status(404).send("Employee not found");
      }

      // Ensure we have enough future periods (with surgical firstPayrollPeriodEndDate logic)
      await PayrollService.ensureFuturePeriodsExist(employee, new Date());

      // Clean up old future periods periodically
      const lastCleanup = req.session.lastPeriodCleanup || 0;
      if (Date.now() - lastCleanup > 24 * 60 * 60 * 1000) {
        // Once per day
        await PayrollService.cleanupFuturePeriods(employee);
        req.session.lastPeriodCleanup = Date.now();
      }

      const periodStartDate = new Date();
      const payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
      }).sort({ month: -1 });

      // 🚨🚨🚨 VERIFICATION TEST MARKER - CORRECT ROUTE HANDLER FOUND! 🚨🚨🚨

      // Initialize basicSalary - will be updated with period-specific value later
      const defaultBasicSalary = 0;
      let basicSalary = Number(payroll?.basicSalary) || defaultBasicSalary;

      const payFrequency = employee.payFrequency;

      // SIMPLE FIX: Remove complex period generation - use only database periods

      let payPeriods = await PayrollPeriod.find({
        employee: employeeId,
        company: company._id,
      }).sort({ startDate: 1 });

      if (payPeriods.length === 0) {
        const payFrequency = await PayFrequency.findById(employee.payFrequency);

        // SURGICAL FIX: Determine correct start date based on firstPayrollPeriodEndDate vs DOA
        let employeeStartDate = moment.tz(employee.doa, DEFAULT_TIMEZONE).startOf("day");

        if (payFrequency && payFrequency.firstPayrollPeriodEndDate && payFrequency.frequency === 'monthly') {
          const configuredFirstPeriodEnd = moment.tz(payFrequency.firstPayrollPeriodEndDate, DEFAULT_TIMEZONE);
          const shouldUseDOA = employeeStartDate.isAfter(configuredFirstPeriodEnd);

          if (!shouldUseDOA) {
            // Use configured first period end date, start from beginning of that month
            employeeStartDate = configuredFirstPeriodEnd.clone().startOf('month').startOf('day');
            console.log("🔄 Using configured first period date for period generation:", {
              doa: employee.doa,
              configuredDate: payFrequency.firstPayrollPeriodEndDate,
              adjustedStartDate: employeeStartDate.format('YYYY-MM-DD')
            });
          }
        }

        const currentEndDate = moment
          .tz(new Date(), DEFAULT_TIMEZONE)
          .endOf("day");

        payPeriods = await PayrollService.generatePayPeriods(
          employee,
          employeeStartDate.toDate(),
          currentEndDate.toDate()
        );
      }

      // Inside the GET /:companyCode/employeeProfile/:employeeId route
      const payPeriodsWithDetails = await Promise.all(
        payPeriods.map(async (period) => {
          // Use BusinessDate for timezone-independent pro-rata calculations
          const periodStartBusiness = period.startDateBusiness || BusinessDate.fromDate(period.startDate);
          const periodEndBusiness = period.endDateBusiness || BusinessDate.fromDate(period.endDate);

          // SURGICAL FIX: For weekly employees, ensure correct 7-day period calculation
          let finalPeriodStartBusiness = periodStartBusiness;
          let finalPeriodEndBusiness = periodEndBusiness;

          if (employee.payFrequency.frequency === 'weekly') {
            // For weekly periods, always calculate proper 7-day period
            // Period end is correct, calculate start as exactly 7 days before (6 days back)
            const endDate = BusinessDate.normalize(periodEndBusiness);
            finalPeriodStartBusiness = BusinessDate.addDays(endDate, -6);
          }

          const proratedResult = BusinessDate.calculateProratedSalary(
            employee.doa,
            basicSalary,
            employee,
            finalPeriodStartBusiness,
            finalPeriodEndBusiness
          );

          // Ensure all required fields are set when creating new periods
          const newPeriod = {
            employee: employee._id,
            startDate: period.startDate,
            endDate: period.endDate,
            frequency: employee.payFrequency.frequency,
            company: req.user.currentCompany,
          };

          // Check if period already exists
          const existingPeriod = await PayrollPeriod.findOne({
            employee: employee._id,
            startDate: period.startDate,
            endDate: period.endDate,
          });

          if (!existingPeriod) {
            await PayrollPeriod.create(newPeriod);
          }

          const periodPAYE = await payrollCalculations.calculatePAYE(
            proratedResult.annualSalary,
            Number(proratedResult.proratedPercentage),
            employee.payFrequency.frequency,
            period.endDate
          );

          // 🚨 CRITICAL FIX: Preserve original basicSalary from database
          const originalBasicSalary = Number(period.basicSalary || 0);


          return {
            ...period.toObject(),
            // Preserve the original historical basicSalary from PayrollPeriod database record
            basicSalary: originalBasicSalary,
            // Store current salary separately for calculations if needed
            currentPayrollBasicSalary: Number(basicSalary),
            proratedSalary: Number(proratedResult.proratedSalary),
            proratedPercentage: Number(proratedResult.proratedPercentage),
            frequency: employee.payFrequency.frequency,
            paye: periodPAYE,
            uif: payrollCalculations.calculateUIF(
              Number(proratedResult.proratedSalary),
              employee,
              period.startDate,
              period.endDate
            ),
            proratedPercentage: Number(proratedResult.proratedPercentage),
            frequency: employee.payFrequency.frequency,
          };
        })
      );

      // Safely access other payroll properties
      const medical = payroll?.medical || {};
      const members = medical.members || 0;
      const employerContribution = medical.employerContribution || 0;
      const medicalAidDeduction = medical.medicalAid || 0;

      const maintenanceOrder = payroll?.maintenanceOrder || 0;
      const garnishee = payroll?.garnishee || 0;
      const voluntaryTaxOverDeduction = payroll?.voluntaryTaxOverDeduction || 0;
      const incomeProtectionDeduction =
        payroll?.incomeProtection?.amountDeductedFromEmployee || 0;

      // FIXED: Enhanced period selection logic to prioritize most recent unfinalized period
      let currentPeriod;
      let periodSelectionMethod;

      if (selectedMonth && viewFinalized === "true") {
        // If viewing a finalized period, find that specific period
        // SIMPLE FIX: Use only database periods
        currentPeriod = findPeriodByDate(payPeriodsWithDetails, selectedMonth);
        periodSelectionMethod = 'specific_finalized_period';
      } else if (selectedMonth) {
        // If a specific month is selected (but not necessarily finalized)
        // SIMPLE FIX: Use only database periods
        currentPeriod = findPeriodByDate(payPeriodsWithDetails, selectedMonth);
        periodSelectionMethod = 'specific_selected_period';
      } else {
        // CRITICAL FIX: Default to most recent unfinalized period

        // Find all unfinalized periods and sort by endDate descending to get the most recent
        const unfinalizedPeriods = payPeriodsWithDetails
          .filter(p => !p.isFinalized)
          .sort((a, b) => new Date(b.endDate) - new Date(a.endDate));

        if (unfinalizedPeriods.length > 0) {
          currentPeriod = unfinalizedPeriods[0]; // Most recent unfinalized period
          periodSelectionMethod = 'most_recent_unfinalized';
        } else {
          // Fallback: Use the most recent period (even if finalized)
          currentPeriod = payPeriodsWithDetails[payPeriodsWithDetails.length - 1];
          periodSelectionMethod = 'most_recent_any';
        }
      }


      // SURGICAL FIX: For display in Calculator card, override unfinalized period end date
      // ONLY for the FIRST period - use pay-frequency firstPayrollPeriodEndDate when DOA is prior to it;
      // if DOA is after, compute first period end date from DOA.
      if (currentPeriod && !currentPeriod.isFinalized && employee?.payFrequency?.firstPayrollPeriodEndDate) {
        try {
          // Check if this is the first period for this employee
          const isFirstPeriod = await PayrollPeriod.countDocuments({
            employee: employee._id,
            isFinalized: true
          }) === 0;

          // Only apply override to the very first period
          if (isFirstPeriod) {
            const configuredFirstEnd = moment(employee.payFrequency.firstPayrollPeriodEndDate).startOf('day');
            const doa = moment(employee.doa).startOf('day');

            let displayEndMoment;
            if (doa.isAfter(configuredFirstEnd)) {
              // DOA after configured: derive end date from DOA and pay frequency
              displayEndMoment = calculateEmployeeSpecificPeriodEndDate(doa, employee.payFrequency);
            } else {
              // DOA prior to configured: use configured first period end date
              displayEndMoment = configuredFirstEnd;
            }

            if (displayEndMoment && displayEndMoment.isValid && displayEndMoment.isValid()) {
              currentPeriod.endDateBusiness = displayEndMoment.format('YYYY-MM-DD');
            }
          }
        } catch (e) {
          console.warn('Display override for first period failed:', e?.message || e);
        }
      }

      // 🚨 PERIOD SELECTION DEBUG - Log final selection result

      // Calculate initial period values
      const currentPeriodCalculations =
        await PayrollService.calculatePayrollTotals(
          employeeId,
          currentPeriod?.endDate ||
            moment.tz(DEFAULT_TIMEZONE).endOf("month").toDate(),
          company._id
        );

      // After fetching the currentPeriod, add this code to fetch hourly rates
      const hourlyRates = currentPeriod
        ? await HourlyRate.findOne({
            employee: employeeId,
            payrollPeriod: currentPeriod._id,
          }).lean()
        : null;

      // Ensure to log the hourly rates for debugging
      // Store the initial values
      const proratedSalary = Number(
        currentPeriodCalculations.basicSalary?.prorated || basicSalary
      );

      // Calculate medical aid amounts FIRST (before using in total income calculation)
      const medicalAidEmployerContribution = payroll?.medical?.employerContribution || 0;

      // When employee handles payment: employer contribution becomes paid-out allowance
      // When company handles payment: employer contribution is taxable benefit
      const medicalAidPaidOutAllowance = (payroll?.medical?.employeeHandlesPayment && medicalAidEmployerContribution > 0)
        ? medicalAidEmployerContribution
        : 0;

      const medicalAidTaxableBenefit = (!payroll?.medical?.employeeHandlesPayment && medicalAidEmployerContribution > 0)
        ? medicalAidEmployerContribution
        : 0;

      // Use the totalIncome directly from currentPeriodCalculations and add medical aid paid-out allowance
      // This will include Loss of Income, Travel Allowance, Travel Expenses, Commission, and Medical Aid Paid-Out Allowance
      const travelExpensesAmount = currentPeriod?.data?.get('travelExpenses')?.expenses || 0;
      const commissionAmount = payroll?.commission || 0;
      const baseTotalIncome = Number(
        currentPeriodCalculations.totalIncome ||
        (proratedSalary + (payroll?.lossOfIncome || 0) + (payroll?.travelAllowance?.fixedAllowanceAmount || 0) + travelExpensesAmount + commissionAmount)
      );

      // Add medical aid paid-out allowance when employee handles payment
      const totalIncome = baseTotalIncome + medicalAidPaidOutAllowance;

      // Calculate actual medical aid deduction for Calculator Card display
      const medicalAidEmployeeDeduction = payroll?.medical?.employeeHandlesPayment
        ? 0  // Employee pays - don't show in Calculator Card
        : (payroll?.medical?.medicalAid
            ? (payroll.medical.medicalAid - medicalAidEmployerContribution)
            : 0);



      // Initialize payroll totals with the correct values (including medical aid taxable benefit)
      const payrollTotals = {
        basicSalary: basicSalary,
        proratedSalary: proratedSalary,
        totalIncome: totalIncome, // Now includes medical aid taxable benefit
        totalPAYE: Number(currentPeriodCalculations.paye || 0),
        totalUIF: Number(currentPeriodCalculations.uif || 0),
        totalDeductions: Number(currentPeriodCalculations.totalDeductions || 0) + medicalAidEmployeeDeduction,
        nettPay: Number(
          currentPeriodCalculations.netPay ||
            totalIncome - (currentPeriodCalculations.deductions?.total || 0) - medicalAidEmployeeDeduction
        ),
      };


      // Ensure Custom Income placeholders are strictly period-specific
      // Finalized: use PayrollPeriod.customIncomeItems snapshot
      // Unfinalized: use Payroll.customIncomeItems for the selected/current period only (no global fallback)
      let displayCustomIncomeItems = [];
      try {
        const isFinalized = !!currentPeriod?.isFinalized;
        if (isFinalized) {
          displayCustomIncomeItems = Array.isArray(currentPeriod?.customIncomeItems)
            ? currentPeriod.customIncomeItems
            : [];
          console.log('🔒 employeeManagement: Using finalized period custom income snapshot', {
            itemsCount: displayCustomIncomeItems.length,
            periodEnd: currentPeriod?.endDate
          });
        } else if (currentPeriod?.endDate) {
          const payrollForPeriod = await Payroll.findOne({
            employee: employeeId,
            company: company._id,
            month: currentPeriod.endDate,
          }).lean();
          displayCustomIncomeItems = (payrollForPeriod && Array.isArray(payrollForPeriod.customIncomeItems))
            ? payrollForPeriod.customIncomeItems
            : [];
          console.log('🟢 employeeManagement: Using unfinalized period payroll custom income', {
            itemsCount: displayCustomIncomeItems.length,
            periodEnd: currentPeriod?.endDate
          });
        }
      } catch (e) {
        console.warn('⚠️ Period-specific custom income resolution failed:', e?.message || e);
        displayCustomIncomeItems = [];
      }

      // Initialize renderData with all necessary calculations
      const renderData = {
        employee,
        company,
        payroll,
        payrolls: [], // Will be populated later
        user: req.user,
        companyCode,
        messages: req.flash(),
        hourlyRates, // Ensure this line is included
        moment,
        formatDateForDisplay,
        formatPayPeriodEndDate: dateUtils.formatPayPeriodEndDate,
        csrfToken: req.csrfToken(),
        currentPeriod,
        currentPeriodCalculations,
        payrollTotals,
        isProrated: currentPeriod?.isPartial || false,
        proratedSalary: currentPeriod?.basicSalary || 0,
        proratedPercentage: currentPeriod?.proratedPercentage || 100,
        fullPeriodSalary: basicSalary,
        // SIMPLE FIX: Remove generated periods - use only database periods
        payFrequency: payFrequency, // Add pay frequency for template access
        selectedMonth: selectedMonth,
        viewFinalized: viewFinalized,
        periodSelectionMethod: periodSelectionMethod,
        // Add displayCustomIncomeItems for Regular Inputs placeholders
        displayCustomIncomeItems: displayCustomIncomeItems || []
      };

      // Update current period with the calculations
      if (currentPeriod) {
        // Validate all numeric values before update
        const validatedCalculations = {
          basicSalary: Number(
            currentPeriodCalculations.basicSalary?.full ||
              currentPeriodCalculations.basicSalary ||
              0
          ),
          proratedSalary: Number(
            currentPeriodCalculations.basicSalary?.prorated ||
              currentPeriodCalculations.proratedSalary ||
              0
          ),
          travelAllowance: 7800,
          paye: Number(currentPeriodCalculations.paye || 0),
          uif: Number(currentPeriodCalculations.uif || 0),
          proratedPercentage: Number(
            currentPeriodCalculations.proRataDetails?.percentage || 100
          ),
          totalIncome: Number(currentPeriodCalculations.totalIncome || 0),
          totalPAYE: Number(currentPeriodCalculations.paye || 0),
          totalUIF: Number(currentPeriodCalculations.uif || 0),
          totalDeductions: Number(
            currentPeriodCalculations.totalDeductions || 0
          ),
          netPay: Number(currentPeriodCalculations.netPay || 0),
        };

        // Update the current period with validated calculations
        currentPeriod.calculations = validatedCalculations;
      }

      // Add render data for the view
      renderData.isProrated =
        currentPeriodCalculations.proRataDetails?.isProrated || false;
      renderData.proratedPercentage =
        currentPeriodCalculations.proRataDetails?.percentage || 100;

      const payrolls = await Payroll.find({ employee: employeeId }).sort({
        month: -1,
      });

      const unfinalizedMonth =
        payrolls.find((p) => !p.finalised)?.month || null;

      const finalisedMonths = payrolls
        .filter((p) => p.finalised)
        .map((p) =>
          moment.tz(p.month, DEFAULT_TIMEZONE).endOf("month").toDate()
        );

      const thisMonth = selectedMonth
        ? moment.tz(selectedMonth, DEFAULT_TIMEZONE).endOf("month")
        : req.query.nextMonth
        ? moment.tz(req.query.nextMonth, DEFAULT_TIMEZONE).endOf("month")
        : unfinalizedMonth
        ? moment.tz(unfinalizedMonth, DEFAULT_TIMEZONE).endOf("month")
        : moment.tz(new Date(), DEFAULT_TIMEZONE).endOf("month");

      const payrollDate = thisMonth.toDate();
      const age = payrollCalculations.calculateAge(employee.dob);

      // Calculate date of birth formatting
      const dobFormatted = employee.dob
        ? moment.tz(employee.dob, DEFAULT_TIMEZONE).format("DD MMMM YYYY")
        : null;

      // Create payFrequencyObject with safe defaults
      const payFrequencyObject = {
        frequency: employee.payFrequency?.frequency || "monthly",
        lastDayOfPeriod: employee.payFrequency?.lastDayOfPeriod || "monthend",
      };

      // Safely calculate benefits
      const travelAllowance = payroll?.travelAllowance || {};
      const travelPercentageAmount =
        travelAllowance.travelPercentageAmount || 0;
      const commission = payroll?.commission || 0;
      const accommodationBenefit = payroll?.accommodationBenefit || 0;
      const bursariesAndScholarships = payroll?.bursariesAndScholarships || {};
      const companyCarBenefit = payrollCalculations.calculateCompanyCarBenefit(
        payroll?.companyCar
      );
      const companyCarUnderOperatingLeaseBenefit =
        payrollCalculations.calculateCompanyCarUnderOperatingLeaseBenefit(
          payroll?.companyCarUnderOperatingLease
        );
      const incomeProtectionBenefit =
        payrollCalculations.calculateIncomeProtectionBenefit(
          payroll?.incomeProtection
        );
      const retirementAnnuityFundBenefit =
        payrollCalculations.calculateRetirementAnnuityFundBenefit(
          payroll?.retirementAnnuityFund
        );
      const providentFundDeduction =
        payroll?.providentFund?.fixedContributionEmployee || 0;

      // Calculate pension fund employee contribution based on contribution method
      let pensionFundDeduction = 0;
      if (payroll?.pensionFund) {
        if (payroll.pensionFund.contributionCalculation === 'fixedAmount') {
          pensionFundDeduction = Number(payroll.pensionFund.fixedContributionEmployee || 0);
        } else if (payroll.pensionFund.contributionCalculation === 'percentageRFI' && employee.rfiConfig?.lastCalculation?.amount) {
          // Calculate based on RFI percentage
          const rfiAmount = Number(employee.rfiConfig.lastCalculation.amount || 0);
          const rfiPercentage = Number(payroll.pensionFund.rfiEmployee || 0);
          pensionFundDeduction = (rfiAmount * rfiPercentage) / 100;
        }
      }
      const medicalAidCredit =
        payrollCalculations.calculateMedicalAidCredit(members);

      // 🎯 COMPREHENSIVE PERIOD-SPECIFIC LOGIC

      // Find the specific payroll period based on selection parameters
      let payrollPeriod;
      // Note: periodSelectionMethod already declared above

      if (selectedMonth) {
        // User has selected a specific month/period
        // CRITICAL FIX: Use timezone-safe period finding
        if (viewFinalized === "true") {
          // Viewing a finalized period
          payrollPeriod = timezoneUtils.findPeriodByDate(payPeriodsWithDetails, selectedMonth);
          periodSelectionMethod = "finalized_period";
        } else {
          // Viewing a specific month (could be current or historical)
          payrollPeriod = timezoneUtils.findPeriodByDate(payPeriodsWithDetails, selectedMonth);
          periodSelectionMethod = "selected_month";
        }
      } else {
        // No specific month selected, use current period
        payrollPeriod = currentPeriod;
        periodSelectionMethod = "current_period";
      }


      // 🎯 PURE DATABASE RETRIEVAL - BYPASS ALL CALCULATIONS
      let periodSpecificBasicSalary = defaultBasicSalary;
      let salarySource = "default";
      let directDatabasePeriod = null;

      // 🚨 CRITICAL FIX: Determine if we should use current period regardless of selectedMonth
      // Find the actual current (non-finalized) period
      const actualCurrentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        company: company._id,
        isFinalized: false
      }).sort({ endDate: -1 }).lean();


      // Step 1: Determine which period to query directly from database
      if (selectedMonth) {
        // User has selected a specific period (either finalized or unfinalized)

        // CRITICAL FIX: Use timezone-safe date range query
        const dateRange = timezoneUtils.createDateRange(selectedMonth);

        directDatabasePeriod = await PayrollPeriod.findOne({
          employee: employeeId,
          endDate: dateRange
        }).lean();

        timezoneUtils.debugDate("Database Query Date", selectedMonth);

        if (directDatabasePeriod) {
          periodSpecificBasicSalary = Number(directDatabasePeriod.basicSalary || 0);
          salarySource = viewFinalized === "true" ? "direct_database_query_finalized" : "direct_database_query_selected";
        }
      } else {
        // No selectedMonth - use current period

        if (actualCurrentPeriod) {
          directDatabasePeriod = actualCurrentPeriod;
          periodSpecificBasicSalary = Number(directDatabasePeriod.basicSalary || 0);
          salarySource = "direct_database_query_current";
        }
      }

      // Step 2: Fallback to processed payrollPeriod if direct query failed
      if (!directDatabasePeriod && payrollPeriod && payrollPeriod.basicSalary !== undefined && payrollPeriod.basicSalary !== null) {
        periodSpecificBasicSalary = Number(payrollPeriod.basicSalary);
        salarySource = "processed_payroll_period";
      } else if (!directDatabasePeriod && payroll?.basicSalary) {
        // Final fallback to current payroll basicSalary
        periodSpecificBasicSalary = Number(payroll.basicSalary);
        salarySource = "current_payroll_fallback";
      }

      // Update basicSalary with the period-specific value
      basicSalary = periodSpecificBasicSalary;


      // If no period is found, create a default one with the resolved basicSalary
      if (!payrollPeriod) {

        const periodStartDate = moment
          .tz(thisMonth, DEFAULT_TIMEZONE)
          .startOf("month"); // Always start from the 1st
        const periodEndDate = moment
          .tz(thisMonth, DEFAULT_TIMEZONE)
          .endOf("month");

        payrollPeriod = {
          startDate: periodStartDate.toDate(),
          endDate: periodEndDate.toDate(),
          frequency: employee.payFrequency?.frequency || "monthly",
          basicSalary: basicSalary, // Use the resolved period-specific basicSalary
          paye: totalPAYE,
          uif: totalUIF,
          isFinalized: false,
        };

        salarySource = "default_period_created";
      }


      // Get current payroll period end date
      const currentPayrollDate = getCurrentOrNextLastDayOfMonth();


      // Calculate basic salary first
      // 🚨 CRITICAL FIX: Use period-specific basicSalary instead of current payroll
      const fullBasicSalary = basicSalary; // Use our period-specific value


      // Calculate period dates using BusinessDate for timezone independence
      const calculatedPeriodDates = (() => {
        if (payrollPeriod) {
          // Use BusinessDate fields if available, fallback to legacy Date fields
          const startDateBusiness = payrollPeriod.startDateBusiness || BusinessDate.fromDate(payrollPeriod.startDate);
          const endDateBusiness = payrollPeriod.endDateBusiness || BusinessDate.fromDate(payrollPeriod.endDate);

          return {
            startDate: startDateBusiness,
            endDate: endDateBusiness,
          };
        } else {
          // Default to current month if no period specified
          const currentDate = BusinessDate.today();
          return {
            startDate: BusinessDate.startOfMonth(currentDate),
            endDate: BusinessDate.endOfMonth(currentDate),
          };
        }
      })();

      // Calculate period dates using BusinessDate for timezone independence

      // FOCUSED FIX: Ensure correct period dates for pro-rata calculation
      let correctedPeriodStart = calculatedPeriodDates.startDate;
      let correctedPeriodEnd = calculatedPeriodDates.endDate;

      // If we have a payroll period with end date 2025-05-31, ensure start date is 2025-05-01
      if (correctedPeriodEnd === '2025-05-31' && correctedPeriodStart !== '2025-05-01') {
        correctedPeriodStart = '2025-05-01';
      }

      // More general fix: if end date is last day of month, ensure start is first day of month
      if (correctedPeriodEnd && (correctedPeriodEnd.endsWith('-31') || correctedPeriodEnd.endsWith('-30') || correctedPeriodEnd.endsWith('-28') || correctedPeriodEnd.endsWith('-29'))) {
        const endDateParts = correctedPeriodEnd.split('-');
        const expectedStart = `${endDateParts[0]}-${endDateParts[1]}-01`;
        if (correctedPeriodStart !== expectedStart) {
          correctedPeriodStart = expectedStart;
        }
      }

      // SURGICAL FIX: For weekly employees, ensure correct 7-day period calculation
      let finalPeriodStart = correctedPeriodStart;
      let finalPeriodEnd = correctedPeriodEnd;

      if (employee.payFrequency.frequency === 'weekly') {
        // For weekly periods, always calculate proper 7-day period
        // Period end is correct, calculate start as exactly 7 days before (6 days back)
        const endDate = BusinessDate.normalize(correctedPeriodEnd);
        finalPeriodStart = BusinessDate.addDays(endDate, -6);

        console.log('Weekly period correction applied:', {
          originalStart: correctedPeriodStart,
          originalEnd: correctedPeriodEnd,
          correctedStart: finalPeriodStart,
          correctedEnd: finalPeriodEnd,
          explanation: 'Weekly periods must be exactly 7 days'
        });
      }

      // Calculate prorated salary using BusinessDate for timezone independence
      const proratedSalaryResult = BusinessDate.calculateProratedSalary(
        employee.doa,
        fullBasicSalary,
        employee,
        finalPeriodStart,
        finalPeriodEnd
      );



      // Get the pro-rata details from the payroll calculations
      const proRataDetails = {
        isProrated: true,
        percentage: parseFloat(proratedSalaryResult.proratedPercentage),
        daysWorked: proratedSalaryResult.workedDays,
        totalDays: proratedSalaryResult.totalDaysInPeriod,
      };



      // Use the properly calculated prorated salary from the utility function
      const calculatedProratedAmount = parseFloat(
        proratedSalaryResult.proratedSalary
      );

      // SURGICAL FIX: Calculate PAYE using YTD method (SARS-compliant)
      const periodsPerYear = employee.payFrequency.frequency === 'weekly' ? 52 :
                            employee.payFrequency.frequency === 'biweekly' ? 26 : 12;

      const ytdPAYEResult = await calculateYTDPAYEForRoute(
        employeeId,
        currentPeriod.endDate,
        employee.payFrequency.frequency,
        employee.age || 35
      );

      // Calculate custom income for UIF and PAYE calculations (moved here to avoid reference error)
      const customIncomeForUIF = (displayCustomIncomeItems && Array.isArray(displayCustomIncomeItems))
        ? displayCustomIncomeItems.reduce((total, item) => {
            let amount = 0;
            if (item.calculatedAmount && item.calculatedAmount > 0) {
              amount = Number(item.calculatedAmount);
            } else if (typeof item.amount === 'number') {
              amount = Number(item.amount);
            } else if (typeof item.monthlyAmount === 'number') {
              amount = Number(item.monthlyAmount);
            }
            return total + amount;
          }, 0)
        : 0;

      console.log('=== CUSTOM INCOME EARLY CALCULATION DEBUG ===');
      console.log('displayCustomIncomeItems available:', !!displayCustomIncomeItems);
      console.log('displayCustomIncomeItems length:', displayCustomIncomeItems?.length || 0);
      console.log('customIncomeForUIF calculated:', customIncomeForUIF);

      // CRITICAL FIX: Adjust annualized income to include custom income
      // The YTD calculation only looks at basicSalary in PayrollPeriod records
      // We need to add custom income to the annualized income for proper PAYE calculation
      const baseAnnualSalary = ytdPAYEResult.annualizedIncome;
      const customIncomeAnnualized = customIncomeForUIF * periodsPerYear;
      const adjustedAnnualSalary = baseAnnualSalary + customIncomeAnnualized;

      console.log('=== PAYE ANNUAL SALARY ADJUSTMENT DEBUG ===');
      console.log('Base annual salary (from YTD):', baseAnnualSalary);
      console.log('Custom income per period:', customIncomeForUIF);
      console.log('Custom income annualized:', customIncomeAnnualized);
      console.log('Adjusted annual salary (including custom income):', adjustedAnnualSalary);

      // Calculate PAYE using enhanced calculation from utils
      // SURGICAL FIX: Use actual employee frequency instead of hardcoded "monthly"
      const travelAllowanceAmountForPAYE = payroll?.travelAllowance?.fixedAllowanceAmount || 0;
      const travelAllowanceTaxableRateForPAYE = payroll?.travelAllowance?.only20PercentTax ? 0.2 : 0.8;

      // Add travel expenses taxable amount to annual salary for PAYE calculation
      const travelExpensesTaxableAmount = travelExpensesAmount * travelAllowanceTaxableRateForPAYE;

      // Add petrol card spend taxable amount to annual salary for PAYE calculation (same method as travel expenses)
      const petrolCardSpendAmount = currentPeriod?.data?.get('travelExpenses')?.petrolCardSpend || 0;
      const petrolCardSpendTaxableAmount = petrolCardSpendAmount * travelAllowanceTaxableRateForPAYE;

      // Add km reimbursement taxable amount to annual salary for PAYE calculation (SARS-compliant method)
      const kmsTravelled = currentPeriod?.data?.get('travelExpenses')?.kmsTravelled || 0;
      const userRatePerKm = payroll?.travelAllowance?.ratePerKm || 0;
      const sarsRatePerKm = 4.76; // SARS prescribed rate per km (should be configurable)
      const kmReimbursementTotal = userRatePerKm * kmsTravelled;
      const kmReimbursementSarsAllowable = sarsRatePerKm * kmsTravelled;
      const kmReimbursementTaxableAmount = Math.max(0, kmReimbursementTotal - kmReimbursementSarsAllowable);

      // For YTD calculation, we need to include additional taxable amounts in the base calculation
      // Note: Travel expenses, petrol card, and commission are period-specific additions
      // Custom income is NOT included here because it's already included in adjustedAnnualSalary
      const periodTaxableAdditions = travelExpensesTaxableAmount + petrolCardSpendTaxableAmount + kmReimbursementTaxableAmount + commissionAmount;

      console.log('=== PAYE TAXABLE ADDITIONS DEBUG ===');
      console.log('Travel expenses taxable:', travelExpensesTaxableAmount);
      console.log('Petrol card taxable:', petrolCardSpendTaxableAmount);
      console.log('KM reimbursement taxable:', kmReimbursementTaxableAmount);
      console.log('Commission:', commissionAmount);
      console.log('Custom income (already in base):', customIncomeForUIF);
      console.log('Total period taxable additions (excluding custom income):', periodTaxableAdditions);

      // CRITICAL FIX: Recalculate YTD PAYE with adjusted annual salary that includes custom income
      // This treats custom income the same as basic salary for PAYE purposes
      let finalPAYE;

      if (customIncomeForUIF > 0) {
        // Recalculate YTD PAYE with the adjusted annual salary that includes custom income
        console.log('=== RECALCULATING PAYE WITH CUSTOM INCOME ===');

        // Calculate what the YTD tax should be with the adjusted annual salary
        const adjustedYTDTax = (adjustedAnnualSalary / periodsPerYear) * ytdPAYEResult.periodNumber;

        // Calculate PAYE on the adjusted annual salary for current period
        const adjustedPAYEResult = await payrollCalculations.calculateEnhancedPAYE({
          annualSalary: adjustedAnnualSalary,
          age: employee.age || 35,
          frequency: employee.payFrequency.frequency,
          periodEndDate: currentPeriod?.endDate
        });

        // Use the period-specific PAYE from the adjusted calculation
        finalPAYE = adjustedPAYEResult.periodPAYE || adjustedPAYEResult.monthlyPAYE || 0;

        console.log('Adjusted annual salary PAYE result:', adjustedPAYEResult);
        console.log('Final PAYE with custom income:', finalPAYE);
      } else {
        // No custom income, use original YTD PAYE result
        finalPAYE = ytdPAYEResult.currentPAYE;
        console.log('No custom income, using original YTD PAYE:', finalPAYE);
      }

      // If there are additional taxable amounts (travel expenses, petrol card, commission), calculate their tax impact
      if (periodTaxableAdditions > 0) {
        const additionalAnnualTaxable = periodTaxableAdditions * periodsPerYear;
        // Use adjustedAnnualSalary (which already includes custom income) as the base
        const totalAdjustedAnnualSalary = adjustedAnnualSalary + additionalAnnualTaxable;

        // Calculate tax on adjusted amount
        const adjustedPayeCalculation = await payrollCalculations.calculateEnhancedPAYE({
          annualSalary: totalAdjustedAnnualSalary,
          age,
          frequency: employee.payFrequency.frequency,
          accommodationBenefit,
          travelAllowance: travelAllowanceAmountForPAYE,
          travelAllowanceTaxableRate: travelAllowanceTaxableRateForPAYE,
          periodEndDate: payrollPeriod?.endDate
        });

        // Calculate the additional tax for this period using YTD method
        const adjustedYTDTax = (adjustedPayeCalculation.annualTax * ytdPAYEResult.periodNumber) / periodsPerYear;

        if (customIncomeForUIF > 0) {
          // If we already recalculated PAYE with custom income, add only the additional tax from other items
          const baseYTDTaxWithCustomIncome = (adjustedAnnualSalary / periodsPerYear) * ytdPAYEResult.periodNumber;
          const additionalTax = adjustedYTDTax - baseYTDTaxWithCustomIncome;
          finalPAYE = finalPAYE + additionalTax;

          console.log('Additional tax calculation with custom income:');
          console.log('  - Base YTD tax with custom income:', baseYTDTaxWithCustomIncome);
          console.log('  - Total adjusted YTD tax:', adjustedYTDTax);
          console.log('  - Additional tax from other items:', additionalTax);
          console.log('  - Final PAYE (base + additional):', finalPAYE);
        } else {
          // No custom income, use original calculation
          const additionalTax = adjustedYTDTax - ytdPAYEResult.ytdTax;
          finalPAYE = ytdPAYEResult.currentPAYE + additionalTax;
        }
      }

      // Apply pro-rata with full precision
      const proratedPAYE = (finalPAYE * proRataDetails.percentage) / 100;

      console.log('=== FINAL PAYE CALCULATION DEBUG ===');
      console.log('Original YTD Income:', ytdPAYEResult.ytdIncome);
      console.log('Original Annualized Income:', ytdPAYEResult.annualizedIncome);
      console.log('Adjusted Annualized Income (with custom income):', adjustedAnnualSalary);
      console.log('Custom income per period:', customIncomeForUIF);
      console.log('Custom income annualized:', customIncomeAnnualized);
      console.log('YTD Tax should be:', ytdPAYEResult.ytdTax);
      console.log('Tax already paid:', ytdPAYEResult.taxAlreadyPaid);
      console.log('Base YTD PAYE (original):', ytdPAYEResult.currentPAYE);
      console.log('Final PAYE (with custom income and additions):', finalPAYE);
      console.log('Prorated PAYE:', proratedPAYE);
      console.log('Period number in tax year:', ytdPAYEResult.periodNumber);



      // Calculate UIF (capped at 177.12) - include custom income in UIF base
      // customIncomeForUIF already calculated above for PAYE calculation

      const uifBase = calculatedProratedAmount + customIncomeForUIF;
      const proratedUIF = Math.min(
        parseFloat((uifBase * 0.01).toFixed(2)),
        177.12
      );

      console.log('=== UIF CALCULATION DEBUG (employeeManagement) ===');
      console.log('Basic salary (calculatedProratedAmount):', calculatedProratedAmount);
      console.log('Custom income for UIF:', customIncomeForUIF);
      console.log('UIF base (basic + custom):', uifBase);
      console.log('UIF amount:', proratedUIF);

      const totalDeductions = parseFloat(
        (proratedPAYE + proratedUIF + (medicalAidEmployeeDeduction || 0) + (pensionFundDeduction || 0)).toFixed(2)
      );



      // Update render data with calculated values

      renderData.proratedSalary = parseFloat(
        proratedSalaryResult.proratedSalary
      );
      // 🚨 CRITICAL FIX: Ensure renderData uses period-specific basicSalary
      renderData.basicSalary = fullBasicSalary; // Now uses period-specific value
      renderData.isProrated = proRataDetails.isProrated;

      renderData.proRataDetails = proRataDetails;

      // Use PayrollService calculations for total income (includes Loss of Income correctly)
      const lossOfIncomeAmount = payroll?.lossOfIncome || 0;
      const travelAllowanceAmount = payroll?.travelAllowance?.fixedAllowanceAmount || 0;
      // travelExpensesAmount already declared at line 853
      // commissionAmount already declared at line 854

      // Prioritize PayrollService calculation, fallback to manual calculation (including custom income)
      renderData.totalIncome = Number(
        currentPeriodCalculations.totalIncome ||
        (parseFloat(proratedSalaryResult.proratedSalary) + parseFloat(lossOfIncomeAmount) + parseFloat(travelAllowanceAmount) + parseFloat(travelExpensesAmount) + parseFloat(commissionAmount) + customIncomeForUIF)
      );

      console.log('=== TOTAL INCOME CALCULATION DEBUG ===');
      console.log('PayrollService totalIncome:', currentPeriodCalculations.totalIncome);
      console.log('Fallback calculation components:');
      console.log('  - Prorated salary:', parseFloat(proratedSalaryResult.proratedSalary));
      console.log('  - Loss of income:', parseFloat(lossOfIncomeAmount));
      console.log('  - Travel allowance:', parseFloat(travelAllowanceAmount));
      console.log('  - Travel expenses:', parseFloat(travelExpensesAmount));
      console.log('  - Commission:', parseFloat(commissionAmount));
      console.log('  - Custom income:', customIncomeForUIF);
      console.log('Final totalIncome used:', renderData.totalIncome);

      // Use PayrollService calculations for deductions (excludes Loss of Income from PAYE correctly)
      console.log('=== PAYE FRONTEND DEBUG ===');
      console.log('currentPeriodCalculations.deductions?.statutory?.paye:', currentPeriodCalculations.deductions?.statutory?.paye);
      console.log('currentPeriodCalculations.paye:', currentPeriodCalculations.paye);
      console.log('proratedPAYE:', proratedPAYE);

      // CRITICAL FIX: For finalized periods, use stored PAYE value to preserve historical accuracy
      // For unfinalized periods, use YTD calculation
      const isCurrentPeriodFinalized = currentPeriod?.isFinalized;
      const storedPAYE = currentPeriod?.PAYE;

      console.log('=== FINALIZED PERIOD CHECK ===');
      console.log('Is current period finalized:', isCurrentPeriodFinalized);
      console.log('Stored PAYE in database:', storedPAYE);
      console.log('YTD calculated PAYE:', proratedPAYE);

      renderData.totalPAYE = Number(
        (isCurrentPeriodFinalized && storedPAYE > 0)
          ? storedPAYE  // Use historical PAYE for finalized periods
          : (proratedPAYE ||  // Use YTD calculation for unfinalized periods
             currentPeriodCalculations.deductions?.statutory?.paye ||
             currentPeriodCalculations.paye ||
             0)
      ).toFixed(2);

      console.log('Final PAYE used for display:', renderData.totalPAYE);
      console.log('Source:', (isCurrentPeriodFinalized && storedPAYE > 0) ? 'Stored (finalized)' : 'YTD calculation');

      console.log('Final totalPAYE being sent to frontend:', renderData.totalPAYE);

      renderData.totalUIF = Number(
        currentPeriodCalculations.deductions?.statutory?.uif ||
        proratedUIF
      );

      renderData.totalDeductions = Number(
        currentPeriodCalculations.deductions?.total ||
        totalDeductions
      );
      renderData.nettPay = parseFloat(
        (
          renderData.totalIncome - totalDeductions
        ).toFixed(2)
      );

      // Add payrollDetails for the template with corrected Loss of Income calculations
      renderData.payrollDetails = {
        basicSalary: fullBasicSalary,
        proRataDetails: proRataDetails,
        isProrated: proRataDetails.isProrated,
        proratedAmount: proratedSalaryResult.proratedSalary,
        proratedPAYE: renderData.totalPAYE,
        proratedUIF: renderData.totalUIF.toFixed(2),
        totalDeductions: renderData.totalDeductions.toFixed(2),
        totalIncome: renderData.totalIncome.toFixed(2), // Include totalIncome with Loss of Income
      };


      // Prevent later calculations from overwriting our prorated values
      const finalRenderData = {
        currentPeriod: true,
        isProrated: renderData.isProrated,
        proratedSalary: renderData.proratedSalary,
        basicSalary: renderData.basicSalary,
        totalIncome: renderData.totalIncome,
        totalPAYE: renderData.totalPAYE,
        totalUIF: renderData.totalUIF,
        totalDeductions: renderData.totalDeductions,
        nettPay: renderData.nettPay,
        payrollDetails: renderData.payrollDetails,
        proRataDetails: renderData.proRataDetails,
      };


      // Use Object.assign to update renderData instead of reassignment
      Object.assign(renderData, finalRenderData);


      // Debug medical aid data
      // Calculate taxable income based on payment method
      // SURGICAL FIX: Create updated payroll object with current basicSalary for accurate calculations
      const updatedPayrollForCalculation = {
        ...(payroll || {}),
        basicSalary: basicSalary  // Use the updated basicSalary value
      };

      const taxableIncome = payroll?.medical?.employeeHandlesPayment
        ? totalIncome  // Employee pays: use total income (includes paid-out allowance)
        : await PayrollService.calculateTaxableIncome(updatedPayrollForCalculation);  // Company pays: use PayrollService calculation with updated basic salary

      console.log('=== MEDICAL AID DEBUG ===');
      console.log('Medical aid data:', payroll?.medical);
      console.log('Employer contribution:', payroll?.medical?.employerContribution);
      console.log('Employee handles payment:', payroll?.medical?.employeeHandlesPayment);
      console.log('Medical aid taxable benefit (company pays):', medicalAidTaxableBenefit);
      console.log('Medical aid paid-out allowance (employee pays):', medicalAidPaidOutAllowance);
      console.log('Medical aid employee deduction (for Calculator Card):', medicalAidEmployeeDeduction);
      console.log('Base total income:', baseTotalIncome);
      console.log('Final total income (includes allowance):', totalIncome);
      console.log('Taxable income for PAYE:', taxableIncome);

      console.log('=== TAXABLE INCOME DEBUG ===');
      console.log('Calculated taxable income:', taxableIncome);
      console.log('Should include medical aid employer contribution:', payroll?.medical?.employerContribution);

      // Calculate all deductions using new function (includes medical aid tax credits)
      const deductionsResult = await payrollCalculations.calculateTotalDeductions(
        payroll || {}, // Provide empty object as fallback
        taxableIncome,
        employee.payFrequency?.frequency || "monthly",
        payrollPeriod?.startDate,
        payrollPeriod?.endDate
      );

      // Calculate medical aid impact using new function
      const medicalAidImpact = await payrollCalculations.calculateMedicalAidTaxImpact(
        payroll?.medical || {}, // Provide empty object as fallback
        employee.payFrequency?.frequency || "monthly",
        taxableIncome,
        payrollPeriod?.endDate
      );

      // Calculate medical aid tax credits using PayrollService method (conditional based on checkbox)
      const applyTaxCredits = !payroll?.medical?.dontApplyTaxCredits;
      const medicalAidTaxCredit = applyTaxCredits
        ? await PayrollService.calculateMedicalAidTaxCredit(
            payroll || {},
            employee.payFrequency?.frequency || "monthly",
            payrollPeriod?.endDate
          )
        : 0;

      console.log('=== MEDICAL AID TAX CREDITS DEBUG ===');
      console.log('Medical aid tax credit calculated:', medicalAidTaxCredit);
      console.log('Medical aid members:', payroll?.medical?.members);
      console.log('Dont apply tax credits:', payroll?.medical?.dontApplyTaxCredits);

      // CRITICAL FIX: For finalized periods, use stored PAYE; for unfinalized, use prorated YTD calculation
      const grossPAYE = (isCurrentPeriodFinalized && storedPAYE > 0)
        ? storedPAYE  // Use historical PAYE for finalized periods
        : proratedPAYE;  // Use prorated YTD calculation for unfinalized periods

      const netPAYE = Math.max(0, grossPAYE - medicalAidTaxCredit);

      // Update payrollTotals with appropriate PAYE (stored for finalized, YTD for unfinalized)
      payrollTotals.totalPAYE = netPAYE;

      console.log('=== PAYE SOURCE DECISION ===');
      console.log('Using stored PAYE:', (isCurrentPeriodFinalized && storedPAYE > 0));
      console.log('finalPAYE (unprorated):', finalPAYE);
      console.log('proratedPAYE (should be used):', proratedPAYE);
      console.log('Gross PAYE used:', grossPAYE);
      console.log('Net PAYE (after tax credits):', netPAYE);
      console.log('Employee frequency:', employee?.payFrequency?.frequency);
      payrollTotals.totalUIF = Number(deductionsResult.uif || 0);
      payrollTotals.totalDeductions = netPAYE + payrollTotals.totalUIF + medicalAidEmployeeDeduction + (pensionFundDeduction || 0);
      payrollTotals.nettPay = payrollTotals.totalIncome - payrollTotals.totalDeductions;

      console.log('=== PAYE TAX CREDITS APPLICATION DEBUG ===');
      console.log('Gross PAYE (before tax credits):', grossPAYE);
      console.log('Medical aid tax credits:', medicalAidTaxCredit);
      console.log('Net PAYE (after tax credits):', netPAYE);

      // Update the values being passed to the template
      Object.assign(renderData, {
        payrolls,
        thisMonth: thisMonth.toDate(),
        unfinalizedMonth,
        finalisedMonths,
        payrollDate,
        dobFormatted,
        employeeAge: age,
        basicSalary,
        travelAllowance,
        travelPercentageAmount,
        commission,
        accommodationBenefit,
        bursariesAndScholarships,
        companyCarBenefit,
        companyCarUnderOperatingLeaseBenefit,
        incomeProtectionBenefit,
        incomeProtectionDeduction,
        retirementAnnuityFundBenefit,
        providentFundDeduction,
        pensionFundDeduction,
        medicalAidCredit,
        medicalAidDeduction: medicalAidEmployeeDeduction,
        medicalAidTaxableBenefit: medicalAidImpact.taxableBenefit,
        periodPAYE: currentPeriod?.paye || 0,
        uifAmount: currentPeriod?.uif || 0,
        maintenanceOrder,
        garnishee,
        voluntaryTaxOverDeduction,
        totalDeductions: payrollTotals.totalDeductions,
        totalIncome: payrollTotals.totalIncome,
        nettPay: payrollTotals.nettPay,
        isProratedMonth: (date) => isProratedMonth(employee, date),
        payFrequency: employee.payFrequency?.name || "N/A",
        payFrequencyObject,
        currentPeriod,
        nextPeriod:
          payPeriodsWithDetails[
            payPeriodsWithDetails.indexOf(currentPeriod) + 1
          ] || null,
        payPeriods: payPeriodsWithDetails,
        payrollPeriod,
        taxableIncome,
        paye: deductionsResult.paye,
        uif: deductionsResult.uif,
        medicalAidCredit: medicalAidImpact.taxCredit,
        medicalAidEmployeeDeduction: medicalAidEmployeeDeduction,
        medicalAidTaxableBenefit: medicalAidTaxableBenefit,
        medicalAidPaidOutAllowance: medicalAidPaidOutAllowance,
        medicalAidTaxCredit: medicalAidTaxCredit,
        applyTaxCredits: applyTaxCredits,
        grossPAYE: grossPAYE,
        netPAYE: netPAYE,
        deductionDetails: deductionsResult.deductionDetails,
        ...payrollTotals,
      });


      // Travel Allowance Calculation

      if (
        payroll?.travelAllowance?.fixedAllowance ||
        payroll?.travelAllowance?.reimbursedExpenses
      ) {
        const travelAllowanceResult =
          await PayrollService.processTravelAllowance(employee, payroll);


        if (travelAllowanceResult.travelAllowanceProcessed) {
          const calculationDetails = travelAllowanceResult.calculationDetails;

          // Calculate correct taxable percentage based on only20PercentTax flag
          const taxablePercentage = payroll?.travelAllowance?.only20PercentTax ? 20 : 80;
          const nonTaxablePercentage = payroll?.travelAllowance?.only20PercentTax ? 80 : 20;

          currentPeriod.calculations.travelAllowance = {
            total: calculationDetails.totalAllowance,
            taxable: {
              amount: calculationDetails.taxableAmount,
              percentage: taxablePercentage,
            },
            nonTaxable: {
              amount: calculationDetails.nonTaxableAmount,
              percentage: nonTaxablePercentage,
            },
            sarsPrescribedRate: calculationDetails.sarsPrescribedRate,
            irpCodes: calculationDetails.irpCodes,
          };
        }
      }

      // Log employee age details

      // Add debug logging

      // 🚨 FINAL VERIFICATION: Ensure basicSalary is correct before template render

      // 🚨 CRITICAL FIX: Override renderData.basicSalary with direct database value
      if (directDatabasePeriod && directDatabasePeriod.basicSalary !== undefined) {
        renderData.basicSalary = Number(directDatabasePeriod.basicSalary);
      }

      // Get navigation data for employee profile navigation arrows
      console.log("Starting navigation data fetch for employee:", employeeId);
      let navigationData = { previous: null, next: null };
      try {
        // Get all employees for the company in the same order as employee management
        const allEmployees = await Employee.find({ company: company._id })
          .select("_id companyEmployeeNumber firstName lastName")
          .sort({ companyEmployeeNumber: 1, lastName: 1, firstName: 1 })
          .lean();

        // Find current employee index
        const currentIndex = allEmployees.findIndex(emp => emp._id.toString() === employeeId);

        if (currentIndex !== -1) {
          // Get previous and next employees
          const previousEmployee = currentIndex > 0 ? allEmployees[currentIndex - 1] : null;
          const nextEmployee = currentIndex < allEmployees.length - 1 ? allEmployees[currentIndex + 1] : null;

          navigationData = {
            previous: previousEmployee ? {
              id: previousEmployee._id,
              name: `${previousEmployee.firstName} ${previousEmployee.lastName}`,
              employeeNumber: previousEmployee.companyEmployeeNumber,
              url: `/clients/${companyCode}/employeeProfile/${previousEmployee._id}`
            } : null,
            next: nextEmployee ? {
              id: nextEmployee._id,
              name: `${nextEmployee.firstName} ${nextEmployee.lastName}`,
              employeeNumber: nextEmployee.companyEmployeeNumber,
              url: `/clients/${companyCode}/employeeProfile/${nextEmployee._id}`
            } : null,
            totalEmployees: allEmployees.length,
            currentPosition: currentIndex + 1
          };
        }
      } catch (navError) {
        console.error("Error fetching navigation data:", navError);
        // Continue with empty navigation data if there's an error
      }

      console.log("Navigation data being passed to template:", navigationData);

      // Pass the data directly to the template
      res.render("employeeProfile", {
        ...renderData,
        // 🚨 DOUBLE PROTECTION: Explicitly pass the correct basicSalary
        basicSalary: directDatabasePeriod ? Number(directDatabasePeriod.basicSalary) : basicSalary,
        navigation: navigationData, // Add navigation data for employee arrows
        totalPAYE: Number(proratedPAYE.toFixed(2)), // Round for display consistency
        totalUIF: proratedUIF,
        totalDeductions: totalDeductions,
        nettPay: parseFloat(
          (calculatedProratedAmount - totalDeductions).toFixed(2)
        ),
        // 🎯 BusinessDate Pro-rated Salary Results
        proratedSalaryResult: proratedSalaryResult,
        proRataDetails: proRataDetails,
        isProrated: proratedSalaryResult.isFirstPeriodWithDOA,
        // 🚨 VERIFICATION TEST MARKER - This will appear on the webpage 🚨
        testMarker: "🔥 PERIOD SELECTION FIX ACTIVE - DEFAULTS TO UNFINALIZED PERIOD 🔥",
        testTimestamp: new Date().toISOString(),
        // 🎯 DEBUGGING INFO FOR PURE DATABASE RETRIEVAL
        debugInfo: {
          selectedMonth: selectedMonth || "none",
          viewFinalized: viewFinalized || "false",
          periodSelectionMethod: periodSelectionMethod,
          salarySource: salarySource,
          periodId: payrollPeriod?._id || "default",
          periodBasicSalary: payrollPeriod?.basicSalary || "N/A",
          directDatabaseValue: directDatabasePeriod?.basicSalary || "N/A",
          usingDirectDatabase: !!directDatabasePeriod,
          // BusinessDate calculation details
          proratedCalculation: {
            workedDays: proratedSalaryResult.workedDays,
            totalDays: proratedSalaryResult.totalDaysInPeriod,
            percentage: proratedSalaryResult.proratedPercentage,
            proratedAmount: proratedSalaryResult.proratedSalary,
            fullAmount: proratedSalaryResult.fullPeriodSalary
          }
        },
      });
    } catch (error) {
      console.error("Error processing employee profile:", error);
      req.flash(
        "error",
        "An error occurred while processing the employee profile"
      );
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

router.get(
  "/:companyCode/employeeProfile/:employeeId/addBasicSalary",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find company first to validate company code
      const company = await Company.findOne({ companyCode });
      if (!company) {
        console.error("Company not found:", companyCode);
        req.flash("error", "Company not found");
        return res.redirect("/dashboard");
      }

      // Get fully populated employee object with company validation
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id
      })
        .populate({
          path: "payFrequency",
          model: "PayFrequency",
        })
        .populate({
          path: "company",
          model: "Company",
        });

      if (!employee) {
        console.error("Employee not found in company:", employeeId, companyCode);
        req.flash("error", "Employee not found in the specified company");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }


      // Validate employee status
      if (employee.status === "Inactive" || employee.status === "Terminated") {
        req.flash(
          "error",
          `Cannot edit salary for ${employee.status} employee`
        );
        return res.redirect(
          `/clients/${companyCode}/employeeProfile/${employeeId}`
        );
      }

      if (!employee.payFrequency) {
        req.flash("error", "Employee pay frequency not configured");
        return res.redirect(
          `/clients/${companyCode}/employeeProfile/${employeeId}`
        );
      }

      // FIXED: Get current period based on user selection first, then default to unfinalized
      let currentPeriod;
      let selectedMonth;

      if (req.query.selectedMonth) {
        // PRIORITY FIX: If a specific month is selected, use that period (respects user choice)
        // CRITICAL FIX: Use UTC parsing to avoid timezone conversion issues
        selectedMonth = moment.utc(req.query.selectedMonth).endOf('day').toDate();

        // Find the exact period for the selected date with range search for better matching
        // Use timezone-aware date calculations for range search
        const dayBefore = moment.tz(selectedMonth, DEFAULT_TIMEZONE).subtract(1, 'day').toDate();
        const dayAfter = moment.tz(selectedMonth, DEFAULT_TIMEZONE).add(1, 'day').toDate();
        currentPeriod = await PayrollPeriod.findOne({
          employee: employeeId,
          company: company._id,
          endDate: {
            $gte: dayBefore,
            $lte: dayAfter
          }
        });

        if (!currentPeriod) {
          // Try exact match if range search fails
          currentPeriod = await PayrollPeriod.findOne({
            employee: employeeId,
            company: company._id,
            endDate: selectedMonth,
          });
        }


      } else {
        // DEFAULT: Only use earliest unfinalized period when no specific selection
        currentPeriod = await PayrollPeriod.findOne({
          employee: employeeId,
          isFinalized: false,
        }).sort({ endDate: 1 }); // Get the earliest non-finalized period

        if (currentPeriod) {
        }
      }

      if (!currentPeriod) {
        try {
          // Create initial period using the correct method for first-time period creation
          currentPeriod = await PayrollPeriod.createInitialPeriod(employee);
        } catch (error) {
          console.error("Error creating initial period:", error);
          req.flash("error", `Failed to create initial pay period: ${error.message}`);
          return res.redirect(
            `/clients/${companyCode}/employeeProfile/${employeeId}`
          );
        }
      }

      // Validate period end date against termination date
      if (employee.lastDayOfService) {
        const terminationDate = moment(employee.lastDayOfService).endOf("day");
        const periodEndDate = moment(currentPeriod.endDate).endOf("day");

        if (periodEndDate.isAfter(terminationDate)) {
          req.flash(
            "error",
            "Cannot edit salary for period after termination date"
          );
          return res.redirect(
            `/clients/${companyCode}/employeeProfile/${employeeId}`
          );
        }
      }

      // Find or create payroll for the current period
      let payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
        month: currentPeriod.endDate,
      });



      // Create a default payroll object if none exists
      const defaultPayroll = payroll || {
        basicSalary: currentPeriod.basicSalary || 0,
        hourlyPaid: false,
        dontAutoPayPublicHolidays: false,
        additionalHours: false,
      };

      // Create a default current period if none exists
      const defaultCurrentPeriod = currentPeriod || {
        _id: "new",
        startDate: new Date(),
        endDate: new Date(),
        basicSalary: 0,
      };

      res.render("addBasicSalary", {
        employee,
        company,
        companyCode,
        currentPeriod: defaultCurrentPeriod,
        payroll: defaultPayroll,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
        req,
        path: req.path,
        moment,
      });
    } catch (error) {
      console.error("Error rendering add basic salary page:", error);
      req.flash("error", "An error occurred while loading the page");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

router.post(
  "/:companyCode/employeeProfile/:employeeId/update-basic-salary",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    // Smart transaction detection for self-hosted MongoDB compatibility
    let session = null;
    let useTransactions = false;

    // Check environment variable first (allows manual override)
    const forceDisableTransactions = process.env.DISABLE_MONGODB_TRANSACTIONS === 'true';

    if (forceDisableTransactions) {
      console.log('ℹ️ MongoDB transactions disabled via environment variable - using atomic operations');
      useTransactions = false;
    } else {
      try {
        // For self-hosted MongoDB, skip transaction detection and use atomic operations
        // This is safer for single-node deployments which are common in self-hosted setups
        console.log('ℹ️ Self-hosted MongoDB detected - using atomic operations for compatibility');
        useTransactions = false;
      } catch (transactionError) {
        console.log('ℹ️ MongoDB transactions not supported - using atomic operations');
        console.log('Transaction detection error:', transactionError.message);
        useTransactions = false;
      }
    }

    try {

      const { employeeId, companyId, basicSalary, hourlyPaid } = req.body;
      const { companyCode } = req.params;

      // Determine if this is a JSON request (AJAX) or form submission
      const isJsonRequest = req.headers['content-type']?.includes('application/json');

      // Enhanced validation for required fields
      if (!employeeId) {
        throw new Error("Missing required field: employeeId");
      }

      // Validate basic salary - ensure we have a valid number
      const parsedSalary = parseFloat(basicSalary);
      if (isNaN(parsedSalary) || parsedSalary < 0) {
        throw new Error(
          "Invalid basic salary value. Must be a positive number."
        );
      }


      // Find company first to validate company code
      const company = await Company.findOne({ companyCode });
      if (!company) {
        throw new Error("Company not found");
      }

      // Get fully populated employee object with company validation
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id
      })
        .populate("payFrequency")
        .populate("company");

      if (!employee) {
        throw new Error("Employee not found in the specified company");
      }

      // Get company ID from validated company
      const validCompanyId = company._id.toString();


      // Validate employee status
      if (employee.status === "Inactive" || employee.status === "Terminated") {
        throw new Error(`Cannot edit salary for ${employee.status} employee`);
      }

      if (!employee.payFrequency) {
        throw new Error("Employee pay frequency not configured");
      }

      // Get current period
      let currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        isFinalized: false,
      }).sort({ endDate: 1 }); // Get the earliest non-finalized period

      if (!currentPeriod) {
        try {
          // Generate a new period using PayrollService
          currentPeriod = await PayrollService.generateNextPeriod(
            employee,
            null, // No previous period
            null // No session needed for read operation
          );
        } catch (error) {
          console.error("Error generating period:", error);
          req.flash("error", `Failed to generate pay period: ${error.message}`);
          return res.redirect(
            `/clients/${companyCode}/employeeProfile/${employeeId}`
          );
        }
      }


      // Find or create payroll for the current period
      let payroll = await Payroll.findOne({
        employee: employeeId,
        company: validCompanyId,
        month: currentPeriod.endDate,
      });


      const isHourlyPaid = hourlyPaid === "on" || hourlyPaid === true;
      const oldSalary = payroll ? payroll.basicSalary : 0;
      const oldHourlyPaid = payroll ? payroll.hourlyPaid : false;

      if (!payroll) {
        payroll = new Payroll({
          employee: employeeId,
          company: validCompanyId,
          month: moment(currentPeriod.endDate).toDate(),
          basicSalary: parsedSalary,
          hourlyPaid: isHourlyPaid,
          status: "draft",
        });
      } else {
        payroll.basicSalary = parsedSalary;
        payroll.hourlyPaid = isHourlyPaid;
      }

      // Update the current period's basic salary as well
      currentPeriod.basicSalary = parsedSalary;

      // Save with conditional session support
      if (useTransactions && session) {
        await currentPeriod.save({ session });
        await payroll.save({ session });
      } else {
        await currentPeriod.save();
        await payroll.save();
      }

      // CRITICAL FIX: Efficient PayrollPeriod recalculation after basic salary change
      console.log('🔄 Recalculating PayrollPeriod amounts with new basic salary...');
      try {
        // Get the updated period with new basic salary
        const updatedPeriod = await PayrollPeriod.findById(currentPeriod._id);

        // ✅ FIXED: Calculate proper proration using BusinessDate (same as employeeProfile)
        const BusinessDate = require('../utils/BusinessDate');

        // ✅ FIXED: Use Business date strings to avoid timezone conversion issues
        // Use startDateBusiness/endDateBusiness if available, otherwise normalize Date objects
        const periodStartStr = updatedPeriod.startDateBusiness || BusinessDate.normalize(updatedPeriod.startDate);
        const periodEndStr = updatedPeriod.endDateBusiness || BusinessDate.normalize(updatedPeriod.endDate);

        console.log('🔍 Period date handling for BusinessDate calculation:', {
          startDate: updatedPeriod.startDate,
          endDate: updatedPeriod.endDate,
          startDateBusiness: updatedPeriod.startDateBusiness,
          endDateBusiness: updatedPeriod.endDateBusiness,
          periodStartStr,
          periodEndStr
        });

        // SURGICAL FIX: For weekly employees, ensure correct 7-day period calculation
        let finalPeriodStartStr = periodStartStr;
        let finalPeriodEndStr = periodEndStr;

        if (employee.payFrequency.frequency === 'weekly') {
          // For weekly periods, always calculate proper 7-day period
          // Period end is correct, calculate start as exactly 7 days before (6 days back)
          const endDate = BusinessDate.normalize(periodEndStr);
          finalPeriodStartStr = BusinessDate.addDays(endDate, -6);

          console.log('Weekly period correction applied (finalize route):', {
            originalStart: periodStartStr,
            originalEnd: periodEndStr,
            correctedStart: finalPeriodStartStr,
            correctedEnd: finalPeriodEndStr,
            explanation: 'Weekly periods must be exactly 7 days'
          });
        }

        // Calculate proration using the same method as employeeProfile
        const proratedSalaryResult = BusinessDate.calculateProratedSalary(
          employee.doa,
          parsedSalary,
          employee,
          finalPeriodStartStr,
          finalPeriodEndStr
        );

        const proRataPercentage = proratedSalaryResult.proratedPercentage;
        const isProrated = proratedSalaryResult.isFirstPeriodWithDOA && proRataPercentage < 100;

        console.log('🔍 BusinessDate proration calculation for salary update:', {
          employeeDOA: employee.doa,
          periodStart: periodStartStr,
          periodEnd: periodEndStr,
          proRataPercentage: proRataPercentage.toFixed(2) + '%',
          isProrated,
          workedDays: proratedSalaryResult.workedDays,
          totalDaysInPeriod: proratedSalaryResult.totalDaysInPeriod
        });

        // Calculate gross pay (pro-rated basic salary)
        const grossPay = parseFloat((parsedSalary * proRataPercentage / 100).toFixed(2));

        // Calculate PAYE using the same method as employeeProfile
        const payrollCalculations = require('../utils/payrollCalculations');
        // SURGICAL FIX: Calculate PAYE using YTD method for salary update
        const periodsPerYear = employee.payFrequency.frequency === 'weekly' ? 52 :
                              employee.payFrequency.frequency === 'biweekly' ? 26 : 12;

        const ytdPAYEResult = await calculateYTDPAYEForRoute(
          employeeId,
          currentPeriod.endDate,
          employee.payFrequency.frequency,
          employee.age || 35
        );

        console.log('🔍 YTD PAYE Result for salary update:', ytdPAYEResult);

        const annualSalary = ytdPAYEResult.annualizedIncome;
        const age = payrollCalculations.calculateAge(employee.dob);

        // SURGICAL FIX: Use actual employee frequency instead of hardcoded "monthly"
        const travelAllowanceAmountForRecalc = payroll?.travelAllowance?.fixedAllowanceAmount || 0;
        const travelAllowanceTaxableRateForRecalc = payroll?.travelAllowance?.only20PercentTax ? 0.2 : 0.8;

        // Add travel expenses taxable amount to annual salary for PAYE recalculation
        const travelExpensesAmountForRecalc = currentPeriod?.data?.get('travelExpenses')?.expenses || 0;
        const travelExpensesTaxableAmountForRecalc = travelExpensesAmountForRecalc * travelAllowanceTaxableRateForRecalc;

        // Add petrol card spend taxable amount to annual salary for PAYE recalculation (same method as travel expenses)
        const petrolCardSpendAmountForRecalc = currentPeriod?.data?.get('travelExpenses')?.petrolCardSpend || 0;
        const petrolCardSpendTaxableAmountForRecalc = petrolCardSpendAmountForRecalc * travelAllowanceTaxableRateForRecalc;

        // Use YTD PAYE calculation for salary update recalculation
        const periodTaxableAdditionsForRecalc = travelExpensesTaxableAmountForRecalc + petrolCardSpendTaxableAmountForRecalc;

        // Use YTD PAYE result as the base
        let finalPAYEForRecalc = ytdPAYEResult.currentPAYE;

        // Fallback: If YTD calculation returns 0, use traditional calculation
        if (finalPAYEForRecalc === 0 && parsedSalary > 0) {
          console.log('⚠️ YTD PAYE returned 0, using traditional fallback calculation');
          const traditionalAnnualSalary = parsedSalary * periodsPerYear;
          const traditionalPayeResult = await payrollCalculations.calculateEnhancedPAYE({
            annualSalary: traditionalAnnualSalary,
            age,
            frequency: employee.payFrequency.frequency,
            periodEndDate: currentPeriod?.endDate
          });
          finalPAYEForRecalc = traditionalPayeResult.monthlyPAYE || traditionalPayeResult.periodPAYE || 0;
          console.log('Traditional fallback PAYE:', finalPAYEForRecalc);
        }

        // If there are additional taxable amounts, calculate their tax impact
        if (periodTaxableAdditionsForRecalc > 0) {
          const additionalAnnualTaxableForRecalc = periodTaxableAdditionsForRecalc * periodsPerYear;
          const totalAdjustedAnnualSalaryForRecalc = ytdPAYEResult.annualizedIncome + additionalAnnualTaxableForRecalc;

          // Calculate tax on adjusted amount
          const adjustedPayeCalculationForRecalc = await payrollCalculations.calculateEnhancedPAYE({
            annualSalary: totalAdjustedAnnualSalaryForRecalc,
            age,
            frequency: employee.payFrequency.frequency,
            accommodationBenefit: payroll?.accommodationBenefit || 0,
            travelAllowance: travelAllowanceAmountForRecalc,
            travelAllowanceTaxableRate: travelAllowanceTaxableRateForRecalc,
            periodEndDate: currentPeriod?.endDate
          });

          // Calculate the additional tax for this period using YTD method
          const adjustedYTDTaxForRecalc = (adjustedPayeCalculationForRecalc.annualTax * ytdPAYEResult.periodNumber) / periodsPerYear;
          const additionalTaxForRecalc = adjustedYTDTaxForRecalc - ytdPAYEResult.ytdTax;

          finalPAYEForRecalc = ytdPAYEResult.currentPAYE + additionalTaxForRecalc;
        }

        const payeCalculation = {
          periodPAYE: finalPAYEForRecalc,
          monthlyPAYE: finalPAYEForRecalc
        };

        console.log('🔍 PAYE calculation for period update:', {
          finalPAYEForRecalc,
          periodPAYE: payeCalculation.periodPAYE,
          proRataPercentage,
          willBeProratedTo: (finalPAYEForRecalc * proRataPercentage) / 100
        });

        // Apply pro-rata to PAYE with full precision using period-specific PAYE
        const periodPAYE = payeCalculation.periodPAYE; // Use new frequency-specific PAYE
        const proratedPAYE = (periodPAYE * proRataPercentage) / 100; // Keep full precision

        // Calculate UIF using the same method as employeeProfile
        const proratedUIF = Math.min(
          parseFloat((grossPay * 0.01).toFixed(2)),
          177.12
        );

        // Calculate SDL for employer reporting (NOT included in employee deductions)
        const sdl = parseFloat((grossPay * 0.01).toFixed(2)); // 1% of gross pay

        // Calculate medical aid employee deduction
        const medicalAidEmployerContribution = payroll?.medical?.employerContribution || 0;
        const medicalAidEmployeeDeduction = payroll?.medical?.employeeHandlesPayment
          ? 0  // Employee pays - don't include in deductions
          : (payroll?.medical?.medicalAid
              ? (payroll.medical.medicalAid - medicalAidEmployerContribution)
              : 0);

        // Calculate pension fund employee contribution based on contribution method
        let pensionFundDeduction = 0;
        if (payroll?.pensionFund) {
          if (payroll.pensionFund.contributionCalculation === 'fixedAmount') {
            pensionFundDeduction = Number(payroll.pensionFund.fixedContributionEmployee || 0);
          } else if (payroll.pensionFund.contributionCalculation === 'percentageRFI' && employee.rfiConfig?.lastCalculation?.amount) {
            // Calculate based on RFI percentage
            const rfiAmount = Number(employee.rfiConfig.lastCalculation.amount || 0);
            const rfiPercentage = Number(payroll.pensionFund.rfiEmployee || 0);
            pensionFundDeduction = (rfiAmount * rfiPercentage) / 100;
          }
        }

        // Calculate total deductions (PAYE + UIF + Medical Aid + Pension Fund, no SDL for employee deductions)
        const totalDeductions = parseFloat((proratedPAYE + proratedUIF + (medicalAidEmployeeDeduction || 0) + (pensionFundDeduction || 0)).toFixed(2));

        // Calculate net pay
        const netPay = parseFloat((grossPay - totalDeductions).toFixed(2));

        console.log('🔍 Final values before database update:', {
          proratedPAYE,
          proratedUIF,
          medicalAidEmployeeDeduction,
          pensionFundDeduction,
          totalDeductions,
          netPay,
          calculation: `${proratedPAYE} + ${proratedUIF} + ${medicalAidEmployeeDeduction} + ${pensionFundDeduction} = ${totalDeductions}`
        });

        // Update PayrollPeriod with recalculated amounts (conditional session support)
        const updateOptions = useTransactions && session ? { session } : {};
        await PayrollPeriod.findByIdAndUpdate(currentPeriod._id, {
          grossPay: grossPay,
          PAYE: proratedPAYE,
          UIF: proratedUIF,
          SDL: sdl, // Store SDL for employer reporting (NOT in totalDeductions)
          totalDeductions: totalDeductions, // Excludes SDL
          netPay: netPay,
          // ✅ FIXED: Add BusinessDate proration fields (same as employeeProfile)
          proratedPercentage: proratedSalaryResult.proratedPercentage,
          workedDays: proratedSalaryResult.workedDays,
          totalDaysInPeriod: proratedSalaryResult.totalDaysInPeriod,
          isFirstPeriodWithDOA: proratedSalaryResult.isFirstPeriodWithDOA
        }, updateOptions);

        console.log('✅ PayrollPeriod recalculation completed:', {
          basicSalary: parsedSalary,
          grossPay: grossPay,
          PAYE: proratedPAYE,
          UIF: proratedUIF,
          totalDeductions: totalDeductions,
          netPay: netPay,
          proRataPercentage: proratedSalaryResult.proratedPercentage.toFixed(2) + '%'
        });

      } catch (recalcError) {
        console.error('❌ PayrollPeriod recalculation failed:', recalcError);
        console.error('❌ Recalculation error details:', {
          message: recalcError.message,
          code: recalcError.code,
          codeName: recalcError.codeName
        });
        // Continue with the save even if recalculation fails
        console.log('⚠️ Continuing with basic salary update despite recalculation error');
        console.log('📝 Basic salary and PayrollPeriod.basicSalary have been updated successfully');
        console.log('🔄 PayrollPeriod amounts will need manual recalculation');
      }


      // Create audit log entry with session
      const auditLog = new AuditLog({
        action: "UPDATE_SALARY",
        entityType: "PAYROLL",
        entityId: payroll._id,
        changes: {
          basicSalary: {
            from: oldSalary,
            to: parsedSalary,
          },
          hourlyPaid: {
            from: oldHourlyPaid,
            to: isHourlyPaid,
          },
        },
        user: req.user._id,
        company: validCompanyId,
        employee: employeeId,
      });
      // Save audit log with conditional session support
      if (useTransactions && session) {
        await auditLog.save({ session });
        // Commit the transaction to ensure all data is persisted
        await session.commitTransaction();
        console.log('✅ Transaction committed successfully');
      } else {
        await auditLog.save();
        console.log('✅ Operations completed successfully (no transaction)');
      }

      // Log the successful update

      // Handle response based on request type
      if (isJsonRequest) {
        // For AJAX requests, return JSON response with period-specific redirect
        const periodDate = currentPeriod.endDate ? new Date(currentPeriod.endDate).toISOString().split('T')[0] : null;
        const redirectUrl = periodDate
          ? `/clients/${companyCode}/employeeProfile/${employeeId}?selectedMonth=${periodDate}`
          : `/clients/${companyCode}/employeeProfile/${employeeId}`;

        res.status(200).json({
          success: true,
          message: "Basic salary updated successfully",
          data: {
            payrollId: payroll._id,
            basicSalary: parsedSalary,
            hourlyPaid: isHourlyPaid,
            periodId: currentPeriod._id,
          },
          redirectUrl: redirectUrl,
        });
      } else {
        // For traditional form submissions, use redirect with period context
        req.flash("success", "Basic salary updated successfully");
        const periodDate = currentPeriod.endDate ? new Date(currentPeriod.endDate).toISOString().split('T')[0] : null;
        const redirectUrl = periodDate
          ? `/clients/${companyCode}/employeeProfile/${employeeId}?selectedMonth=${periodDate}`
          : `/clients/${companyCode}/employeeProfile/${employeeId}`;
        res.redirect(redirectUrl);
      }
    } catch (error) {
      // Conditionally abort transaction on error
      if (useTransactions && session) {
        try {
          await session.abortTransaction();
          console.log('🔄 Transaction aborted due to error');
        } catch (abortError) {
          console.error('Error aborting transaction:', abortError.message);
        }
      }

      console.error("Error saving basic salary:", error);

      // Handle error response based on request type
      if (req.headers['content-type']?.includes('application/json')) {
        // For AJAX requests, return JSON error response
        res.status(400).json({
          success: false,
          message: error.message || "An error occurred while saving the basic salary",
          error: error.message,
        });
      } else {
        // For traditional form submissions, use redirect with flash message
        req.flash(
          "error",
          error.message || "An error occurred while saving the basic salary"
        );

        // Try to preserve period context from referrer or request
        const referrer = req.get('Referrer') || '';
        const selectedMonthMatch = referrer.match(/selectedMonth=([^&]+)/);
        const selectedMonth = selectedMonthMatch ? selectedMonthMatch[1] : null;

        const redirectUrl = selectedMonth
          ? `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}?selectedMonth=${selectedMonth}`
          : `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`;

        res.redirect(redirectUrl);
      }
    } finally {
      // Always end the session if it exists
      if (session) {
        try {
          await session.endSession();
          console.log('🔄 Database session ended');
        } catch (endError) {
          console.error('Error ending session:', endError.message);
        }
      }
    }
  }
);

router.get(
  "/:companyCode/employeeProfile/:employeeId/edit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );

      if (!company || !employee) {
        req.flash("error", "Company or Employee not found");
        return res.redirect("/clients");
      }

      // Fetch ALL pay frequencies for this company
      const payFrequencies = await PayFrequency.find({
        company: company._id,
      }).sort({ name: 1 });

      // Map frequencies to include proper ID and name
      const mappedFrequencies = payFrequencies.map((pf) => ({
        _id: pf._id,
        name:
          pf.name ||
          `${pf.frequency.charAt(0).toUpperCase() + pf.frequency.slice(1)} ${
            pf.lastDayOfPeriod.charAt(0).toUpperCase() +
            pf.lastDayOfPeriod.slice(1)
          }`,
        frequency: pf.frequency,
        lastDayOfPeriod: pf.lastDayOfPeriod,
      }));


      res.render("editEmployeeBasicInfo", {
        company,
        employee,
        payFrequencies: mappedFrequencies,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering edit employee form:", error);
      req.flash("error", "Failed to load edit employee form");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

router.post(
  "/:companyCode/employeeProfile/:employeeId/finalize",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { employeeId, currentPeriodEndDate } = req.body;
      const { companyCode } = req.params;

      // Find employee
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );
      if (!employee) {
        throw new Error("Employee not found");
      }

      // Validate employee status
      if (employee.status === "Inactive" || employee.status === "Terminated") {
        throw new Error(
          `Cannot finalize period for ${employee.status} employee`
        );
      }

      // Get current period with validation using BusinessDate for complete timezone independence
      const periodEndDateStr = BusinessDate.normalize(currentPeriodEndDate);


      // Try BusinessDate field first (preferred)
      let currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        endDateBusiness: periodEndDateStr,
        isFinalized: false,
      });

      // If BusinessDate field not found, try legacy Date field for backward compatibility
      if (!currentPeriod) {

        currentPeriod = await PayrollPeriod.findOne({
          employee: employeeId,
          endDate: BusinessDate.toDate(periodEndDateStr, true),
          isFinalized: false,
        });

        if (currentPeriod) {
        }
      } else {
      }

      if (!currentPeriod) {
        throw new Error("Period not found or already finalized");
      }

      // Get fully populated employee object
      const populatedEmployee = await Employee.findById(employeeId)
        .populate("payFrequency")
        .populate("company");

      if (!populatedEmployee) {
        throw new Error("Employee not found");
      }

      if (!populatedEmployee.payFrequency) {
        throw new Error("Employee pay frequency not configured");
      }

      // Validate period end date against termination date using BusinessDate
      if (populatedEmployee.lastDayOfService) {
        const terminationDateStr = BusinessDate.normalize(populatedEmployee.lastDayOfService);

        if (BusinessDate.isAfter(periodEndDateStr, terminationDateStr)) {
          throw new Error(
            "Cannot finalize period that ends after termination date"
          );
        }
      }

      // Validate sequential finalization order using normalized date
      const chronologicalValidation = await ChronologicalValidator.validateFinalizationOrder(
        employeeId,
        populatedEmployee.company._id,
        periodEndDateStr
      );

      if (!chronologicalValidation.isValid) {
        const errorMessage = chronologicalValidation.errors.join("; ");
        throw new Error(errorMessage);
      }

      // 🚨 CRITICAL FIX: Calculate and save PAYE, UIF, and SDL before finalizing
      // Use the same calculation logic as the employee profile to ensure consistency

      // Get payroll data for this period (ensures custom income for the correct month)
      const payroll = await Payroll.findOne({
        employee: employeeId,
        company: populatedEmployee.company._id,
        month: currentPeriod.endDate,
      });

      // Calculate custom income (same logic as employee profile)
      const customIncomeForUIF = (payroll && Array.isArray(payroll.customIncomeItems))
        ? payroll.customIncomeItems.reduce((total, item) => {
            let amount = 0;
            if (item.calculatedAmount && item.calculatedAmount > 0) {
              amount = Number(item.calculatedAmount);
            } else if (typeof item.amount === 'number') {
              amount = Number(item.amount);
            } else if (typeof item.monthlyAmount === 'number') {
              amount = Number(item.monthlyAmount);
            }
            return total + amount;
          }, 0)
        : 0;

      // Calculate YTD PAYE
      const ytdPAYEResult = await calculateYTDPAYEForRoute(
        employeeId,
        currentPeriod.endDate,
        populatedEmployee.payFrequency.frequency,
        populatedEmployee.age || 35
      );

      console.log('💰 YTD PAYE calculation result:', ytdPAYEResult.currentPAYE);
      console.log('💰 Current period PAYE (prorated):', currentPeriod.PAYE);

      // Calculate prorated amounts
      const proRataPercentage = Number(currentPeriod.proratedPercentage || 100);
      const basicSalary = Number(currentPeriod.basicSalary || 0);
      const proratedBasicSalary = (basicSalary * proRataPercentage) / 100;

      // Calculate PAYE (including custom income if present)
      let finalPAYEToSave = ytdPAYEResult.currentPAYE;

      if (customIncomeForUIF > 0) {
        const periodsPerYear = ytdPAYEResult.frequencyMultiplier || 12;
        const customIncomeAnnualized = customIncomeForUIF * periodsPerYear;
        const adjustedAnnualSalary = ytdPAYEResult.annualizedIncome + customIncomeAnnualized;

        const adjustedPAYEResult = await payrollCalculations.calculateEnhancedPAYE({
          annualSalary: adjustedAnnualSalary,
          age: populatedEmployee.age || 35,
          frequency: populatedEmployee.payFrequency.frequency,
          periodEndDate: currentPeriod.endDate
        });

        finalPAYEToSave = adjustedPAYEResult.periodPAYE || adjustedPAYEResult.monthlyPAYE || 0;
      }

      // Apply proration to PAYE
      const proratedPAYE = (finalPAYEToSave * proRataPercentage) / 100;

      // Check if this period is prorated
      const isProrated = currentPeriod.proratedPercentage && currentPeriod.proratedPercentage < 100;

      if (isProrated && currentPeriod.PAYE > 0) {
        console.log('⚠️ Period is prorated, preserving existing prorated PAYE:', currentPeriod.PAYE);
        finalPAYEToSave = currentPeriod.PAYE; // Keep the prorated amount
      } else {
        finalPAYEToSave = proratedPAYE; // Use calculated prorated PAYE
      }

      // Calculate UIF (including custom income)
      const uifBase = proratedBasicSalary + customIncomeForUIF;
      const finalUIFToSave = Math.min(uifBase * 0.01, 177.12);

      // Calculate SDL (1% of gross pay)
      const finalSDLToSave = proratedBasicSalary * 0.01;

      console.log('=== FINALIZATION CALCULATIONS ===');
      console.log('Basic salary:', basicSalary);
      console.log('Prorated basic salary:', proratedBasicSalary);
      console.log('Custom income for UIF:', customIncomeForUIF);
      console.log('UIF base:', uifBase);
      console.log('Final PAYE to save:', finalPAYEToSave);
      console.log('Final UIF to save:', finalUIFToSave);
      console.log('Final SDL to save:', finalSDLToSave);

      // Finalize current period and save all calculated values
      currentPeriod.isFinalized = true;
      currentPeriod.finalizedAt = new Date();
      currentPeriod.PAYE = finalPAYEToSave;
      currentPeriod.UIF = finalUIFToSave;
      currentPeriod.SDL = finalSDLToSave;
      currentPeriod.grossPay = proratedBasicSalary + customIncomeForUIF;

      // Copy pay components from Payroll to PayrollPeriod for complete data storage
      if (payroll) {
        console.log('📋 Copying pay components from Payroll to PayrollPeriod...');

        // Copy commission
        currentPeriod.commission = payroll.commission || 0;
        currentPeriod.commissionEnabled = payroll.commissionEnabled || false;

        // Copy loss of income
        currentPeriod.lossOfIncome = payroll.lossOfIncome || 0;
        currentPeriod.lossOfIncomeEnabled = payroll.lossOfIncomeEnabled || false;

        // Copy accommodation benefit
        currentPeriod.accommodationBenefit = payroll.accommodationBenefit || 0;

        // Copy custom income items (the key missing piece!)
        if (payroll.customIncomeItems && payroll.customIncomeItems.length > 0) {
          currentPeriod.customIncomeItems = payroll.customIncomeItems.map(item => ({
            customIncomeId: item.customIncomeId,
            name: item.name,
            inputType: item.inputType,
            amount: item.amount,
            percentage: item.percentage,
            quantity: item.quantity,
            hoursWorkedFactor: item.hoursWorkedFactor,
            customRate: item.customRate,
            monthlyAmount: item.monthlyAmount,
            incomeItems: item.incomeItems,
            enableProRata: item.enableProRata,
            differentRateForEveryEmployee: item.differentRateForEveryEmployee,
            calculatedAmount: item.calculatedAmount,
            createdAt: item.createdAt
          }));
          console.log('✅ Copied custom income items:', currentPeriod.customIncomeItems.length);
        } else {
          currentPeriod.customIncomeItems = [];
        }
      }

      // Persist full calculations snapshot (mirrors Employee Profile logic)
      try {
        const calc = await PayrollService.calculatePayrollTotals(
          employeeId,
          currentPeriod.endDate
        );
        currentPeriod.calculations = calc;
      } catch (e) {
        console.warn('Could not compute period calculations snapshot:', e?.message || e);
      }
      await currentPeriod.save();

      console.log('✅ Period finalized with values:', {
        PAYE: currentPeriod.PAYE,
        UIF: currentPeriod.UIF,
        SDL: currentPeriod.SDL,
        grossPay: currentPeriod.grossPay
      });

      // Generate next period with comprehensive termination checks using BusinessDate
      console.log('🔍 Checking if next period can be generated...');
      console.log('Employee status:', populatedEmployee.status);
      console.log('Last day of service:', populatedEmployee.lastDayOfService);
      console.log('Period end date:', periodEndDateStr);

      const canGenerateNext =
        populatedEmployee.status !== "Serving Notice" ||
        (populatedEmployee.lastDayOfService &&
          BusinessDate.isBefore(
            periodEndDateStr,
            BusinessDate.normalize(populatedEmployee.lastDayOfService)
          ));

      console.log('Can generate next period:', canGenerateNext);

      if (canGenerateNext) {
        try {
          console.log('🔄 Generating next period after finalization...');
          const nextPeriod = await PayrollService.generateNextPeriod(
            populatedEmployee,
            currentPeriod
          );

          if (nextPeriod) {
            console.log('✅ Next period generated successfully:', {
              periodId: nextPeriod._id,
              startDate: nextPeriod.startDateBusiness || nextPeriod.startDate,
              endDate: nextPeriod.endDateBusiness || nextPeriod.endDate,
              basicSalary: nextPeriod.basicSalary
            });
          } else {
            console.log('⚠️ No next period was generated (may be at end of employment)');
          }
        } catch (error) {
          console.error("Error generating next period:", error);
          // Don't throw here - finalization was successful, just log the error
          console.log('⚠️ Period finalized successfully but next period generation failed');
        }
      } else {
        console.log('⚠️ Cannot generate next period - employee status or termination prevents it');
      }

      req.flash("success", "Period finalized successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      console.error("Error finalizing period:", error);
      req.flash("error", error.message || "Failed to finalize period");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// NEW ROUTE: Unfinalize payroll period
router.post(
  "/:companyCode/employeeProfile/:employeeId/unfinalize",
  ensureAuthenticated,
  async (req, res) => {
    try {
      console.log("=== UNFINALIZE POST ROUTE CALLED ===");
      const { employeeId, currentPeriodEndDate } = req.body;
      const { companyCode } = req.params;
      console.log("Request body:", req.body);
      console.log("Route params:", { companyCode, employeeId: req.params.employeeId });

      // Find company
      console.log("Looking for company with code:", companyCode);
      const company = await Company.findOne({ companyCode });
      if (!company) {
        console.log("Company not found");
        throw new Error("Company not found");
      }
      console.log("Company found:", company._id);

      // Find employee
      console.log("Looking for employee with ID:", employeeId);
      const employee = await Employee.findById(employeeId).populate("payFrequency");
      if (!employee) {
        console.log("Employee not found");
        throw new Error("Employee not found");
      }
      console.log("Employee found:", employee.name);

      // Validate employee belongs to company
      console.log("Validating employee belongs to company...");
      if (employee.company.toString() !== company._id.toString()) {
        console.log("Employee does not belong to this company");
        throw new Error("Employee does not belong to this company");
      }
      console.log("Employee validation passed");

      // Use UnfinalizationService for validation and processing
      console.log("Loading UnfinalizationService...");
      const UnfinalizationService = require("../services/UnfinalizationService");

      // Validate unfinalization
      console.log("Validating unfinalization with:", {
        employeeId,
        companyId: company._id,
        currentPeriodEndDate
      });
      const validation = await UnfinalizationService.validateUnfinalization(
        employeeId,
        company._id,
        currentPeriodEndDate
      );
      console.log("Validation result:", validation);

      if (!validation.isValid) {
        console.log("Validation failed, redirecting with error");
        req.flash("error", validation.errors.join("; "));
        return res.redirect(
          `/clients/${companyCode}/employeeProfile/${employeeId}`
        );
      }

      // Perform unfinalization
      console.log("Performing unfinalization...");
      const result = await UnfinalizationService.unfinalizePeriod(
        employeeId,
        company._id,
        currentPeriodEndDate,
        req.user._id
      );
      console.log("Unfinalization result:", result);

      if (!result.success) {
        console.log("Unfinalization failed:", result.message);
        req.flash("error", result.message);
        return res.redirect(
          `/clients/${companyCode}/employeeProfile/${employeeId}`
        );
      }

      console.log("Unfinalization successful!");

      // Show warnings if any
      if (result.warnings && result.warnings.length > 0) {
        console.log("Showing warnings:", result.warnings);
        result.warnings.forEach(warning => {
          req.flash("warning", warning);
        });
      }

      console.log("Setting success flash message and redirecting...");
      req.flash("success", "Period unfinalized successfully");

      // Redirect to the specific period that was unfinalized so user can make changes
      const unfinalizedPeriodDate = new Date(currentPeriodEndDate).toISOString().split('T')[0];
      console.log("Redirecting to unfinalized period:", unfinalizedPeriodDate);
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}?selectedMonth=${unfinalizedPeriodDate}`);
    } catch (error) {
      console.error("Error unfinalizing period:", error);
      console.error("Error stack:", error.stack);
      req.flash("error", error.message || "Failed to unfinalize period");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// NEW ROUTE: Check if period can be unfinalized (API endpoint)
router.get(
  "/:companyCode/employeeProfile/:employeeId/period/:periodEndDate/can-unfinalize",
  ensureAuthenticated,
  async (req, res) => {
    try {
      console.log("=== CAN-UNFINALIZE ROUTE CALLED ===");
      const { employeeId, periodEndDate, companyCode } = req.params;
      console.log("Route params:", { employeeId, periodEndDate, companyCode });

      // Find company
      console.log("Looking for company with code:", companyCode);
      const company = await Company.findOne({ companyCode });
      if (!company) {
        console.log("Company not found");
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }
      console.log("Company found:", company._id);

      // Use UnfinalizationService for validation
      console.log("Loading UnfinalizationService...");
      const UnfinalizationService = require("../services/UnfinalizationService");
      console.log("UnfinalizationService loaded successfully");

      console.log("Calling validateUnfinalization with:", {
        employeeId,
        companyId: company._id,
        periodEndDate
      });

      const validation = await UnfinalizationService.validateUnfinalization(
        employeeId,
        company._id,
        periodEndDate
      );

      console.log("Validation result:", validation);

      res.json({
        success: true,
        canUnfinalize: validation.isValid,
        errors: validation.errors,
        warnings: validation.warnings,
      });
    } catch (error) {
      console.error("Error checking unfinalization eligibility:", error);
      console.error("Error stack:", error.stack);
      res.status(500).json({
        success: false,
        message: "Failed to check unfinalization eligibility",
        error: error.message,
      });
    }
  }
);

// NEW ROUTE: Get affected periods for unfinalization (API endpoint)
router.get(
  "/:companyCode/employeeProfile/:employeeId/period/:periodEndDate/affected-periods",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { employeeId, periodEndDate, companyCode } = req.params;

      // Find company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Get affected periods
      const UnfinalizationService = require("../services/UnfinalizationService");
      const ChronologicalValidator = require("../utils/ChronologicalValidator");

      const affectedPeriods = await UnfinalizationService.getAffectedPeriods(
        employeeId,
        company._id,
        periodEndDate
      );

      const impact = await ChronologicalValidator.analyzeUnfinalizationImpact(
        employeeId,
        company._id,
        periodEndDate
      );

      res.json({
        success: true,
        affectedPeriods: affectedPeriods,
        impact: impact,
      });
    } catch (error) {
      console.error("Error getting affected periods:", error);
      res.status(500).json({
        success: false,
        message: "Failed to get affected periods",
        error: error.message,
      });
    }
  }
);

// NEW ROUTE: Check if period can be finalized (API endpoint)
router.get(
  "/:companyCode/employeeProfile/:employeeId/debug-periods",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { employeeId, companyCode } = req.params;

      // Get company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({ error: "Company not found" });
      }

      // Get all periods for this employee
      const allPeriods = await PayrollPeriod.find({
        employee: employeeId,
        company: company._id
      }).sort({ endDate: 1 }).lean();

      const debugInfo = {
        employeeId,
        companyId: company._id,
        companyCode,
        totalPeriods: allPeriods.length,
        periods: allPeriods.map(p => ({
          id: p._id,
          startDate: p.startDate.toISOString(),
          endDate: p.endDate.toISOString(),
          startDateFormatted: new Date(p.startDate).toISOString().split('T')[0],
          endDateFormatted: new Date(p.endDate).toISOString().split('T')[0],
          isFinalized: p.isFinalized,
          frequency: p.frequency,
          basicSalary: p.basicSalary
        }))
      };

      res.json(debugInfo);
    } catch (error) {
      console.error("Error in debug-periods route:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

router.get(
  "/:companyCode/employeeProfile/:employeeId/period/:periodEndDate/can-finalize",
  ensureAuthenticated,
  async (req, res) => {
    // Prevent caching to avoid 304 responses
    res.set({
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    });

    try {
      const { employeeId, periodEndDate, companyCode } = req.params;


      // Find company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Find employee
      const employee = await Employee.findById(employeeId).populate("payFrequency");
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Validate employee status
      if (employee.status === "Inactive" || employee.status === "Terminated") {
        return res.json({
          success: true,
          canFinalize: false,
          errors: [`Cannot finalize period for ${employee.status} employee`],
          warnings: [],
        });
      }

      // Check if period exists and is not already finalized
      // Use BusinessDate for complete timezone independence
      const periodEndDateStr = BusinessDate.normalize(periodEndDate);

      // The PayrollPeriod model automatically converts YYYY-MM-DD strings to T23:59:59.999Z
      // So we need to query using the exact same format that would be stored
      const queryEndDate = new Date(periodEndDateStr + 'T23:59:59.999Z');


      // First, let's see what PayrollPeriod records actually exist for this employee
      const allEmployeePeriods = await PayrollPeriod.find({
        employee: employeeId,
        company: company._id
      }).sort({ endDate: 1 }).lean();


      // Try to find the specific period using BusinessDate field first (preferred)
      let currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        company: company._id,
        endDateBusiness: periodEndDateStr,
        isFinalized: false,
      });

      // If BusinessDate field not found, try legacy Date field for backward compatibility
      if (!currentPeriod) {

        currentPeriod = await PayrollPeriod.findOne({
          employee: employeeId,
          company: company._id,
          endDate: BusinessDate.toDate(periodEndDateStr, true),
          isFinalized: false,
        });

        if (currentPeriod) {
        }
      } else {
      }


      // If exact match fails, try a range query to see if there's a close match
      if (!currentPeriod) {
        const dayBefore = new Date(queryEndDate.getTime() - 24 * 60 * 60 * 1000);
        const dayAfter = new Date(queryEndDate.getTime() + 24 * 60 * 60 * 1000);

        const nearbyPeriods = await PayrollPeriod.find({
          employee: employeeId,
          company: company._id,
          endDate: {
            $gte: dayBefore,
            $lte: dayAfter
          },
          isFinalized: false,
        }).lean();

      }

      if (!currentPeriod) {
        // Try to find any period with this end date, regardless of finalization status
        // Use date range to handle timezone discrepancies
        const startOfDay = new Date(periodEndDateStr + 'T00:00:00.000Z');
        const endOfDay = new Date(periodEndDateStr + 'T23:59:59.999Z');

        const anyPeriod = await PayrollPeriod.findOne({
          employee: employeeId,
          company: company._id,
          endDate: {
            $gte: startOfDay,
            $lte: endOfDay
          }
        });

        if (anyPeriod) {

          return res.json({
            success: true,
            canFinalize: false,
            errors: [`Period ending ${periodEndDateStr} is already finalized`],
            warnings: [],
          });
        }

        return res.json({
          success: true,
          canFinalize: false,
          errors: ["Period not found or already finalized"],
          warnings: [],
          debug: {
            searchedEndDate: queryEndDate.toISOString(),
            employeeId,
            companyId: company._id,
            totalPeriodsForEmployee: allEmployeePeriods.length
          }
        });
      }

      // Use ChronologicalValidator for sequential validation with timezone-independent date
      const validation = await ChronologicalValidator.validateFinalizationOrder(
        employeeId,
        company._id,
        periodEndDateStr
      );


      res.json({
        success: true,
        canFinalize: validation.isValid,
        errors: validation.errors,
        warnings: validation.warnings,
      });
    } catch (error) {
      console.error("Error checking finalization eligibility:", error);
      res.status(500).json({
        success: false,
        message: "Error checking finalization eligibility",
      });
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/addNewEmployeeDetails",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      // Fetch ALL pay frequencies for this company
      const payFrequencies = await PayFrequency.find({
        company: company._id,
      }).sort({ name: 1 });


      // Fetch employee number settings
      const employeeNumberSettings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      // Create an empty employee object for the template
      const employee = {
        bank: "",
        accountNumber: "",
        accountType: "",
        branchCode: "",
        accountHolder: "",
      };

      res.render("addNewEmployeeDetails", {
        company,
        companyCode,
        payFrequencies,
        employeeNumberSettings,
        employee, // Pass the empty employee object
        user: req.user,
        title: "Add New Employee",
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering add employee form:", error);
      req.flash("error", "Failed to load add employee form");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.post(
  "/:companyCode/employeeManagement/addNew/submit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Get employee number settings
      const settings = await EmployeeNumberSettings.findOne({
        company: company._id,
      });

      // Handle employee number generation/validation
      let companyEmployeeNumber;

      // Default to automatic mode if no settings exist or mode is not explicitly set to manual
      const isManualMode = settings?.mode === "manual";

      if (!isManualMode) {
        // Automatic mode (default when no settings exist)
        try {
          // Use centralized, pattern-aware generator (atomic, validates uniqueness)
          companyEmployeeNumber = await EmployeeNumberService.generateEmployeeNumber(company._id);
        } catch (error) {
          console.error("Error generating employee number:", error);
          return res.status(400).json({
            success: false,
            message: "Failed to generate employee number: " + error.message,
          });
        }
      } else {
        // Manual mode - require employee number from form
        companyEmployeeNumber = req.body.companyEmployeeNumber;
        if (!companyEmployeeNumber) {
          return res.status(400).json({
            success: false,
            message: "Employee number is required in manual mode",
          });
        }

        const existingEmployee = await Employee.findOne({
          company: company._id,
          companyEmployeeNumber: req.body.companyEmployeeNumber,
        });

        if (existingEmployee) {
          return res.status(400).json({
            success: false,
            message: "This employee number is already in use for this company",
          });
        }
      }

      // Get the selected pay frequency
      const payFrequency = await PayFrequency.findById(req.body.payFrequency);
      if (!payFrequency) {
        throw new Error("Selected pay frequency not found");
      }

      // Calculate the appropriate first payroll period end date
      const doa = moment(req.body.doa).startOf("day");
      let firstPayrollPeriodEndDate;


      if (payFrequency.frequency === "weekly") {
        // Map day names to numbers (0 = Sunday, 6 = Saturday)
        const daysOfWeek = {
          sunday: 0,
          monday: 1,
          tuesday: 2,
          wednesday: 3,
          thursday: 4,
          friday: 5,
          saturday: 6,
        };

        // Get the target day number (e.g., 3 for Wednesday)
        const targetDay =
          daysOfWeek[payFrequency.lastDayOfPeriod.toLowerCase()];
        const currentDay = doa.day();

        // Calculate days to add to reach the next target day
        let daysToAdd = (targetDay - currentDay + 7) % 7;

        // If we're already on the target day, use today as the end date
        if (daysToAdd === 0) {
          firstPayrollPeriodEndDate = doa.clone().endOf("day").toDate();
        } else {
          // If we're past the target day, go to next week's target day
          if (currentDay > targetDay) {
            daysToAdd = 7 - (currentDay - targetDay);
          }
          firstPayrollPeriodEndDate = doa
            .clone()
            .add(daysToAdd, "days")
            .endOf("day")
            .toDate();
        }

      } else if (payFrequency.frequency === "monthly") {
        // SURGICAL FIX: Use firstPayrollPeriodEndDate unless DOA is after it
        const configuredFirstPeriodEnd = moment(payFrequency.firstPayrollPeriodEndDate);
        const shouldUseDOA = doa.isAfter(configuredFirstPeriodEnd);

        if (shouldUseDOA) {
          // DOA is after configured date, calculate from DOA
          if (payFrequency.lastDayOfPeriod === "monthend") {
            firstPayrollPeriodEndDate = doa.clone().endOf("month").toDate();
          } else {
            firstPayrollPeriodEndDate = doa
              .clone()
              .date(parseInt(payFrequency.lastDayOfPeriod))
              .endOf("day")
              .toDate();
          }
        } else {
          // Use the configured first payroll period end date
          firstPayrollPeriodEndDate = configuredFirstPeriodEnd.toDate();
        }
      } else {
        // biweekly
        firstPayrollPeriodEndDate = doa
          .clone()
          .add(13, "days")
          .endOf("day")
          .toDate();
      }


      // Create employee data object with proper structure
      const employeeData = {
        firstName: req.body.firstName,
        lastName: req.body.lastName,
        email: req.body.email,
        phone: req.body.phone, // Add phone field
        dob: req.body.dob ? moment.utc(req.body.dob).startOf('day').toDate() : null,
        passportNumber: req.body.idType === 'passport' ? req.body.passportNumber : undefined,
        paymentMethod: req.body.paymentMethod, // Use value directly from form
        companyEmployeeNumber,
        globalEmployeeId: uuidv4(),
        payFrequency: payFrequency._id,
        lastDayOfPeriod: payFrequency.lastDayOfPeriod,
        doa: req.body.doa,
        firstPayrollPeriodEndDate,
        jobTitle: req.body.jobTitle,
        department: req.body.department,
        costCentre: req.body.costCentre,
        isDirector: req.body.isDirector === true,
        typeOfDirector: req.body.isDirector ? req.body.typeOfDirector : null,
        workingHours: req.body.workingHours === "Part Time" ? "Less Than 22 Hours a Week" : "Full Time",
        incomeTaxNumber: req.body.incomeTaxNumber || undefined,

        // Personal Details
        personalDetails: {
          idType: req.body.idType === 'none' ? 'other' : (req.body.idType || 'rsa'),
          idNumber: req.body.idType === 'none' ? undefined : req.body.idNumber,
          mobileNumber: req.body.personalDetails?.mobileNumber || req.body['personalDetails.mobileNumber'] || null,
          nationality: "South African",
          countryOfBirth: "South Africa",
        },

        // Payment Details
        paymentMethod: req.body.paymentMethod?.toLowerCase() || 'eft', // Fix case sensitivity but keep default

        // Address Details (Unified)
        unitNumber: req.body.unitNumber,
        complex: req.body.complex,
        streetNumber: req.body.streetNumber,
        street: req.body.street,
        suburbOrDistrict: req.body.suburbOrDistrict,
        cityOrTown: req.body.cityOrTown,
        code: req.body.code,
        country: req.body.country || 'South Africa',

        // Regular Hours
        regularHours: req.body.regularHours,

        // Company Reference
        company: company._id,
      };

      // Bank Details - save directly to employee fields (matching editEmployeeBasicInfo.ejs)
      if (req.body.bankName?.trim()) {
        employeeData.bank = req.body.bankName.trim();
      }
      if (req.body.accountType?.trim()) {
        employeeData.accountType = req.body.accountType.trim();
      }
      if (req.body.accountNumber?.trim()) {
        employeeData.accountNumber = req.body.accountNumber.trim();
      }
      if (req.body.branchCode?.trim()) {
        employeeData.branchCode = req.body.branchCode.trim();
      }
      if (req.body.accountHolder?.trim()) {
        employeeData.accountHolder = req.body.accountHolder.trim();
      }

      // Create and save the new employee
      const newEmployee = new Employee(employeeData);
      await newEmployee.save();

      // Update settings if in automatic mode
      if (settings?.mode === "automatic") {
        settings.lastGeneratedNumber = companyEmployeeNumber;
        await settings.save();
      }

      // Add delay to ensure database operations are fully committed
      await new Promise(resolve => setTimeout(resolve, 150));

      const timestamp = Date.now();
      res.json({
        success: true,
        message: "Employee added successfully",
        redirectUrl: `/clients/${companyCode}/employeeManagement?refresh=${timestamp}&action=add`,
        employeeId: newEmployee._id,
        timestamp: timestamp
      });
    } catch (error) {
      console.error("Error creating employee:", error);
      res.status(400).json({
        success: false,
        message: error.message || "Failed to create employee",
      });
    }
  }
);

router.post(
  "/:companyCode/employeeProfile/:employeeId/edit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const employeeData = req.body;


      // Validate company and employee
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Process dates - handle empty strings as null
      if (employeeData.dob && employeeData.dob.trim() !== '') {
        employeeData.dob = moment.utc(employeeData.dob).startOf("day").toDate();
      } else {
        employeeData.dob = null;
      }

      if (employeeData.doa && employeeData.doa.trim() !== '') {
        employeeData.doa = moment.utc(employeeData.doa).startOf("day").toDate();
        employeeData.endOfMonthDate = moment
          .utc(employeeData.doa)
          .endOf("month")
          .toDate();
      } else {
        employeeData.doa = null;
      }

      // Create update object with all fields
      const updateData = {
        firstName: employeeData.firstName,
        lastName: employeeData.lastName,
        dob: employeeData.dob,
        idType: employeeData.idType,
        idNumber:
          employeeData.idType === "none" ? undefined : employeeData.idNumber,
        passportNumber: employeeData.passportNumber,
        jobTitle: employeeData.jobTitle,
        department: employeeData.department, // Explicitly include department
        costCentre: employeeData.costCentre,
        payFrequency: employeeData.payFrequency,
        doa: employeeData.doa,
        endOfMonthDate: employeeData.endOfMonthDate, // Include endOfMonthDate if it was calculated
        paymentMethod: employeeData.paymentMethod,
        bank: employeeData.bank,
        accountType: employeeData.accountType,
        accountNumber: employeeData.accountNumber,
        branchCode: employeeData.branchCode,
        holderRelationship: employeeData.holderRelationship,
        incomeTaxNumber: employeeData.incomeTaxNumber,
        // Address Details (Unified)
        unitNumber: employeeData.unitNumber,
        complex: employeeData.complex,
        streetNumber: employeeData.streetNumber,
        street: employeeData.street,
        suburbOrDistrict: employeeData.suburbOrDistrict,
        cityOrTown: employeeData.cityOrTown,
        code: employeeData.code,
        country: employeeData.country,
        email: employeeData.email,
        company: company._id, // Ensure company is set
      };

      // Debug: Log the initial updateData creation

      // Debug: Log the entire request body

      // Initialize personalDetails as an object to ensure proper structure
      updateData.personalDetails = updateData.personalDetails || {};

      // Directly set mobile number if present in personalDetails
      if (employeeData.personalDetails && employeeData.personalDetails.mobileNumber !== undefined) {
        updateData.personalDetails.mobileNumber = employeeData.personalDetails.mobileNumber || undefined;
      }

      // Handle idType and related fields
      if (employeeData.idType) {
        updateData.personalDetails.idType = employeeData.idType;
        // Also update root level for backward compatibility
        updateData.idType = employeeData.idType;

        // Only include ID number or passport number if idType is not 'none'
        if (employeeData.idType !== 'none') {
          if (employeeData.idType === 'rsa' && employeeData.idNumber) {
            updateData.personalDetails.idNumber = employeeData.idNumber;
            updateData.idNumber = employeeData.idNumber; // Backward compatibility
          } else if (employeeData.idType === 'passport' && employeeData.passportNumber) {
            updateData.personalDetails.passportNumber = employeeData.passportNumber;
            updateData.passportNumber = employeeData.passportNumber; // Backward compatibility
          }
        } else {
          // Clear ID-related fields when idType is 'none'
          updateData.personalDetails.idNumber = undefined;
          updateData.personalDetails.passportNumber = undefined;
          updateData.idNumber = undefined;
          updateData.passportNumber = undefined;
        }
      }

      // Handle mobile number in different possible formats
      if (employeeData['personalDetails.mobileNumber'] !== undefined) {
        updateData.personalDetails = updateData.personalDetails || {};
        updateData.personalDetails.mobileNumber = employeeData['personalDetails.mobileNumber'] || undefined;
      } else if (employeeData.personalDetails && employeeData.personalDetails.mobileNumber) {
        updateData.personalDetails = updateData.personalDetails || {};
        updateData.personalDetails.mobileNumber = employeeData.personalDetails.mobileNumber || undefined;
      } else if (employeeData.mobileNumber) {
        updateData.personalDetails = updateData.personalDetails || {};
        updateData.personalDetails.mobileNumber = employeeData.mobileNumber || undefined;
      } else {
        updateData.personalDetails = updateData.personalDetails || {};
        updateData.personalDetails.mobileNumber = undefined;
      }

      // Debug: Log the update data before saving

      // Clean up the update data
      const cleanObject = (obj) => {
        Object.keys(obj).forEach(key => {
          if (obj[key] === undefined || obj[key] === '') {
            delete obj[key];
          } else if (typeof obj[key] === 'object' && obj[key] !== null && !(obj[key] instanceof Date)) {
            cleanObject(obj[key]);
            // Remove empty objects
            if (Object.keys(obj[key]).length === 0) {
              delete obj[key];
            }
          }
        });
        return obj;
      };

      // Debug: Log update data before cleaning

      // Clean the update data and personalDetails
      cleanObject(updateData);
      if (updateData.personalDetails) {
        cleanObject(updateData.personalDetails);
      }

      // Debug: Log update data after cleaning

      // Debug: Log the update operation details

      // Update employee data
      const updatedEmployee = await Employee.findOneAndUpdate(
        { _id: employeeId, company: company._id },
        { $set: updateData },
        {
          new: true,
          runValidators: true,
          // Add this to include all fields in the returned document
          projection: { __v: 0 } // Exclude version key
        }
      );

      if (!updatedEmployee) {
        console.error('Employee not found:', { employeeId, company: company._id });
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Debug: Log the updated employee document

      // Verify the mobile number was saved correctly
      const freshEmployee = await Employee.findById(employeeId).lean();

      // Add delay to ensure database operations are fully committed
      await new Promise(resolve => setTimeout(resolve, 150));

      // Send success response with the success flag
      const timestamp = Date.now();
      return res.json({
        success: true,
        message: "Employee updated successfully",
        data: updatedEmployee,
        redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}?refresh=${timestamp}&action=edit`,
        timestamp: timestamp
      });
    } catch (error) {
      console.error("Error updating employee:", error);
      return res.status(500).json({
        success: false,
        message: error.message || "Error updating employee",
      });
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/selfService",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Update the select fields to include companyEmployeeNumber
      const employees = await Employee.find({ company: company._id })
        .select(
          "firstName lastName email selfServiceEnabled lastActivity companyEmployeeNumber"
        )
        .sort({ lastName: 1, firstName: 1 });

      res.render("selfServiceTab", {
        companyCode,
        company,
        employees,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading self service page:", error);
      req.flash("error", "Failed to load self service page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/bulkActions",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      const employees = await Employee.find({ company: company._id });

      res.render("bulkActionsTab", {
        company,
        employees,
        user: req.user,
        companyCode,
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading bulk actions page:", error);
      res
        .status(500)
        .send("An error occurred while loading the bulk actions page");
    }
  }
);

// Bulk End Service Route
router.get(
  "/:companyCode/employeeManagement/end-service",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      // Get active employees only (not terminated)
      const employees = await Employee.find({
        company: company._id,
        employmentStatus: true  // Boolean field: true = active, false = terminated
      }).select('firstName lastName companyEmployeeNumber status');

      res.render("bulk-end-service", {
        company,
        employees,
        user: req.user,
        companyCode,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading bulk end service page:", error);
      res
        .status(500)
        .send("An error occurred while loading the bulk end service page");
    }
  }
);

// Helper function to get termination reason from UIF code
function getTerminationReasonFromCode(code) {
  const reasons = {
    1: "Resignation",
    2: "Dismissal",
    3: "Retrenchment",
    4: "Death",
    5: "Retirement",
    6: "Medical Incapacity",
    7: "Contract Expiry",
    8: "Other"
  };
  return reasons[code] || "Unknown reason";
}

// Bulk End Service POST Route
router.post(
  "/:companyCode/employeeManagement/end-service",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const { employeeIds, lastDayOfService, uifStatusCode, temporaryAbsence } = req.body;

      // Enhanced validation
      if (!employeeIds || employeeIds.length === 0) {
        req.flash("error", "No employees selected for end service");
        return res.redirect(`/clients/${companyCode}/employeeManagement/end-service`);
      }

      // Validate batch size (prevent system overload)
      if (employeeIds.length > 50) {
        req.flash("error", "Cannot process more than 50 employees at once. Please select fewer employees.");
        return res.redirect(`/clients/${companyCode}/employeeManagement/end-service`);
      }

      if (!lastDayOfService) {
        req.flash("error", "Last day of service is required");
        return res.redirect(`/clients/${companyCode}/employeeManagement/end-service`);
      }

      if (!uifStatusCode) {
        req.flash("error", "UIF Status Code is required");
        return res.redirect(`/clients/${companyCode}/employeeManagement/end-service`);
      }

      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      const today = new Date();
      const lastDay = new Date(lastDayOfService);

      // Validate termination date is not in the past (allow today)
      if (lastDay < today.setHours(0, 0, 0, 0)) {
        req.flash("error", "Last day of service cannot be in the past");
        return res.redirect(`/clients/${companyCode}/employeeManagement/end-service`);
      }

      let successCount = 0;
      let errorCount = 0;
      const errors = [];
      const successfulEmployees = [];
      const failedEmployees = [];

      // Start a session for transaction management
      const session = await Employee.startSession();

      try {
        // Start transaction
        await session.startTransaction();

        // Pre-validation: Check all employees exist and are valid for termination
        const employeeValidationResults = [];
        for (const employeeId of employeeIds) {
          try {
            const employee = await Employee.findById(employeeId).session(session);
            if (!employee) {
              employeeValidationResults.push({
                id: employeeId,
                valid: false,
                reason: "Employee not found"
              });
              continue;
            }

            // Validate employee belongs to the company
            if (employee.company.toString() !== company._id.toString()) {
              employeeValidationResults.push({
                id: employeeId,
                employee,
                valid: false,
                reason: "Employee does not belong to this company"
              });
              continue;
            }

            // Validate employee is not already terminated
            if (employee.employmentStatus === false || employee.status === "Inactive") {
              employeeValidationResults.push({
                id: employeeId,
                employee,
                valid: false,
                reason: "Employee is already terminated"
              });
              continue;
            }

            // Validate termination date is not before DOA
            const doa = new Date(employee.doa || employee.dateOfAppointment);
            if (lastDay < doa) {
              employeeValidationResults.push({
                id: employeeId,
                employee,
                valid: false,
                reason: "Termination date is before date of appointment"
              });
              continue;
            }

            employeeValidationResults.push({
              id: employeeId,
              employee,
              valid: true
            });

          } catch (error) {
            employeeValidationResults.push({
              id: employeeId,
              valid: false,
              reason: `Validation error: ${error.message}`
            });
          }
        }

        // Check if any employees are valid for processing
        const validEmployees = employeeValidationResults.filter(result => result.valid);
        const invalidEmployees = employeeValidationResults.filter(result => !result.valid);

        if (validEmployees.length === 0) {
          await session.abortTransaction();

          // Add all invalid employee errors
          invalidEmployees.forEach(result => {
            const employeeName = result.employee ?
              `${result.employee.firstName} ${result.employee.lastName}` :
              `ID: ${result.id}`;
            errors.push(`${employeeName}: ${result.reason}`);
            failedEmployees.push({
              id: result.id,
              name: employeeName,
              reason: result.reason
            });
          });

          req.flash("error", `No employees could be processed. ${errors.join('; ')}`);
          return res.redirect(`/clients/${companyCode}/employeeManagement/end-service`);
        }

        // Process valid employees within the transaction
        for (const result of validEmployees) {
          const { employee, id: employeeId } = result;

          try {
            // Create audit log entry
            const auditEntry = {
              action: 'BULK_END_SERVICE',
              employeeId: employeeId,
              employeeName: `${employee.firstName} ${employee.lastName}`,
              performedBy: req.user._id,
              performedAt: new Date(),
              details: {
                lastDayOfService: lastDay,
                uifStatusCode: uifStatusCode,
                terminationReason: getTerminationReasonFromCode(uifStatusCode),
                temporaryAbsence: temporaryAbsence === 'on'
              }
            };

            // Prepare updates following single employee end service logic
            const updates = {
              status: lastDay > today ? "Serving Notice" : "Serving Notice", // Keep as "Serving Notice" initially
              terminationNoticeDate: today,
              lastDayOfService: lastDay,
              uifStatusCode: uifStatusCode,
              terminationReason: getTerminationReasonFromCode(uifStatusCode)
            };

            // Add temporary absence details if applicable
            if (temporaryAbsence === 'on') {
              updates.temporaryAbsence = {
                isTemporary: true,
                expectedReturnDate: null, // Can be set later
                isPaid: false // Default to unpaid
              };
            }

            // Update employee with validation disabled (matching single employee logic)
            const updatedEmployee = await Employee.findByIdAndUpdate(
              employeeId,
              { $set: updates },
              {
                new: true,
                runValidators: false, // Disable validation for this update
                session: session // Use transaction session
              }
            );

            if (!updatedEmployee) {
              throw new Error(`Failed to update employee ${employee.firstName} ${employee.lastName}`);
            }

            // Handle service periods (matching single employee logic)
            const servicePeriod = {
              startDate: updatedEmployee.doa || updatedEmployee.dateOfAppointment || today,
              endDate: lastDay,
              terminationReason: getTerminationReasonFromCode(uifStatusCode),
              uifStatusCode: uifStatusCode
            };

            if (updatedEmployee.servicePeriods.length === 0) {
              updatedEmployee.servicePeriods.push(servicePeriod);
            } else {
              updatedEmployee.servicePeriods[
                updatedEmployee.servicePeriods.length - 1
              ] = servicePeriod;
            }

            // Save the service period updates without validation
            await Employee.findByIdAndUpdate(
              employeeId,
              { $set: { servicePeriods: updatedEmployee.servicePeriods } },
              {
                runValidators: false,
                session: session // Use transaction session
              }
            );

            successfulEmployees.push({
              id: employeeId,
              name: `${employee.firstName} ${employee.lastName}`,
              auditEntry: auditEntry
            });
            successCount++;

          } catch (error) {
            console.error(`Error ending service for employee ${employeeId}:`, error);
            const employeeName = `${employee.firstName} ${employee.lastName}`;
            errors.push(`Failed to end service for ${employeeName}: ${error.message}`);
            failedEmployees.push({
              id: employeeId,
              name: employeeName,
              reason: error.message
            });
            errorCount++;

            // For transaction mode, we'll continue processing other employees
            // but log the error for detailed reporting
          }
        }

        // Add invalid employees to the failed list
        invalidEmployees.forEach(result => {
          const employeeName = result.employee ?
            `${result.employee.firstName} ${result.employee.lastName}` :
            `ID: ${result.id}`;
          errors.push(`${employeeName}: ${result.reason}`);
          failedEmployees.push({
            id: result.id,
            name: employeeName,
            reason: result.reason
          });
          errorCount++;
        });

        // Commit transaction if we have any successful updates
        if (successCount > 0) {
          await session.commitTransaction();
          console.log(`Bulk end service transaction committed successfully for ${successCount} employees`);
        } else {
          await session.abortTransaction();
          console.log('Bulk end service transaction aborted - no successful updates');
        }

      } catch (transactionError) {
        // Rollback transaction on any critical error
        await session.abortTransaction();
        console.error('Bulk end service transaction failed:', transactionError);

        req.flash("error", "Transaction failed. No employees were processed. Please try again.");
        return res.redirect(`/clients/${companyCode}/employeeManagement/end-service`);

      } finally {
        // Always end the session
        await session.endSession();
      }

      // Enhanced flash messages with detailed feedback
      if (successCount > 0) {
        const successMessage = `Successfully ended service for ${successCount} employee${successCount > 1 ? 's' : ''}`;
        req.flash("success", successMessage);
      }

      if (errorCount > 0) {
        const errorMessage = `Failed to end service for ${errorCount} employee${errorCount > 1 ? 's' : ''}`;
        req.flash("error", errorMessage);

        // Add detailed error information
        if (errors.length > 0) {
          req.flash("error", `Detailed errors: ${errors.slice(0, 5).join('; ')}${errors.length > 5 ? ` and ${errors.length - 5} more...` : ''}`);
        }
      }

      res.redirect(`/clients/${companyCode}/employeeManagement/bulkActions`);
    } catch (error) {
      console.error("Error in bulk end service:", error);
      req.flash("error", "An error occurred while processing the bulk end service request");
      res.redirect(`/clients/${companyCode}/employeeManagement/end-service`);
    }
  }
);

// Bulk Reinstate Routes
router.get(
  "/:companyCode/employeeManagement/reinstate",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      // Get terminated employees only (for reinstatement)
      const employees = await Employee.find({
        company: company._id,
        employmentStatus: false  // Boolean field: false = terminated
      }).select('firstName lastName companyEmployeeNumber status lastDayOfService');

      res.render("bulk-reinstate", {
        company,
        employees,
        user: req.user,
        companyCode,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading bulk reinstate page:", error);
      res
        .status(500)
        .send("An error occurred while loading the bulk reinstate page");
    }
  }
);

router.post(
  "/:companyCode/employeeManagement/reinstate",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const { employeeIds, reinstatementDate, reinstatementReason } = req.body;

      // Enhanced validation
      if (!employeeIds || employeeIds.length === 0) {
        req.flash("error", "No employees selected for reinstatement");
        return res.redirect(`/clients/${companyCode}/employeeManagement/reinstate`);
      }

      if (employeeIds.length > 50) {
        req.flash("error", "Cannot process more than 50 employees at once. Please select fewer employees.");
        return res.redirect(`/clients/${companyCode}/employeeManagement/reinstate`);
      }

      if (!reinstatementDate) {
        req.flash("error", "Reinstatement date is required");
        return res.redirect(`/clients/${companyCode}/employeeManagement/reinstate`);
      }

      if (!reinstatementReason) {
        req.flash("error", "Reinstatement reason is required");
        return res.redirect(`/clients/${companyCode}/employeeManagement/reinstate`);
      }

      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      const reinstateDate = new Date(reinstatementDate);
      const today = new Date();

      // Validate reinstatement date is not in the future
      if (reinstateDate > today) {
        req.flash("error", "Reinstatement date cannot be in the future");
        return res.redirect(`/clients/${companyCode}/employeeManagement/reinstate`);
      }

      let successCount = 0;
      let errorCount = 0;
      const errors = [];

      // Start transaction
      const session = await Employee.startSession();

      try {
        await session.startTransaction();

        // Process each employee
        for (const employeeId of employeeIds) {
          try {
            const employee = await Employee.findById(employeeId).session(session);
            if (!employee) {
              errors.push(`Employee with ID ${employeeId} not found`);
              errorCount++;
              continue;
            }

            // Validate employee belongs to the company
            if (employee.company.toString() !== company._id.toString()) {
              errors.push(`Employee ${employee.firstName} ${employee.lastName} does not belong to this company`);
              errorCount++;
              continue;
            }

            // Validate employee is terminated (can be reinstated)
            if (employee.employmentStatus !== false) {
              errors.push(`Employee ${employee.firstName} ${employee.lastName} is not terminated and cannot be reinstated`);
              errorCount++;
              continue;
            }

            // Update employee status
            const updates = {
              status: "Active",
              employmentStatus: true, // Boolean field: true = active
              reinstatementDate: reinstateDate,
              reinstatementReason: reinstatementReason,
              lastDayOfService: null, // Clear termination date
              uifStatusCode: null, // Clear UIF status
              terminationReason: null, // Clear termination reason
              terminationNoticeDate: null // Clear termination notice
            };

            await Employee.findByIdAndUpdate(
              employeeId,
              { $set: updates },
              {
                new: true,
                runValidators: false,
                session: session
              }
            );

            // Add new service period for reinstatement
            const newServicePeriod = {
              startDate: reinstateDate,
              endDate: null, // Open-ended
              reinstatementReason: reinstatementReason
            };

            await Employee.findByIdAndUpdate(
              employeeId,
              { $push: { servicePeriods: newServicePeriod } },
              { session: session }
            );

            successCount++;

          } catch (error) {
            console.error(`Error reinstating employee ${employeeId}:`, error);
            const employee = await Employee.findById(employeeId);
            const employeeName = employee ? `${employee.firstName} ${employee.lastName}` : `ID: ${employeeId}`;
            errors.push(`Failed to reinstate ${employeeName}: ${error.message}`);
            errorCount++;
          }
        }

        if (successCount > 0) {
          await session.commitTransaction();
        } else {
          await session.abortTransaction();
        }

      } catch (transactionError) {
        await session.abortTransaction();
        console.error('Bulk reinstate transaction failed:', transactionError);
        req.flash("error", "Transaction failed. No employees were processed. Please try again.");
        return res.redirect(`/clients/${companyCode}/employeeManagement/reinstate`);
      } finally {
        await session.endSession();
      }

      // Set flash messages
      if (successCount > 0) {
        req.flash("success", `Successfully reinstated ${successCount} employee${successCount > 1 ? 's' : ''}`);
      }

      if (errorCount > 0) {
        const errorMessage = `Failed to reinstate ${errorCount} employee${errorCount > 1 ? 's' : ''}`;
        req.flash("error", errorMessage);

        if (errors.length > 0) {
          req.flash("error", `Detailed errors: ${errors.slice(0, 5).join('; ')}${errors.length > 5 ? ` and ${errors.length - 5} more...` : ''}`);
        }
      }

      res.redirect(`/clients/${companyCode}/employeeManagement/bulkActions`);
    } catch (error) {
      console.error("Error in bulk reinstate:", error);
      req.flash("error", "An error occurred while processing the bulk reinstate request");
      res.redirect(`/clients/${companyCode}/employeeManagement/reinstate`);
    }
  }
);

// Bulk Undo End of Service Routes
router.get(
  "/:companyCode/employeeManagement/undo-end-of-service",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      // Get employees with "Serving Notice" status (pending termination)
      const employees = await Employee.find({
        company: company._id,
        status: "Serving Notice"
      }).select('firstName lastName companyEmployeeNumber status lastDayOfService terminationNoticeDate');

      res.render("bulk-undo-end-of-service", {
        company,
        employees,
        user: req.user,
        companyCode,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading bulk undo end of service page:", error);
      res
        .status(500)
        .send("An error occurred while loading the bulk undo end of service page");
    }
  }
);

router.post(
  "/:companyCode/employeeManagement/undo-end-of-service",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const { employeeIds } = req.body;

      // Enhanced validation
      if (!employeeIds || employeeIds.length === 0) {
        req.flash("error", "No employees selected for undo end of service");
        return res.redirect(`/clients/${companyCode}/employeeManagement/undo-end-of-service`);
      }

      if (employeeIds.length > 50) {
        req.flash("error", "Cannot process more than 50 employees at once. Please select fewer employees.");
        return res.redirect(`/clients/${companyCode}/employeeManagement/undo-end-of-service`);
      }

      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      let successCount = 0;
      let errorCount = 0;
      const errors = [];

      // Start transaction
      const session = await Employee.startSession();

      try {
        await session.startTransaction();

        // Process each employee
        for (const employeeId of employeeIds) {
          try {
            const employee = await Employee.findById(employeeId).session(session);
            if (!employee) {
              errors.push(`Employee with ID ${employeeId} not found`);
              errorCount++;
              continue;
            }

            // Validate employee belongs to the company
            if (employee.company.toString() !== company._id.toString()) {
              errors.push(`Employee ${employee.firstName} ${employee.lastName} does not belong to this company`);
              errorCount++;
              continue;
            }

            // Validate employee has pending termination (Serving Notice)
            if (employee.status !== "Serving Notice") {
              errors.push(`Employee ${employee.firstName} ${employee.lastName} does not have a pending termination to undo`);
              errorCount++;
              continue;
            }

            // Undo the termination (matching single employee logic)
            const updates = {
              status: "Active",
              employmentStatus: true, // Boolean field: true = active
              lastDayOfService: null,
              uifStatusCode: null,
              terminationReason: null,
              terminationNoticeDate: null
            };

            await Employee.findByIdAndUpdate(
              employeeId,
              { $set: updates },
              {
                new: true,
                runValidators: false,
                session: session
              }
            );

            // Update service periods - remove end date from current period
            if (employee.servicePeriods && employee.servicePeriods.length > 0) {
              const currentPeriod = employee.servicePeriods[employee.servicePeriods.length - 1];
              currentPeriod.endDate = null;
              currentPeriod.terminationReason = null;

              await Employee.findByIdAndUpdate(
                employeeId,
                { $set: { servicePeriods: employee.servicePeriods } },
                { session: session }
              );
            }

            successCount++;

          } catch (error) {
            console.error(`Error undoing end of service for employee ${employeeId}:`, error);
            const employee = await Employee.findById(employeeId);
            const employeeName = employee ? `${employee.firstName} ${employee.lastName}` : `ID: ${employeeId}`;
            errors.push(`Failed to undo termination for ${employeeName}: ${error.message}`);
            errorCount++;
          }
        }

        if (successCount > 0) {
          await session.commitTransaction();
        } else {
          await session.abortTransaction();
        }

      } catch (transactionError) {
        await session.abortTransaction();
        console.error('Bulk undo end of service transaction failed:', transactionError);
        req.flash("error", "Transaction failed. No employees were processed. Please try again.");
        return res.redirect(`/clients/${companyCode}/employeeManagement/undo-end-of-service`);
      } finally {
        await session.endSession();
      }

      // Set flash messages
      if (successCount > 0) {
        req.flash("success", `Successfully undone termination for ${successCount} employee${successCount > 1 ? 's' : ''}`);
      }

      if (errorCount > 0) {
        const errorMessage = `Failed to undo termination for ${errorCount} employee${errorCount > 1 ? 's' : ''}`;
        req.flash("error", errorMessage);

        if (errors.length > 0) {
          req.flash("error", `Detailed errors: ${errors.slice(0, 5).join('; ')}${errors.length > 5 ? ` and ${errors.length - 5} more...` : ''}`);
        }
      }

      res.redirect(`/clients/${companyCode}/employeeManagement/bulkActions`);
    } catch (error) {
      console.error("Error in bulk undo end of service:", error);
      req.flash("error", "An error occurred while processing the bulk undo end of service request");
      res.redirect(`/clients/${companyCode}/employeeManagement/undo-end-of-service`);
    }
  }
);

// Information Inputs Routes
router.get(
  "/:companyCode/employeeManagement/essentials",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      // Get all employees for the company
      const employees = await Employee.find({
        company: company._id
      }).select('firstName lastName companyEmployeeNumber employeeNumber employmentStatus status dob doa idType idNumber passportNumber passportCountryCode email');

      // Get employee number settings (add default if not found)
      const employeeNumberSettings = company.employeeNumberSettings || { mode: 'manual' };

      res.render("essentials", {
        company,
        employees,
        user: req.user,
        companyCode,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
        employeeNumberSettings,
        moment: require('moment-timezone'),
        DEFAULT_TIMEZONE: 'Africa/Johannesburg'
      });
    } catch (error) {
      console.error("Error loading essentials page:", error);
      res.status(500).send("An error occurred while loading the essentials page");
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/payment-banking-info",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      // Get all employees for the company
      const employees = await Employee.find({
        company: company._id
      }).select('firstName lastName companyEmployeeNumber employeeNumber employmentStatus status paymentMethod bank accountNumber branchCode accountType accountHolder');

      res.render("payment-banking-info", {
        company,
        employees,
        user: req.user,
        companyCode,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading payment banking info page:", error);
      res.status(500).send("An error occurred while loading the payment banking info page");
    }
  }
);

// POST route for payment-banking-info
router.post(
  "/:companyCode/employeeManagement/payment-banking-info",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const { employees } = req.body;

      // Validate company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found"
        });
      }

      // Validate request body
      if (!Array.isArray(employees)) {
        return res.status(400).json({
          success: false,
          message: "Invalid request format: employees must be an array"
        });
      }

      let successCount = 0;
      let errorCount = 0;
      const errors = [];

      // Process each employee update
      for (const employeeData of employees) {
        try {
          const employee = await Employee.findById(employeeData._id);

          if (!employee) {
            errors.push({
              employeeId: employeeData._id,
              error: "Employee not found"
            });
            errorCount++;
            continue;
          }

          // Update banking info using direct fields
          const updates = {
            paymentMethod: employeeData.paymentMethod,
            bank: employeeData.bank,
            accountNumber: employeeData.accountNumber,
            branchCode: employeeData.branchCode,
            accountType: employeeData.accountType,
            accountHolder: employeeData.accountHolder
          };

          await Employee.findByIdAndUpdate(employee._id, { $set: updates });
          successCount++;

        } catch (error) {
          console.error(`Error updating employee ${employeeData._id}:`, error);
          errors.push({
            employeeId: employeeData._id,
            error: error.message
          });
          errorCount++;
        }
      }

      res.json({
        success: true,
        message: `Successfully updated ${successCount} employees`,
        successCount,
        errorCount,
        errors: errors.length > 0 ? errors : undefined
      });

    } catch (error) {
      console.error("Error processing payment & banking info update:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error"
      });
    }
  }
);

// Payroll Inputs Routes
router.get(
  "/:companyCode/employeeManagement/payslip-inputs",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      // Get all employees for the company
      const employees = await Employee.find({
        company: company._id
      }).select('firstName lastName companyEmployeeNumber employmentStatus status');

      res.render("payslip-inputs", {
        company,
        employees,
        user: req.user,
        companyCode,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error loading payslip inputs page:", error);
      res.status(500).send("An error occurred while loading the payslip inputs page");
    }
  }
);

// Bulk Essentials POST Route
router.post(
  "/:companyCode/employeeManagement/essentials",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const { employees } = req.body;

      // Validate request data
      if (!employees || !Array.isArray(employees)) {
        return res.status(400).json({
          success: false,
          error: "Invalid employee data format",
          message: "Employee data must be provided as an array"
        });
      }

      // Validate company access
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          error: "Company not found",
          message: "The specified company could not be found"
        });
      }

      // Validate batch size
      if (employees.length > 100) {
        return res.status(400).json({
          success: false,
          error: "Batch size too large",
          message: "Cannot process more than 100 employees at once"
        });
      }

      let successCount = 0;
      let errorCount = 0;
      const errors = [];
      const processedEmployees = [];

      // Start database transaction
      const session = await mongoose.startSession();

      try {
        await session.withTransaction(async () => {
          // Process each employee update
          for (let i = 0; i < employees.length; i++) {
            try {
              const employeeData = employees[i];
              const { _id, ...updateData } = employeeData;

              if (!_id) {
                errors.push(`Row ${i + 1}: Missing employee ID`);
                errorCount++;
                continue;
              }

              // Validate and sanitize employee data
              const validationResult = await validateEmployeeEssentials(updateData, i + 1);
              if (!validationResult.isValid) {
                errors.push(...validationResult.errors);
                errorCount++;
                continue;
              }

              // Update employee
              const updatedEmployee = await Employee.findByIdAndUpdate(
                _id,
                { $set: validationResult.sanitizedData },
                {
                  new: true,
                  runValidators: true,
                  session
                }
              );

              if (!updatedEmployee) {
                errors.push(`Row ${i + 1}: Employee not found`);
                errorCount++;
                continue;
              }

              // Verify employee belongs to company
              if (updatedEmployee.company.toString() !== company._id.toString()) {
                errors.push(`Row ${i + 1}: Employee does not belong to this company`);
                errorCount++;
                continue;
              }

              processedEmployees.push({
                id: updatedEmployee._id,
                name: `${updatedEmployee.firstName} ${updatedEmployee.lastName}`,
                employeeNumber: updatedEmployee.companyEmployeeNumber || updatedEmployee.employeeNumber
              });

              successCount++;

            } catch (error) {
              console.error(`Error updating employee at row ${i + 1}:`, error);
              errors.push(`Row ${i + 1}: ${error.message || 'Update failed'}`);
              errorCount++;
            }
          }

          // If all operations failed, throw error to rollback transaction
          if (successCount === 0 && errorCount > 0) {
            throw new Error('All employee updates failed');
          }
        });

        // Prepare response
        const response = {
          success: successCount > 0,
          successCount,
          errorCount,
          totalCount: employees.length,
          message: successCount > 0
            ? `Successfully updated ${successCount} employee${successCount > 1 ? 's' : ''}${errorCount > 0 ? ` (${errorCount} failed)` : ''}`
            : 'No employees were updated',
          processedEmployees: processedEmployees.slice(0, 10), // Limit response size
          errors: errors.slice(0, 20), // Limit error list
          hasMoreErrors: errors.length > 20
        };

        if (successCount > 0) {
          res.json(response);
        } else {
          res.status(400).json(response);
        }

      } catch (transactionError) {
        console.error("Transaction error in bulk essentials update:", transactionError);
        res.status(500).json({
          success: false,
          error: "Transaction failed",
          message: "All changes have been rolled back due to an error",
          successCount: 0,
          errorCount: employees.length,
          errors: ["Transaction failed: " + transactionError.message]
        });
      } finally {
        await session.endSession();
      }

    } catch (error) {
      console.error("Error in bulk essentials update:", error);
      res.status(500).json({
        success: false,
        error: "Internal server error",
        message: "An unexpected error occurred while processing the request",
        details: error.message
      });
    }
  }
);

router.post(
  "/:companyCode/employeeManagement/payslip-inputs",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const { employees } = req.body;

      if (!employees || !Array.isArray(employees)) {
        return res.status(400).json({ error: "Invalid employee data" });
      }

      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({ error: "Company not found" });
      }

      let successCount = 0;
      let errorCount = 0;
      const errors = [];

      // Process each employee update
      for (const employeeData of employees) {
        try {
          const { id, ...updateData } = employeeData;

          if (!id) {
            errors.push("Missing employee ID");
            errorCount++;
            continue;
          }

          // Validate and sanitize numeric fields
          const numericFields = ['basicSalary', 'hoursWorked', 'overtimeHours', 'commission', 'allowances', 'deductions', 'leaveDays'];
          const sanitizedData = {};

          numericFields.forEach(field => {
            if (updateData[field] !== null && updateData[field] !== undefined && updateData[field] !== '') {
              const value = parseFloat(updateData[field]);
              if (!isNaN(value) && value >= 0) {
                sanitizedData[field] = value;
              }
            }
          });

          // Update employee
          const updatedEmployee = await Employee.findByIdAndUpdate(
            id,
            { $set: sanitizedData },
            { new: true, runValidators: false }
          );

          if (!updatedEmployee) {
            errors.push(`Employee with ID ${id} not found`);
            errorCount++;
            continue;
          }

          // Verify employee belongs to company
          if (updatedEmployee.company.toString() !== company._id.toString()) {
            errors.push(`Employee ${updatedEmployee.firstName} ${updatedEmployee.lastName} does not belong to this company`);
            errorCount++;
            continue;
          }

          successCount++;

        } catch (error) {
          console.error(`Error updating employee ${employeeData.id}:`, error);
          errors.push(`Failed to update employee: ${error.message}`);
          errorCount++;
        }
      }

      // Return response
      const response = {
        success: successCount > 0,
        successCount,
        errorCount,
        message: successCount > 0
          ? `Successfully updated ${successCount} employee${successCount > 1 ? 's' : ''}`
          : 'No employees were updated',
        errors: errors.length > 0 ? errors.slice(0, 5) : undefined
      };

      if (successCount > 0) {
        res.json(response);
      } else {
        res.status(400).json(response);
      }

    } catch (error) {
      console.error("Error in payslip inputs update:", error);
      res.status(500).json({
        error: "An error occurred while updating payslip inputs",
        message: error.message
      });
    }
  }
);

router.get(
  "/:companyCode/employeeManagement/bulk-add-employees",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).send("Company not found");
      }

      res.render("bulk-add-employees", {
        company,
        companyCode,
        user: req.user,
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error rendering bulk add employees page:", error);
      res.status(500).send("Internal Server Error");
    }
  }
);

router.post(
  "/:companyCode/employeeManagement/preview-upload",
  ensureAuthenticated,
  csrfProtection,
  (req, res) => {

    // Log all request headers for debugging
    Object.keys(req.headers).forEach(key => {
    });

    // Create a temporary buffer to collect the request body for diagnosis
    let rawBodyChunks = [];

    // Handle body data events to inspect the raw request
    req.on('data', chunk => {
      if (rawBodyChunks.length < 5) { // Only store first few chunks to avoid memory issues
        rawBodyChunks.push(chunk);
      }
    });

    // Handle request end event
    req.on('end', () => {

      // Log information about the collected chunks
      if (rawBodyChunks.length > 0) {
        const totalLength = rawBodyChunks.reduce((acc, chunk) => acc + chunk.length, 0);

        // Peek at the first chunk to see the form boundary
        const firstChunk = Buffer.concat(rawBodyChunks.slice(0, 1));
      }

      // Now process with multer

      // Configure multer with detailed logging
      const customMulter = multer({
        storage: multer.memoryStorage(),
        limits: {
          fileSize: 10 * 1024 * 1024, // 10MB
          files: 1,
          fields: 10
        },
        fileFilter: (req, file, cb) => {

          // Accept all files for diagnostic purposes
          return cb(null, true);
        }
      });

      // Use the custom multer instance
      customMulter.single('file')(req, res, function(err) {

        if (err) {
          console.error('\n=== DEBUG: Multer Error ===');
          console.error('Error type:', err.constructor.name);
          console.error('Error message:', err.message);
          console.error('Error stack:', err.stack);

          // If the error comes from Busboy, analyze deeper
          if (err.message.includes('Unexpected end of form')) {
            console.error('\n=== CRITICAL DEBUG: Busboy Error ===');
            console.error('This suggests the multipart form data is malformed or incomplete');
            console.error('Content-Type header:', req.get('content-type'));
            console.error('Boundary value:', req.get('content-type').split('boundary=')[1]);
          }

          return res.status(400).json({
            success: false,
            message: err.message,
            diagnostic: 'The file upload failed due to malformed request data. Please try again with a different file or browser.'
          });
        }

        if (!req.file) {
          console.error('\n=== DEBUG: No File in Request ===');
          return res.status(400).json({
            success: false,
            message: 'No file uploaded or file upload was interrupted',
            diagnostic: 'The server did not receive a file in the request. Please select a file and try again.'
          });
        }


        try {
          // Basic file validation
          if (!req.file.buffer || req.file.buffer.length === 0) {
            throw new Error('File buffer is empty or corrupted');
          }

          // Process the file as plain text first to inspect its content
          const filePreview = req.file.buffer.toString().substring(0, 100);

          if (filePreview.includes('PK')) {
          } else if (filePreview.includes('<?xml')) {
          } else {
          }

          // Try to process with xlsx
          try {
            const workbook = xlsx.read(req.file.buffer, { type: 'buffer' });

            const firstSheet = workbook.Sheets[workbook.SheetNames[0]];

            // Convert to JSON
            const rows = xlsx.utils.sheet_to_json(firstSheet, { header: 1 });

            if (rows.length < 2) {
              throw new Error('Excel file must contain headers and at least one data row');
            }

            const headers = rows[0].map(h => h?.toString().trim() || '');

            // Simple data processing
            const dataRows = rows.slice(1, 6).map(row => {
              const obj = {};
              headers.forEach((header, i) => {
                obj[header] = row[i] || '';
              });
              return obj;
            });

            // Return success
            return res.status(200).json({
              success: true,
              message: 'File processed successfully',
              file: {
                name: req.file.originalname,
                size: req.file.size
              },
              preview: dataRows
            });

          } catch (xlsxError) {
            console.error('XLSX processing error:', xlsxError);
            throw new Error(`XLSX processing failed: ${xlsxError.message}`);
          }

        } catch (error) {
          console.error('\n=== DEBUG: Processing Error ===');
          console.error('Error:', error);
          return res.status(400).json({
            success: false,
            message: error.message,
            diagnostic: 'The server received the file but could not process it. Please check the file format.'
          });
        }
      });
    });

    // Handle request errors
    req.on('error', err => {
      console.error('\n=== DEBUG: Request Stream Error ===');
      console.error('Error:', err);
      res.status(400).json({
        success: false,
        message: 'Error in request stream: ' + err.message,
        diagnostic: 'The file upload was interrupted. Please try again.'
      });
    });
  }
);

router.post(
  "/:companyCode/employeeManagement/confirm-upload",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Company.findOne({ companyCode });
      const records = req.body;

      const successList = [];
      const errorList = [];

      for (const record of records) {
        try {
          // Create employee record
          const employee = new Employee({
            ...record,
            company: company._id,
            globalEmployeeId: uuidv4()
          });

          await employee.save();

          successList.push({
            row: record.row,
            name: `${record.firstName} ${record.lastName}`,
            employeeNumber: employee.companyEmployeeNumber
          });
        } catch (error) {
          errorList.push({
            row: record.row,
            error: error.message
          });
        }
      }

      res.json({
        successful: successList.length,
        failed: errorList.length,
        successList,
        errorList
      });
    } catch (error) {
      console.error("Error confirming upload:", error);
      res.status(500).json({ error: "Failed to process upload" });
    }
  }
);

router.get("/skills-equity", async (req, res) => {
  try {
    const employees = await Employee.find().select(
      "lastName firstName employeeNumber gender race occupationLevel occupationCategory"
    );
    res.render("skills-equity", { employees: employees });
  } catch (error) {
    console.error("Error rendering skills-equity page:", error);
    res
      .status(500)
      .send("An error occurred while rendering the skills-equity page");
  }
});
router.get(
  "/:companyCode/employeeManagement/download-template",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection
  async (req, res) => {
    try {
      const { companyCode } = req.params;

      // Validate company access
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Check user permissions
      const userCompanyIds = req.user.companies.map(c => c._id ? c._id.toString() : c.toString());
      if (!userCompanyIds.includes(company._id.toString())) {
        req.flash(
          "error",
          "You don't have permission to access this company's data"
        );
        return res.redirect("/clients");
      }

      const generator = new EmployeeTemplateGenerator();
      const workbook = await generator.generateTemplate();

      // Set security headers
      res.setHeader("X-Content-Type-Options", "nosniff");
      res.setHeader(
        "Cache-Control",
        "no-store, no-cache, must-revalidate, private"
      );

      // Set download headers
      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=employee-import-template-${companyCode}-${Date.now()}.xlsx`
      );

      await workbook.xlsx.write(res);
      res.end();
    } catch (error) {
      console.error("Error generating template:", error);
      req.flash("error", "Failed to generate template");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// Handle file upload
router.post(
  "/:companyCode/employeeManagement/bulk-add-employees",
  ensureAuthenticated,
  csrfProtection,
  uploadMiddleware.single('file'),
  async (req, res) => {
    try {

      const { companyCode } = req.params;

      // Validate company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        console.error("Company not found:", companyCode);
        return res.status(404).json({
          success: false,
          message: "Company not found"
        });
      }

      // Check if file was uploaded
      if (!req.file) {
        console.error("No file uploaded");
        return res.status(400).json({
          success: false,
          message: "No file uploaded"
        });
      }


      try {
        // Read Excel file from buffer
        const workbook = xlsx.read(req.file.buffer, { type: 'buffer' });

        if (!workbook || !workbook.SheetNames || workbook.SheetNames.length === 0) {
          throw new Error('Invalid Excel file structure');
        }


        const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
        if (!firstSheet || !firstSheet['!ref']) {
          throw new Error('Excel file is empty or corrupted');
        }

        // Convert to JSON
        const rows = xlsx.utils.sheet_to_json(firstSheet, { header: 1 });

        if (rows.length < 2) {
          throw new Error('Excel file must contain headers and at least one data row');
        }

        // Validate headers
        const headers = rows[0].map(h => h?.toString().trim() || '');
        const requiredHeaders = ['First Name', 'Last Name', 'Email', 'Department', 'Position'];
        const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));

        if (missingHeaders.length > 0) {
          throw new Error(`Missing required headers: ${missingHeaders.join(', ')}`);
        }


        res.json({
          success: true,
          message: 'File uploaded and validated successfully',
          file: {
            name: req.file.originalname,
            size: req.file.size,
            rows: rows.length - 1 // Exclude header row
          }
        });

      } catch (error) {
        console.error('Excel validation error:', error);
        return res.status(400).json({
          success: false,
          message: error.message || 'Invalid Excel file format'
        });
      }

    } catch (error) {
      console.error("Error in bulk employee upload:", error);
      res.status(error.status || 400).json({
        success: false,
        message: error.message || 'Error uploading file'
      });
    }
  }
);

// Mount upload router with proper prefix
router.use('/:companyCode/employeeManagement', (req, res, next) => {
  next();
}, uploadRouter);

// Add an alternative upload route that bypasses busboy/multer issues
router.post(
  "/:companyCode/employeeManagement/alt-preview-upload",
  ensureAuthenticated,
  csrfProtection,
  express.raw({
    type: 'multipart/form-data',
    limit: '15mb'  // Slightly larger than our 10MB limit to ensure we get the full file
  }),
  async (req, res) => {

    try {
      // Manually parse multipart form data

      // Check if we have the raw body
      if (!req.body || !Buffer.isBuffer(req.body) || req.body.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'No request body received',
          diagnostic: 'The server did not receive any data. Please try again.'
        });
      }


      // Extract boundary from content-type header
      const contentType = req.get('content-type');
      const boundaryMatch = contentType.match(/boundary=(?:"([^"]+)"|([^;]+))/i);

      if (!boundaryMatch) {
        return res.status(400).json({
          success: false,
          message: 'Invalid content-type header (missing boundary)',
          diagnostic: 'The upload request was missing required information. Please try again.'
        });
      }

      const boundary = boundaryMatch[1] || boundaryMatch[2];

      // Convert to string for simple processing (not ideal for large files but works for this case)
      const bodyStr = req.body.toString('utf-8');

      // Find the file in the multipart data
      const filePartMatch = bodyStr.match(new RegExp(`--${boundary}[\\r\\n]+content-disposition:\\s*form-data;\\s*name="file";\\s*filename="([^"]+)"[\\r\\n]+content-type:\\s*([^\\r\\n]+)[\\r\\n]+[\\r\\n]+(.*?)--${boundary}`, 'is'));

      if (!filePartMatch) {
        return res.status(400).json({
          success: false,
          message: 'No file found in request',
          diagnostic: 'The server could not find the file in the uploaded data. Please try a different file.'
        });
      }

      // Extract file info
      const filename = filePartMatch[1];
      const fileMimeType = filePartMatch[2];


      // Find the file content indices
      const fileHeaderEnd = bodyStr.indexOf('\r\n\r\n', bodyStr.indexOf(`filename="${filename}"`)) + 4;
      const fileEnd = bodyStr.indexOf(`--${boundary}`, fileHeaderEnd) - 2; // -2 for \r\n

      // Extract file content as buffer
      const fileContent = req.body.slice(fileHeaderEnd, fileEnd);

      // Process Excel file
      try {
        const workbook = xlsx.read(fileContent, { type: 'buffer' });

        if (!workbook || !workbook.SheetNames || workbook.SheetNames.length === 0) {
          throw new Error('Invalid Excel file structure');
        }


        // Get first worksheet
        const firstSheet = workbook.Sheets[workbook.SheetNames[0]];

        if (!firstSheet || !firstSheet['!ref']) {
          throw new Error('Empty worksheet');
        }

        // Convert to JSON
        const rows = xlsx.utils.sheet_to_json(firstSheet, { header: 1 });

        if (rows.length < 2) {
          throw new Error('Excel file must contain headers and at least one data row');
        }

        // Get headers from first row
        const headers = rows[0].map(h => h?.toString().trim() || '');

        // Convert data rows to objects with header keys
        const dataRows = [];
        for (let i = 1; i < Math.min(rows.length, 6); i++) {
          const rowData = {};
          headers.forEach((header, index) => {
            rowData[header] = rows[i][index]?.toString() || '';
          });
          dataRows.push(rowData);
        }

        // Return success
        return res.status(200).json({
          success: true,
          message: 'File processed successfully',
          file: {
            name: filename,
            size: fileContent.length,
            rows: rows.length - 1
          },
          preview: dataRows
        });

      } catch (excelError) {
        console.error('Excel processing error:', excelError);
        return res.status(400).json({
          success: false,
          message: excelError.message,
          diagnostic: 'The file appears to be invalid or corrupted. Please check the file format.'
        });
      }

    } catch (error) {
      console.error('Error in alternative upload handler:', error);
      return res.status(500).json({
        success: false,
        message: 'Server error processing upload',
        diagnostic: error.message
      });
    }
  }
);

// Alternative upload route using multer
router.post('/:companyCode/employeeManagement/alt-preview-upload', ensureAuthenticated, csrf({ cookie: true }), async (req, res) => {

    // Create a multer instance with memory storage
    const upload = multer({
        storage: multer.memoryStorage(),
        limits: {
            fileSize: 15 * 1024 * 1024, // 15MB limit
            files: 1
        },
        fileFilter: (req, file, cb) => {

            // Check file type
            if (!file.mimetype.match(/^(application\/vnd\.openxmlformats-officedocument\.spreadsheetml\.sheet|application\/vnd\.ms-excel)$/)) {
                return cb(new Error('Invalid file type. Only Excel files are allowed.'));
            }

            // Check file extension
            if (!file.originalname.match(/\.(xlsx|xls)$/)) {
                return cb(new Error('Invalid file extension. Only .xlsx and .xls files are allowed.'));
            }

            cb(null, true);
        }
    }).single('file');

    try {
        // Handle the file upload
        upload(req, res, async (err) => {
            if (err instanceof multer.MulterError) {
                console.error('Multer error:', err);
                return res.status(400).json({
                    success: false,
                    message: 'File upload error',
                    error: err.message
                });
            } else if (err) {
                console.error('Upload error:', err);
                return res.status(400).json({
                    success: false,
                    message: 'File validation error',
                    error: err.message
                });
            }

            if (!req.file) {
                console.error('No file uploaded');
                return res.status(400).json({
                    success: false,
                    message: 'No file uploaded',
                    diagnostic: 'Please select a file to upload.'
                });
            }


            try {
                // Process the Excel file
                const workbook = xlsx.read(req.file.buffer, { type: 'buffer' });
                const sheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[sheetName];
                const data = xlsx.utils.sheet_to_json(worksheet, { header: 1 });

                // Validate the data structure
                if (!data || data.length < 2) {
                    return res.status(400).json({
                        success: false,
                        message: 'Invalid file structure',
                        diagnostic: 'The file must contain at least a header row and one data row.'
                    });
                }

                // Get headers and validate required columns
                const headers = data[0];
                const requiredHeaders = ['First Name', 'Last Name', 'Email', 'Department', 'Position'];
                const missingHeaders = requiredHeaders.filter(header => !headers.includes(header));

                if (missingHeaders.length > 0) {
                    return res.status(400).json({
                        success: false,
                        message: 'Missing required columns',
                        diagnostic: `The following required columns are missing: ${missingHeaders.join(', ')}`
                    });
                }

                // Convert to JSON format
                const jsonData = data.slice(1).map((row, index) => {
                    const record = {};
                    headers.forEach((header, colIndex) => {
                        record[header] = row[colIndex] || '';
                    });
                    return record;
                });

                // Return success response with preview
                return res.json({
                    success: true,
                    message: 'File processed successfully',
                    file: {
                        name: req.file.originalname,
                        size: req.file.size,
                        type: req.file.mimetype
                    },
                    preview: jsonData.slice(0, 5) // Return first 5 rows as preview
                });

            } catch (excelError) {
                console.error('Excel processing error:', excelError);
                return res.status(400).json({
                    success: false,
                    message: 'Error processing Excel file',
                    error: excelError.message
                });
            }
        });
    } catch (error) {
        console.error('Route handler error:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
});

// Alternative upload route with minimal middleware
// Place this BEFORE any routes that use body-parser or other middleware that might consume the request body
router.post('/:companyCode/employeeManagement/basic-upload', ensureAuthenticated, (req, res) => {

    // Manually check CSRF token to avoid middleware issues
    const csrfToken = req.headers['x-csrf-token'];
    const csrfCookie = req.cookies && req.cookies['_csrf'];


    // Configure multer for this specific route only
    const upload = multer({
        storage: multer.memoryStorage(),
        limits: { fileSize: 15 * 1024 * 1024 }, // 15MB limit
        fileFilter: (req, file, cb) => {

            // Check MIME type
            const validMimeTypes = [
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/octet-stream', // Some browsers might send this for Excel files
                'application/zip', // .xlsx files are actually zip files
                '' // Handle empty mimetype
            ];

            if (file.mimetype && !validMimeTypes.includes(file.mimetype)) {
                return cb(new Error(`Invalid file type: ${file.mimetype}. Only Excel files are allowed.`));
            }

            // Check file extension
            if (!file.originalname.match(/\.(xlsx|xls)$/i)) {
                return cb(new Error(`Invalid file extension. Only .xlsx and .xls files are allowed.`));
            }

            cb(null, true);
        }
    }).single('file'); // Make sure field name matches client-side code

    // Handle upload using multer with direct error handling
    upload(req, res, function(err) {

        // Check for errors
        if (err) {
            console.error('Upload error:', err);
            return res.status(400).json({
                success: false,
                message: err.message || 'File upload failed',
                error: err.toString()
            });
        }

        // Check if file exists
        if (!req.file) {
            console.error('No file received');
            return res.status(400).json({
                success: false,
                message: 'No file uploaded',
                diagnostic: 'Please select a valid Excel file to upload.'
            });
        }


        try {
            // Process Excel file
            const workbook = xlsx.read(req.file.buffer, { type: 'buffer' });
            const sheetName = workbook.SheetNames[0];

            if (!sheetName) {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid Excel file',
                    diagnostic: 'The uploaded file does not contain any sheets.'
                });
            }

            const worksheet = workbook.Sheets[sheetName];
            const rawData = xlsx.utils.sheet_to_json(worksheet, { header: 1 });

            // Validate data
            if (!rawData || rawData.length < 2) {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid file contents',
                    diagnostic: 'The Excel file must contain at least a header row and one data row.'
                });
            }

            // Process headers
            const headers = rawData[0];
            const requiredHeaders = ['First Name', 'Last Name', 'Email', 'Department', 'Position'];
            const missingHeaders = requiredHeaders.filter(header => !headers.includes(header));

            if (missingHeaders.length > 0) {
                return res.status(400).json({
                    success: false,
                    message: 'Missing required columns',
                    diagnostic: `The following required columns are missing: ${missingHeaders.join(', ')}`
                });
            }

            // Convert data to JSON with header mapping
            const jsonData = rawData.slice(1)
                .filter(row => row.some(cell => cell !== undefined && cell !== null && cell !== '')) // Skip empty rows
                .map(row => {
                    const record = {};
                    headers.forEach((header, i) => {
                        record[header] = row[i] !== undefined ? row[i] : '';
                    });
                    return record;
                });


            // Return success with preview
            return res.status(200).json({
                success: true,
                message: 'File uploaded and processed successfully',
                file: {
                    name: req.file.originalname,
                    size: req.file.size,
                    type: req.file.mimetype
                },
                preview: jsonData.slice(0, 5), // First 5 rows
                totalRows: jsonData.length
            });
        } catch (error) {
            console.error('Excel processing error:', error);
            return res.status(400).json({
                success: false,
                message: 'Error processing Excel file',
                error: error.message,
                diagnostic: 'The file could not be processed. Please ensure it is a valid Excel file with the required structure.'
            });
        }
    });
});

// Confirm upload route to process previewed data
router.post('/:companyCode/employeeManagement/confirm-upload', ensureAuthenticated, express.json(), async (req, res) => {
    const companyCode = req.params.companyCode;
    const employeeData = req.body;

    if (!Array.isArray(employeeData)) {
        console.error('Invalid data format received');
        return res.status(400).json({
            success: false,
            message: 'Invalid data format. Expected an array of employee records.'
        });
    }


    try {
        // In a real implementation, you would save this data to your database
        // For this example, we'll just simulate success

        const results = {
            successful: employeeData.length,
            failed: 0
        };

        return res.status(200).json({
            success: true,
            message: 'Employees added successfully',
            ...results
        });
    } catch (error) {
        console.error('Error processing employee data:', error);
        return res.status(500).json({
            success: false,
            message: 'Error processing employee data',
            error: error.message
        });
    }
});

router.post(
  "/:companyCode/employeeManagement/selfService/enable/:employeeId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId } = req.params;

      const employee = await Employee.findById(employeeId);
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Check if email exists
      if (!employee.email) {
        return res.status(400).json({
          success: false,
          message: "Employee must have an email address to enable self-service",
        });
      }

      // Find or create employee role
      const employeeRole = await Role.findOne({ name: "employee" });
      if (!employeeRole) {
        return res.status(500).json({
          success: false,
          message: "Employee role not found",
        });
      }

      // Check if user account exists
      let user = await User.findOne({ email: employee.email });

      if (user) {
        // Update existing user
        user.firstName = employee.firstName;
        user.lastName = employee.lastName;
        user.role = employeeRole._id;
        if (!user.companies.includes(employee.company)) {
          user.companies.push(employee.company);
        }
        user.currentCompany = employee.company;
      } else {
        // Create new user
        const tempPassword = crypto.randomBytes(20).toString("hex");
        user = new User({
          firstName: employee.firstName,
          lastName: employee.lastName,
          email: employee.email,
          phone: employee.phone,
          password: tempPassword,
          role: employeeRole._id,
          companies: [employee.company],
          currentCompany: employee.company,
          isVerified: false,
          howDidYouHearAboutUs: "Other", // Set default value for self-service users
        });
      }

      await user.save();

      // Update employee
      employee.user = user._id;
      employee.selfServiceEnabled = true;
      employee.selfServiceToken = crypto.randomBytes(32).toString("hex");
      employee.selfServiceTokenExpiration = new Date(
        Date.now() + 24 * 60 * 60 * 1000
      ); // 24 hours

      // Ensure personalDetails exists and has idType set
      if (!employee.personalDetails) {
        employee.personalDetails = {};
      }

      // Only set idType if it doesn't exist to avoid overwriting existing data
      if (!employee.personalDetails.idType) {
        employee.personalDetails.idType = "other"; // Set default value to pass validation
      }

      await employee.save();

      // Send setup email
      const setupLink = `${process.env.BASE_URL}/employee/setup/${employee.selfServiceToken}`;

      // Ensure proper FROM address formatting
      const fromAddress = process.env.EMAIL_USER;
      const fromName = process.env.EMAIL_FROM_NAME || "Panda Software Solutions Group";

      if (!fromAddress) {
        throw new Error("EMAIL_USER environment variable is required for sending emails");
      }

      const mailOptions = {
        from: `"${fromName}" <${fromAddress}>`,
        to: employee.email,
        subject: "Set Up Your Employee Self-Service Account",
        html: `
          <h1>Welcome to Employee Self-Service</h1>
          <p>Hello ${employee.firstName},</p>
          <p>Your self-service account has been enabled. Please click the link below to set up your account:</p>
          <a href="${setupLink}">${setupLink}</a>
          <p>This link will expire in 24 hours.</p>
          <p>If you did not request this, please ignore this email.</p>
        `,
      };

      // Send email with better error handling
      try {
        console.log("Sending self-service setup email to:", employee.email);
        const emailInfo = await transporter.sendMail(mailOptions);
        console.log("Email sent successfully:", emailInfo.messageId);

        // Surgically add notification creation (preserves existing email functionality)
        try {
          const UnifiedNotificationService = require('../services/UnifiedNotificationService');
          const service = new UnifiedNotificationService();
          await service.createFromEmail(
            {
              to: employee.email,
              subject: mailOptions.subject,
              html: mailOptions.html
            },
            'self_service_setup',
            {
              userId: employee._id,
              messageId: emailInfo.messageId,
              setupUrl: setupLink,
              metadata: {
                employeeName: employee.firstName || employee.email.split('@')[0],
                selfServiceEnabled: true
              }
            }
          );
          console.log('✅ Self-service setup notification created successfully');
        } catch (notificationError) {
          // Never let notification errors affect email sending
          console.error('⚠️ Self-service setup notification creation failed (email still sent):', notificationError.message);
        }
      } catch (emailError) {
        console.error("Failed to send setup email:", emailError);
        // Don't fail the entire operation if email fails
        console.log("Self-service enabled but email failed to send");
      }

      res.json({
        success: true,
        message: "Self-service enabled and setup email sent",
      });
    } catch (error) {
      console.error("Error enabling self-service:", error);

      // Log validation errors in more detail
      if (error.name === 'ValidationError') {
        console.error('Validation errors:', JSON.stringify(error.errors));
      }

      res.status(500).json({
        success: false,
        message: "Failed to enable self-service",
        error: error.message,
      });
    }
  }
);

// Disable self-service route
router.post(
  "/:companyCode/employeeManagement/selfService/disable/:employeeId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId } = req.params;
      console.log("Disabling self-service for employee:", employeeId);

      const employee = await Employee.findById(employeeId);
      if (!employee) {
        console.log("Employee not found:", employeeId);
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Disable self-service
      employee.selfServiceEnabled = false;
      employee.selfServiceToken = null;
      employee.selfServiceTokenExpiration = null;
      await employee.save();

      console.log("Self-service disabled successfully for employee:", employeeId);

      res.json({
        success: true,
        message: "Self-service disabled successfully",
      });
    } catch (error) {
      console.error("Error disabling self-service:", error);
      res.status(500).json({
        success: false,
        message: "Failed to disable self-service",
        error: error.message,
      });
    }
  }
);

// Resend self-service setup email route
router.post(
  "/:companyCode/employeeManagement/selfService/resend/:employeeId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { employeeId } = req.params;
      console.log("Resending setup email for employee:", employeeId);

      const employee = await Employee.findById(employeeId);
      if (!employee) {
        console.log("Employee not found:", employeeId);
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Check if employee has email
      if (!employee.email) {
        console.log("Employee has no email address");
        return res.status(400).json({
          success: false,
          message: "Employee must have an email address",
        });
      }

      // Use SelfServiceTokenService to handle the resend
      const baseUrl = `${req.protocol}://${req.get("host")}`;
      const setupResult = await SelfServiceTokenService.handleSelfServiceEnable(
        employee,
        baseUrl
      );

      console.log("Setup email resent successfully");

      res.json({
        success: true,
        message: "Setup email resent successfully",
      });
    } catch (error) {
      console.error("Error resending setup email:", error);
      res.status(500).json({
        success: false,
        message: "Failed to resend setup email",
        error: error.message,
      });
    }
  }
);

router.post(
  "/selfService/enable/:employeeId",
  async (req, res) => {
    try {
      const employee = await Employee.findById(req.params.employeeId);

      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Check if employee already has self-service enabled
      if (employee.selfServiceEnabled) {
        return res.status(400).json({
          success: false,
          message: "Self-service is already enabled for this employee",
        });
      }

      // Check if email exists
      if (!employee.email) {
        return res.status(400).json({
          success: false,
          message: "Employee must have an email address for self-service",
        });
      }

      // Generate a temporary password
      const tempPassword = generateTempPassword();

      // Check if user already exists with this email
      let user = await User.findOne({ email: employee.email });
      const existingUser = !!user;

      // Find the employee role
      const employeeRole = await Role.findOne({ name: "employee" });
      if (!employeeRole) {
        return res.status(500).json({
          success: false,
          message: "Employee role not found",
        });
      }

      // Create or update user
      if (existingUser) {
        user.companies = [...new Set([...user.companies, employee.company])];
        if (!user.currentCompany) {
          user.currentCompany = employee.company;
        }
      } else {
        user = new User({
          firstName: employee.firstName,
          lastName: employee.lastName,
          email: employee.email,
          bio: "Tell us about yourself",
          password: tempPassword,
          role: employeeRole._id,
          companies: [employee.company],
          currentCompany: employee.company,
          isVerified: false,
          howDidYouHearAboutUs: "Other", // Set default value for self-service users
        });
      }

      await user.save();

      // Update employee
      employee.user = user._id;
      employee.selfServiceEnabled = true;
      employee.selfServiceToken = crypto.randomBytes(32).toString("hex");
      employee.selfServiceTokenExpiration = new Date(
        Date.now() + 24 * 60 * 60 * 1000
      ); // 24 hours

      // Ensure personalDetails exists and has idType set
      if (!employee.personalDetails) {
        employee.personalDetails = {};
      }

      // Only set idType if it doesn't exist to avoid overwriting existing data
      if (!employee.personalDetails.idType) {
        employee.personalDetails.idType = "other"; // Set default value to pass validation
      }

      await employee.save();

      // Send setup email
      const setupLink = `${process.env.BASE_URL}/employee/setup/${employee.selfServiceToken}`;

      // Ensure proper FROM address formatting
      const fromAddress = process.env.EMAIL_USER;
      const fromName = process.env.EMAIL_FROM_NAME || "Panda Software Solutions Group";

      if (!fromAddress) {
        throw new Error("EMAIL_USER environment variable is required for sending emails");
      }

      const mailOptions = {
        from: `"${fromName}" <${fromAddress}>`,
        to: employee.email,
        subject: "Set Up Your Employee Self-Service Account",
        html: `
          <h1>Welcome to Employee Self-Service</h1>
          <p>Hello ${employee.firstName},</p>
          <p>Your self-service account has been enabled. Please click the link below to set up your account:</p>
          <a href="${setupLink}">${setupLink}</a>
          <p>This link will expire in 24 hours.</p>
          <p>If you did not request this, please ignore this email.</p>
        `,
      };

      // Send email with better error handling
      try {
        console.log("Sending self-service setup email to:", employee.email);
        const emailInfo = await transporter.sendMail(mailOptions);
        console.log("Email sent successfully:", emailInfo.messageId);
      } catch (emailError) {
        console.error("Failed to send setup email:", emailError);
        // Don't fail the entire operation if email fails
        console.log("Self-service enabled but email failed to send");
      }

      res.json({
        success: true,
        message: "Self-service enabled and setup email sent",
      });
    } catch (error) {
      console.error("Error enabling self-service:", error);
      res.status(500).json({
        success: false,
        message: "Failed to enable self-service",
        error: error.message,
      });
    }
  }
);

router.get(
  "/:companyCode/employeeProfile/:employeeId/classifications",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId);

      if (!company || !employee) {
        req.flash("error", "Company or Employee not found");
        return res.redirect("/");
      }

      res.render("classifications", {
        company,
        employee,
        user: req.user,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering classifications form:", error);
      req.flash("error", "Failed to load classifications form");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// POST route for classifications
router.post(
  "/:companyCode/employeeProfile/:employeeId/classifications",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const {
        workingHours,
        isDirector,
        typeOfDirector,
        isContractor,
        isUifExempt,
        uifExemptReason,
      } = req.body;

      // Find company first
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Update employee
      const employee = await Employee.findOneAndUpdate(
        { _id: employeeId, company: company._id },
        {
          $set: {
            workingHours,
            isDirector: !!isDirector,
            typeOfDirector: isDirector ? typeOfDirector : null,
            isContractor: !!isContractor,
            isUifExempt: !!isUifExempt,
            uifExemptReason: isUifExempt ? uifExemptReason : null,
          },
        },
        { new: true }
      );

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      req.flash("success", "Classifications updated successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      console.error("Error updating classifications:", error);
      req.flash("error", "Failed to update classifications");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}/classifications`
      );
    }
  }
);

// GET route for RFI configuration
router.get(
  "/:companyCode/employeeProfile/:employeeId/defineRFI",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const company = await Company.findOne({ companyCode });
      const employee = await Employee.findById(employeeId)
        .populate("rfiConfig.selectedIncomes.componentId")
        .populate("rfiConfig.percentageIncomes.componentId");

      // Get the employee's payroll record with basic salary
      const payroll = await Payroll.findOne({ employee: employeeId });
      const assignedComponents = [];

      // Add basic salary if it exists in payroll
      if (payroll?.basicSalary) {
        assignedComponents.push({
          id: "basicSalary",
          name: "Basic Salary",
          amount: payroll.basicSalary,
          type: "income",
          isSelected:
            employee.rfiConfig?.selectedIncomes?.some(
              (inc) => inc.name === "Basic Salary"
            ) || false,
        });
      }

      // Add other components from payroll if they exist and have values
      if (payroll) {
        // Commission
        if (payroll.commission && payroll.commission.amount) {
          assignedComponents.push({
            id: "commission",
            name: "Commission",
            amount: payroll.commission.amount,
            type: "income",
            isSelected:
              employee.rfiConfig?.selectedIncomes?.some(
                (inc) => inc.name === "Commission"
              ) || false,
          });
        }

        // Travel Allowance
        if (payroll.travelAllowance && payroll.travelAllowance.amount) {
          assignedComponents.push({
            id: "travelAllowance",
            name: "Travel Allowance",
            amount: payroll.travelAllowance.amount,
            type: "allowance",
            isSelected:
              employee.rfiConfig?.selectedIncomes?.some(
                (inc) => inc.name === "Travel Allowance"
              ) || false,
          });
        }

        // Add other allowances if they exist
        if (payroll.allowances && payroll.allowances.length > 0) {
          payroll.allowances.forEach((allowance) => {
            if (allowance.amount) {
              assignedComponents.push({
                id: allowance._id.toString(),
                name: allowance.name,
                amount: allowance.amount,
                type: "allowance",
                isSelected:
                  employee.rfiConfig?.selectedIncomes?.some(
                    (inc) => inc.name === allowance.name
                  ) || false,
              });
            }
          });
        }

        // Add any other relevant components
        // ... add other components as needed
      }

      // Sort components by type and name
      assignedComponents.sort((a, b) => {
        if (a.type === b.type) {
          return a.name.localeCompare(b.name);
        }
        return a.type === "income" ? -1 : 1;
      });

      res.render("defineRFI", {
        company,
        employee,
        payComponents: assignedComponents, // Pass the assigned components
        rfiConfig: employee.rfiConfig || {
          option: "",
          selectedIncomes: [],
          percentageIncomes: {},
        },
        validation: RFI_VALIDATION,
        moment,
        csrfToken: req.csrfToken(),
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering RFI configuration form:", error);
      req.flash(
        "error",
        "An error occurred while loading the RFI configuration"
      );
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// POST route for RFI configuration
router.post(
  "/:companyCode/employeeProfile/:employeeId/defineRFI",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const { companyCode, employeeId } = req.params;
      const { option, selectedIncomes, percentageValues } = req.body;

      // Get company first
      const company = await Company.findOne({ companyCode });
      if (!company) {
        await session.abortTransaction();
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Validate required fields
      if (!option) {
        await session.abortTransaction();
        return res.status(400).json({
          success: false,
          message: "RFI calculation method is required",
        });
      }

      // Validate based on selected option
      if (option === "selectedIncome") {
        if (
          !selectedIncomes ||
          !Array.isArray(selectedIncomes) ||
          selectedIncomes.length === 0
        ) {
          await session.abortTransaction();
          return res.status(400).json({
            success: false,
            message: "At least one income component must be selected",
          });
        }
      } else if (option === "percentagePerIncome") {
        if (!percentageValues || Object.keys(percentageValues).length === 0) {
          await session.abortTransaction();
          return res.status(400).json({
            success: false,
            message:
              "Percentage values are required for at least one component",
          });
        }

        // Validate percentage values
        for (const [component, percentage] of Object.entries(
          percentageValues
        )) {
          const value = parseFloat(percentage);
          if (isNaN(value) || value < 0 || value > 100) {
            await session.abortTransaction();
            return res.status(400).json({
              success: false,
              message: `Invalid percentage value for ${component}. Must be between 0 and 100`,
            });
          }
        }
      }

      // Update employee RFI configuration
      const updatedEmployee = await Employee.findByIdAndUpdate(
        employeeId,
        {
          $set: {
            "rfiConfig.option": option,
            "rfiConfig.selectedIncomes":
              option === "selectedIncome"
                ? selectedIncomes.map((name) => ({
                    name,
                    includeInCalculation: true,
                  }))
                : [],
            "rfiConfig.percentageIncomes":
              option === "percentagePerIncome"
                ? Object.entries(percentageValues).map(
                    ([name, percentage]) => ({
                      name,
                      percentage: parseFloat(percentage),
                    })
                  )
                : [],
          },
        },
        { new: true, session }
      );

      if (!updatedEmployee) {
        await session.abortTransaction();
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Add audit log with company ID
      await AuditLog.create(
        [
          {
            user: req.user._id,
            company: company._id, // Add company ID here
            action: "UPDATE_RFI_CONFIG",
            entity: "Employee",
            entityId: employeeId,
            details: {
              option,
              selectedIncomes:
                option === "selectedIncome" ? selectedIncomes : [],
              percentageValues:
                option === "percentagePerIncome" ? percentageValues : {},
              timestamp: new Date(),
            },
          },
        ],
        { session }
      );

      await session.commitTransaction();

      res.json({
        success: true,
        message: "RFI configuration updated successfully",
        data: updatedEmployee.rfiConfig,
        redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}`,
      });
    } catch (error) {
      await session.abortTransaction();
      console.error("Error updating RFI configuration:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to update RFI configuration",
      });
    } finally {
      session.endSession();
    }
  }
);

const RFI_VALIDATION = {
  MIN_PERCENTAGE: 0,
  MAX_PERCENTAGE: 100,
  MIN_COMPONENTS: 1,
  REQUIRED_COMPONENTS: ["basicSalary"], // Components that must be included
};
// Add these validation rules at the top of the file
const FUND_VALIDATION = {
  FIXED_AMOUNT: {
    MIN: 0,
    MAX: 1000000000, // 1 billion
    DECIMALS: 2,
  },
  PERCENTAGE: {
    MIN: 0,
    MAX: 100,
    DECIMALS: 2,
  },
  CATEGORY_FACTOR: {
    MIN: 0.01,
    MAX: 10,
    DECIMALS: 2,
  },
};

// Add validation middleware
const validateFundInput = (req, res, next) => {
  const {
    contributionCalculation,
    fixedContributionEmployee,
    fixedContributionEmployer,
    rfiEmployee,
    rfiEmployer,
    categoryFactor,
  } = req.body;

  const errors = [];

  try {
    // Validate contribution method
    if (!["fixedAmount", "percentageRFI"].includes(contributionCalculation)) {
      errors.push("Invalid contribution calculation method");
    }

    // Validate fixed amounts if that method is selected
    if (contributionCalculation === "fixedAmount") {
      const employeeAmount = parseFloat(fixedContributionEmployee);
      const employerAmount = parseFloat(fixedContributionEmployer);

      if (
        isNaN(employeeAmount) ||
        employeeAmount < FUND_VALIDATION.FIXED_AMOUNT.MIN ||
        employeeAmount > FUND_VALIDATION.FIXED_AMOUNT.MAX
      ) {
        errors.push(
          `Employee fixed contribution must be between ${FUND_VALIDATION.FIXED_AMOUNT.MIN} and ${FUND_VALIDATION.FIXED_AMOUNT.MAX}`
        );
      }

      if (
        isNaN(employerAmount) ||
        employerAmount < FUND_VALIDATION.FIXED_AMOUNT.MIN ||
        employerAmount > FUND_VALIDATION.FIXED_AMOUNT.MAX
      ) {
        errors.push(
          `Employer fixed contribution must be between ${FUND_VALIDATION.FIXED_AMOUNT.MIN} and ${FUND_VALIDATION.FIXED_AMOUNT.MAX}`
        );
      }
    }

    // Validate RFI percentages if that method is selected
    if (contributionCalculation === "percentageRFI") {
      const employeePercentage = parseFloat(rfiEmployee);
      const employerPercentage = parseFloat(rfiEmployer);

      if (
        isNaN(employeePercentage) ||
        employeePercentage < FUND_VALIDATION.PERCENTAGE.MIN ||
        employeePercentage > FUND_VALIDATION.PERCENTAGE.MAX
      ) {
        errors.push(
          `Employee RFI percentage must be between ${FUND_VALIDATION.PERCENTAGE.MIN} and ${FUND_VALIDATION.PERCENTAGE.MAX}`
        );
      }

      if (
        isNaN(employerPercentage) ||
        employerPercentage < FUND_VALIDATION.PERCENTAGE.MIN ||
        employerPercentage > FUND_VALIDATION.PERCENTAGE.MAX
      ) {
        errors.push(
          `Employer RFI percentage must be between ${FUND_VALIDATION.PERCENTAGE.MIN} and ${FUND_VALIDATION.PERCENTAGE.MAX}`
        );
      }
    }

    // Validate category factor
    if (categoryFactor) {
      const factor = parseFloat(categoryFactor);
      if (
        isNaN(factor) ||
        factor < FUND_VALIDATION.CATEGORY_FACTOR.MIN ||
        factor > FUND_VALIDATION.CATEGORY_FACTOR.MAX
      ) {
        errors.push(
          `Category factor must be between ${FUND_VALIDATION.CATEGORY_FACTOR.MIN} and ${FUND_VALIDATION.CATEGORY_FACTOR.MAX}`
        );
      }
    }

    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        errors: errors,
      });
    }

    next();
  } catch (error) {
    console.error("Fund validation error:", error);
    res.status(500).json({
      success: false,
      message: "Error validating fund configuration",
    });
  }
};

// GET route for regular hours page
router.get(
  "/:companyCode/:employeeProfile/:employeeId/regularHours",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Fetch the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Fetch the employee
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      });
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Render the regular hours page
      res.render("regularHours", {
        company,
        employee,
        csrfToken: req.csrfToken(), // Add CSRF token
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering regular hours form:", error);
      req.flash("error", "Failed to load regular hours form");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

router.post(
  "/:companyCode/employeeProfile/:employeeId/regularHours",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const { hourlyPaid, hoursPerDay, schedule, workingDays, dayType } =
        req.body;


      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      });
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Calculate full days per week
      const calculatedFullDays = (workingDays || []).reduce((total, day) => {
        return total + (dayType[day] === "Half Day" ? 0.5 : 1);
      }, 0);

      // Update regular hours in Employee model
      employee.regularHours = {
        hourlyPaid: hourlyPaid === "on",
        hoursPerDay: parseFloat(hoursPerDay) || 8.0,
        schedule: schedule || "Fixed",
        workingDays: Array.isArray(workingDays) ? workingDays : [],
        dayTypes: {
          Mon: dayType.Mon || "Normal Day",
          Tue: dayType.Tue || "Normal Day",
          Wed: dayType.Wed || "Normal Day",
          Thu: dayType.Thu || "Normal Day",
          Fri: dayType.Fri || "Normal Day",
          Sat: dayType.Sat || "Normal Day",
          Sun: dayType.Sun || "Normal Day",
        },
        fullDaysPerWeek: calculatedFullDays,
        lastUpdated: new Date(),
      };

      // Also update the Payroll model
      const payroll = await Payroll.findOne({
        employee: employeeId,
        company: company._id,
      });

      if (payroll) {
        payroll.hourlyPaid = hourlyPaid === "on";
        await payroll.save();
      }

      await employee.save();

      // For XHR/Fetch requests
      if (req.xhr || req.headers.accept?.includes("application/json")) {
        return res.json({
          success: true,
          message: "Regular hours updated successfully",
          redirectUrl: `/clients/${companyCode}/employeeProfile/${employeeId}`,
        });
      }

      // For traditional form submissions
      req.flash("success", "Regular hours updated successfully");
      return res.redirect(
        `/clients/${companyCode}/employeeProfile/${employeeId}`
      );
    } catch (error) {
      console.error("Error updating regular hours:", error);

      // For XHR/Fetch requests
      if (req.xhr || req.headers.accept?.includes("application/json")) {
        return res.status(500).json({
          success: false,
          message: error.message || "Failed to update regular hours",
        });
      }

      // For traditional form submissions
      req.flash("error", "Failed to update regular hours");
      return res.redirect(
        `/clients/${companyCode}/employeeProfile/${employeeId}/regularHours`
      );
    }
  }
);

router.get(
  "/:companyCode/:employeeProfile/:employeeId/eti",
  ensureAuthenticated,
  csrfProtection, // Make sure this middleware is here
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find company first
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Find employee
      const employee = await Employee.findById(employeeId);
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Generate CSRF token
      const csrfToken = req.csrfToken();

      // Render with all necessary data
      res.render("eti", {
        company,
        employee,
        user: req.user,
        csrfToken, // Pass the token explicitly
        messages: req.flash(),
        moment: require("moment"), // Add moment if needed for date formatting
      });
    } catch (error) {
      console.error("Error rendering ETI page:", error);
      req.flash("error", "An error occurred while loading the ETI page");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}`
      );
    }
  }
);

// POST route for ETI updates
router.post(
  "/:companyCode/employeeProfile/:employeeId/eti",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const { status, effectiveFrom } = req.body;

      // Find employee
      const employee = await Employee.findById(employeeId);
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Ensure date is first of the month
      const effectiveDate = new Date(effectiveFrom);
      effectiveDate.setDate(1);
      effectiveDate.setHours(0, 0, 0, 0);

      // Add current status to history before updating
      if (employee.etiStatus) {
        employee.etiHistory.push({
          status: employee.etiStatus,
          effectiveDate: employee.etiEffectiveFrom,
          updatedBy: req.user._id,
          updatedAt: new Date(),
        });
      }

      // Update current status with first-of-month date
      employee.etiStatus = status;
      employee.etiEffectiveFrom = effectiveDate;

      await employee.save();

      req.flash("success", "ETI status updated successfully");
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    } catch (error) {
      console.error("Error updating ETI status:", error);
      req.flash("error", "Failed to update ETI status");
      res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}/eti`
      );
    }
  }
);

router.get(
  "/:companyCode/:employeeProfile/:employeeId/skillsEquity",
  ensureAuthenticated,
  csrfProtection, // Add CSRF protection middleware
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      });
      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      res.render("skillsEquity", {
        company,
        employee,
        user: req.user,
        csrfToken: req.csrfToken(), // Pass CSRF token to view
        messages: req.flash(),
      });
    } catch (error) {
      console.error("Error rendering Skills Equity page:", error);
      req.flash(
        "error",
        "An error occurred while loading the Skills Equity page"
      );
      res.redirect(`/clients/${companyCode}/employeeProfile/${employeeId}`);
    }
  }
);

// POST route for updating Skills Equity
router.post(
  "/:companyCode/employeeProfile/:employeeId/skillsEquity",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;
      const {
        gender,
        race,
        occupationLevel,
        occupationCategory,
        jobValue,
        province,
        disabled,
        foreignNational,
        notRSACitizen,
      } = req.body;

      // First find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/");
      }

      // Find and update employee using company._id
      const employee = await Employee.findOneAndUpdate(
        {
          _id: employeeId,
          company: company._id, // Use company._id instead of req.company._id
        },
        {
          $set: {
            gender,
            race,
            occupationLevel,
            occupationCategory,
            jobValue,
            province,
            disabled: !!disabled,
            foreignNational: !!foreignNational,
            notRSACitizen: !!notRSACitizen,
          },
        },
        { new: true }
      );

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Success case
      req.flash("success", "Skills & Equity information updated successfully");
      return res.redirect(
        `/clients/${companyCode}/employeeProfile/${employeeId}`
      );
    } catch (error) {
      console.error("Error updating Skills & Equity:", error);
      req.flash("error", "Failed to update Skills & Equity information");
      return res.redirect(
        `/clients/${req.params.companyCode}/employeeProfile/${req.params.employeeId}/skillsEquity`
      );
    }
  }
);

// Add this route for handling the delete page
router.get(
  "/:companyCode/employeeProfile/:employeeId/delete",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        req.flash("error", "Company not found");
        return res.redirect("/clients");
      }

      // Find the employee
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      }).populate("company"); // Add populate to match the template's needs

      if (!employee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${companyCode}/employeeManagement`);
      }

      // Render the delete page
      res.render("deleteEmployee", {
        employee,
        companyCode,
        messages: req.flash(),
        csrfToken: req.csrfToken(),
      });
    } catch (error) {
      console.error("Error rendering delete employee page:", error);
      req.flash("error", "An error occurred while loading the delete page");
      res.redirect(`/clients/${req.params.companyCode}/employeeManagement`);
    }
  }
);

// Add this route for handling employee deletion
router.delete(
  "/:companyCode/employeeProfile/:employeeId/delete",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Find and delete the employee
      const deletedEmployee = await Employee.findOneAndDelete({
        _id: employeeId,
        company: company._id,
      });

      if (!deletedEmployee) {
        return res.status(404).json({
          success: false,
          message: "Employee not found",
        });
      }

      // Delete associated payroll records
      await Payroll.deleteMany({
        employee: employeeId,
        company: company._id,
      });

      // Delete associated payroll periods
      await PayrollPeriod.deleteMany({
        employee: employeeId,
        company: company._id,
      });


      // Add delay to ensure database operations are fully committed
      await new Promise(resolve => setTimeout(resolve, 150));

      const timestamp = Date.now();
      res.json({
        success: true,
        message: "Employee and all associated records deleted successfully",
        redirectUrl: `/clients/${companyCode}/employeeManagement?refresh=${timestamp}&action=delete`,
        timestamp: timestamp
      });
    } catch (error) {
      console.error("Error deleting employee:", error);
      res.status(500).json({
        success: false,
        message: "Failed to delete employee",
        error: error.message,
      });
    }
  }
);

// Update hourly status
router.post('/:companyCode/employee/:employeeId/hourly-status', ensureAuthenticated, csrfProtection, async (req, res) => {
  try {
    const { employeeId, companyCode } = req.params;
    const { hourlyPaid } = req.body;


    // Find company first to validate company code
    const company = await Company.findOne({ companyCode });
    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company not found'
      });
    }

    // Find the employee and ensure they belong to the company
    const employee = await Employee.findOne({
      _id: employeeId,
      company: company._id
    });

    if (!employee) {
      return res.status(404).json({
        success: false,
        message: 'Employee not found'
      });
    }

    // Update the hourly paid status
    if (!employee.regularHours) {
      employee.regularHours = {};
    }
    employee.regularHours.hourlyPaid = hourlyPaid;

    // Save the changes
    await employee.save();


    res.json({
      success: true,
      message: `Employee is now ${hourlyPaid ? 'hourly paid' : 'salaried'}`,
      hourlyPaid: hourlyPaid
    });
  } catch (error) {
    console.error('Error updating hourly paid status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update hourly paid status',
      error: error.message
    });
  }
});

// SURGICAL FIX: Add route to regenerate periods for employees with updated firstPayrollPeriodEndDate
router.post(
  "/:companyCode/employeeManagement/:employeeId/regenerate-periods",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, employeeId } = req.params;

      // Validate company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({ success: false, message: "Company not found" });
      }

      // Validate employee
      const employee = await Employee.findOne({
        _id: employeeId,
        company: company._id,
      }).populate("payFrequency");

      if (!employee) {
        return res.status(404).json({ success: false, message: "Employee not found" });
      }

      // Check if employee has firstPayrollPeriodEndDate configured
      if (!employee.payFrequency?.firstPayrollPeriodEndDate) {
        return res.status(400).json({
          success: false,
          message: "Employee does not have firstPayrollPeriodEndDate configured"
        });
      }

      // Regenerate periods
      const result = await PayrollService.regeneratePeriodsForEmployee(employee, {
        preserveFinalized: true, // Don't touch finalized periods
        dryRun: false
      });

      console.log(`✅ Period regeneration completed for employee ${employeeId}:`, result);

      res.json({
        success: true,
        message: `Successfully regenerated ${result.deletedCount} periods. ${result.preservedCount} finalized periods were preserved.`,
        ...result
      });

    } catch (error) {
      console.error("Error regenerating periods:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to regenerate periods"
      });
    }
  }
);

// Excel Export Route for Essentials
router.get(
  "/:companyCode/employeeManagement/essentials/export",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;

      // Validate company access
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          error: "Company not found"
        });
      }

      // Fetch employees
      const employees = await Employee.find({
        company: company._id
      }).select(
        'firstName lastName companyEmployeeNumber employeeNumber dob doa idType idNumber passportNumber email'
      ).sort({ lastName: 1, firstName: 1 });

      // Create workbook
      const workbook = XLSX.utils.book_new();

      // Prepare data for Excel
      const excelData = employees.map(employee => ({
        'Last Name': employee.lastName || '',
        'First Name': employee.firstName || '',
        'Employee Number': employee.companyEmployeeNumber || employee.employeeNumber || '',
        'Date of Birth': employee.dob ? moment(employee.dob).format('YYYY-MM-DD') : '',
        'Date of Appointment': employee.doa ? moment(employee.doa).format('YYYY-MM-DD') : '',
        'ID Type': employee.idType || 'None',
        'ID Number': employee.idNumber || '',
        'Passport Number': employee.passportNumber || '',
        'Email': employee.email || ''
      }));

      // Create worksheet
      const worksheet = XLSX.utils.json_to_sheet(excelData);

      // Set column widths
      const columnWidths = [
        { wch: 20 }, // Last Name
        { wch: 20 }, // First Name
        { wch: 15 }, // Employee Number
        { wch: 15 }, // Date of Birth
        { wch: 18 }, // Date of Appointment
        { wch: 12 }, // ID Type
        { wch: 15 }, // ID Number
        { wch: 15 }, // Passport Number
        { wch: 30 }  // Email
      ];
      worksheet['!cols'] = columnWidths;

      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Employee Essentials');

      // Generate Excel buffer
      const excelBuffer = XLSX.write(workbook, {
        type: 'buffer',
        bookType: 'xlsx'
      });

      // Set response headers
      const filename = `employee-essentials-${moment().format('YYYY-MM-DD')}.xlsx`;
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Content-Length', excelBuffer.length);

      // Send file
      res.send(excelBuffer);

    } catch (error) {
      console.error("Error exporting essentials to Excel:", error);
      res.status(500).json({
        success: false,
        error: "Export failed",
        message: "An error occurred while generating the Excel file"
      });
    }
  }
);

module.exports = router;
