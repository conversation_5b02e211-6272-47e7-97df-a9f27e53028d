const express = require("express");
const router = express.Router();
const { PDFDocument, rgb, StandardFonts } = require("pdf-lib");
const fs = require("fs").promises;
const path = require("path");
const mongoose = require("mongoose");
const Employee = require("../models/Employee");
const Payslip = require("../models/Payslip");
const payrollCalculations = require("../utils/payrollCalculations");
const moment = require("moment-timezone");
const PayrollPeriod = require("../models/PayrollPeriod");
const PayrollService = require("../services/PayrollService");
const Payroll = require("../models/Payroll");
const { ensureAuthenticated } = require("../config/ensureAuthenticated");
const csrf = require("csurf");
const Company = require("../models/Company");
const PayRun = require("../models/payRun");
const archiver = require('archiver');

// NEW: Enhanced Layout System (Feature Flag Controlled)
const { EnhancedPayslipGenerator } = require("../utils/enhancedPayslipGenerator");
const USE_ENHANCED_LAYOUT = process.env.USE_ENHANCED_PAYSLIP_LAYOUT === 'true';

const DEFAULT_TIMEZONE = "Africa/Johannesburg";

// 🚨 UTILITY FUNCTION: Calculate custom income dynamically like employeeProfile.ejs
function calculateCustomIncomeTotal(customIncomeItems) {
  if (!customIncomeItems || !Array.isArray(customIncomeItems) || customIncomeItems.length === 0) {
    return 0;
  }

  return customIncomeItems.reduce((total, item) => {
    let amount = 0;
    if (item.calculatedAmount && item.calculatedAmount > 0) {
      amount = Number(item.calculatedAmount);
    } else if (typeof item.amount === 'number') {
      amount = Number(item.amount);
    } else if (typeof item.monthlyAmount === 'number') {
      amount = Number(item.monthlyAmount);
    }
    return total + amount;
  }, 0);
}

/**
 * Calculate YTD PAYE using SARS-compliant cumulative method (payslip version)
 * @param {string} employeeId - Employee ID
 * @param {Date|string} currentPeriodDate - Current period date
 * @param {string} frequency - Pay frequency
 * @param {number} employeeAge - Employee age for tax calculation
 * @returns {Promise<Object>} - YTD PAYE calculation details
 */
async function calculateYTDPAYEForPayslip(employeeId, currentPeriodDate, frequency = 'monthly', employeeAge = 35) {
  try {
    console.log('=== YTD PAYE CALCULATION DEBUG (payslip) ===');

    // 1. Get tax year boundaries
    const currentDate = moment(currentPeriodDate);
    const currentYear = currentDate.year();

    const taxYearStart = currentDate.month() >= 2 ?
      moment(`${currentYear}-03-01`) :
      moment(`${currentYear - 1}-03-01`);

    const taxYearEnd = taxYearStart.clone().add(1, 'year').subtract(1, 'day');

    console.log('Tax year:', taxYearStart.format('YYYY-MM-DD'), 'to', taxYearEnd.format('YYYY-MM-DD'));

    // 2. Get all periods in tax year (finalized and current)
    const periodsInTaxYear = await PayrollPeriod.find({
      employee: employeeId,
      endDate: {
        $gte: taxYearStart.toDate(),
        $lte: taxYearEnd.toDate()
      }
    }).sort({ endDate: 1 });

    console.log('Total periods in tax year:', periodsInTaxYear.length);

    if (periodsInTaxYear.length === 0) {
      console.log('No periods found in tax year, returning zero values');
      return { ytdTax: 0, taxAlreadyPaid: 0, currentPAYE: 0, periodNumber: 0, annualizedIncome: 0 };
    }

    // Special case: If we only have one period and it's not finalized,
    // this is likely the first period being created - use traditional calculation
    if (periodsInTaxYear.length === 1 && !periodsInTaxYear[0].isFinalized) {
      console.log('⚠️ Single unfinalized period detected - using traditional calculation for first period');
      const basicSalary = periodsInTaxYear[0].basicSalary;
      const traditionalAnnualSalary = basicSalary * (frequency === 'weekly' ? 52 : frequency === 'biweekly' ? 26 : 12);

      const traditionalTaxResult = await payrollCalculations.calculateEnhancedPAYE({
        annualSalary: traditionalAnnualSalary,
        frequency: frequency,
        age: employeeAge,
        periodEndDate: currentPeriodDate
      });

      const traditionalPAYE = traditionalTaxResult.annualPAYE / (frequency === 'weekly' ? 52 : frequency === 'biweekly' ? 26 : 12);

      console.log('Traditional calculation for first period:', {
        basicSalary,
        annualSalary: traditionalAnnualSalary,
        monthlyPAYE: traditionalPAYE
      });

      return {
        ytdIncome: basicSalary,
        annualizedIncome: traditionalAnnualSalary,
        annualTax: traditionalTaxResult.annualPAYE,
        ytdTax: traditionalPAYE,
        taxAlreadyPaid: 0,
        currentPAYE: traditionalPAYE,
        periodNumber: 1,
        frequencyMultiplier: frequency === 'weekly' ? 52 : frequency === 'biweekly' ? 26 : 12
      };
    }

    // 3. Calculate YTD income and period number
    let ytdIncome = 0;
    let currentPeriodNumber = 0;
    let taxAlreadyPaid = 0;

    periodsInTaxYear.forEach((period) => {
      if (period.basicSalary && period.basicSalary > 0) {
        // FIXED: Use actual prorated amount for YTD income calculation
        const proratedPercentage = period.proratedPercentage || 100;
        const actualProratedAmount = (period.basicSalary * proratedPercentage) / 100;

        ytdIncome += actualProratedAmount;
        currentPeriodNumber++;

        console.log(`Period ${moment(period.endDate).format('YYYY-MM-DD')}: Full R${period.basicSalary}, Prorated ${proratedPercentage}% = R${actualProratedAmount}, Finalized: ${period.isFinalized}, PAYE: ${period.PAYE}`);
        console.log(`  - Start: ${moment(period.startDate).format('YYYY-MM-DD')}, End: ${moment(period.endDate).format('YYYY-MM-DD')}`);

        // Sum PAYE from previous finalized periods (exclude current period)
        const periodEndDate = moment(period.endDate);
        const currentEndDate = moment(currentPeriodDate);

        if (period.isFinalized && period.PAYE && periodEndDate.isBefore(currentEndDate, 'month')) {
          taxAlreadyPaid += period.PAYE;
          console.log(`✓ Adding tax from finalized period ${periodEndDate.format('YYYY-MM-DD')}: R${period.PAYE}`);
        } else if (period.isFinalized && period.PAYE) {
          console.log(`✗ Skipping current period ${periodEndDate.format('YYYY-MM-DD')}: R${period.PAYE} (same as current)`);
        } else if (!period.isFinalized) {
          console.log(`✗ Skipping unfinalized period ${periodEndDate.format('YYYY-MM-DD')}`);
        } else if (!period.PAYE) {
          console.log(`✗ Skipping period with no PAYE ${periodEndDate.format('YYYY-MM-DD')}`);
        }
      }
    });

    console.log('YTD Income:', ytdIncome);
    console.log('Current period number:', currentPeriodNumber);
    console.log('Tax already paid:', taxAlreadyPaid);

    // 4. Calculate fractional periods and annualized income (SARS-compliant method)
    const frequencyMultiplier = frequency === 'weekly' ? 52 :
                               frequency === 'biweekly' || frequency === 'bi-weekly' ? 26 : 12;

    // Calculate fractional periods based on proration percentages
    let fractionalPeriods = 0;

    periodsInTaxYear.forEach((period) => {
      if (period.basicSalary && period.basicSalary > 0) {
        const proratedPercentage = period.proratedPercentage || 100;
        const fractionalPeriod = proratedPercentage / 100;
        fractionalPeriods += fractionalPeriod;
        console.log(`Period ${moment(period.endDate).format('YYYY-MM-DD')}: ${fractionalPeriod.toFixed(4)} fractional periods`);
      }
    });

    // FIXED: Use fractional periods for annualization (SARS method)
    const annualizedIncome = (ytdIncome / fractionalPeriods) * frequencyMultiplier;

    console.log('Total fractional periods:', fractionalPeriods.toFixed(4));
    console.log('YTD Income:', ytdIncome);
    console.log('Annualized income (SARS method):', annualizedIncome);

    // 5. Calculate annual tax on annualized income
    const annualTaxResult = await payrollCalculations.calculateEnhancedPAYE({
      annualSalary: annualizedIncome,
      frequency: frequency,
      age: employeeAge,
      periodEndDate: currentPeriodDate
    });

    // Extract annual tax - use the correct property name from calculateEnhancedPAYE
    const annualTax = annualTaxResult.annualPAYE || annualTaxResult.annualTax || (annualTaxResult.monthlyPAYE * 12) || 0;

    console.log('Annual tax calculation result:', annualTaxResult);
    console.log('Extracted annual tax:', annualTax);

    // 6. Calculate YTD tax using fractional periods (SARS-compliant method)
    const ytdTax = (annualTax * fractionalPeriods) / frequencyMultiplier;

    console.log('YTD tax calculation:');
    console.log(`  Annual tax: R${annualTax}`);
    console.log(`  Fractional periods: ${fractionalPeriods.toFixed(4)}`);
    console.log(`  Frequency multiplier: ${frequencyMultiplier}`);
    console.log(`  YTD tax should be: R${ytdTax}`);

    // 7. Calculate current period PAYE
    let currentPAYE = Math.max(0, ytdTax - taxAlreadyPaid);

    // Special case: If this is the first period and no tax has been paid yet,
    // and we're getting a very high PAYE (indicating YTD calculation issue),
    // fall back to traditional monthly calculation
    if (currentPeriodNumber === 1 && taxAlreadyPaid === 0 && currentPAYE > (annualTax / frequencyMultiplier * 1.5)) {
      console.log('⚠️ First period with unusually high PAYE, using traditional calculation');
      currentPAYE = annualTax / frequencyMultiplier;
      console.log('Fallback PAYE for first period:', currentPAYE);
    }

    console.log('Current period PAYE:', currentPAYE);
    console.log('=== END YTD PAYE CALCULATION (payslip) ===');

    return {
      ytdIncome,
      annualizedIncome,
      annualTax: annualTax,
      ytdTax,
      taxAlreadyPaid,
      currentPAYE,
      periodNumber: currentPeriodNumber,
      frequencyMultiplier
    };

  } catch (error) {
    console.error('Error calculating YTD PAYE (payslip):', error);
    return { ytdTax: 0, taxAlreadyPaid: 0, currentPAYE: 0, periodNumber: 0 };
  }
}

// Initialize CSRF protection
const csrfProtection = csrf({ cookie: true });

// Helper function to format currency
const formatCurrency = (amount) => {
  return new Intl.NumberFormat("en-ZA", {
    style: "currency",
    currency: "ZAR",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

// POST: Finalize a payslip
router.post("/:payslipId/finalize", ensureAuthenticated, async (req, res) => {

  try {
    const { payslipId } = req.params;
    const payrollPeriod = await PayrollPeriod.findById(payslipId).populate("employee", "firstName lastName");

    if (!payrollPeriod) {
      return res.status(404).json({
        success: false,
        message: "Payroll period not found",
      });
    }

    // Check if user has access to this company
    const hasAccess = req.user.companies.some((companyId) =>
      companyId.equals(payrollPeriod.company)
    );

    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: "Not authorized to finalize this payslip",
      });
    }

    // Check if already finalized
    if (payrollPeriod.isFinalized) {
      return res.status(400).json({
        success: false,
        message: "Payroll period is already finalized",
      });
    }

    // Update current payroll period
    payrollPeriod.isFinalized = true;
    payrollPeriod.finalizedAt = new Date();
    payrollPeriod.finalizedBy = req.user._id;
    await payrollPeriod.save();

    // Get employee details for generating next period
    const employee = await Employee.findById(payrollPeriod.employee).populate(
      "payFrequency"
    );
    if (!employee) {
      throw new Error("Employee not found");
    }

    // Generate next period using the PayrollPeriod model's logic
    const newPayrollPeriod = await PayrollPeriod.generateNextPeriod(
      employee,
      payrollPeriod
    );
    if (!newPayrollPeriod) {
    }


    res.json({
      success: true,
      message: "Payroll period finalized successfully",
      newPeriodId: newPayrollPeriod?._id,
    });
  } catch (error) {
    console.error("Error finalizing payroll period:", error);
    res.status(500).json({
      success: false,
      message: "Error finalizing payroll period",
    });
  }
});

// Update the GET route
router.get("/download/:employeeId/:month", async (req, res) => {
  const { employeeId, month } = req.params;


  try {
    // Get employee with populated company and employerDetails
    const employee = await Employee.findById(employeeId).populate([
      {
        path: "company",
        populate: {
          path: "employerDetails",
        },
      },
      "payFrequency",
    ]);

    if (!employee) {
      return res.status(404).send("Employee not found");
    }

    // Find the payroll period
    const requestedDate = moment.tz(month, DEFAULT_TIMEZONE);
    const payrollPeriod = await PayrollPeriod.findOne({
      employee: employeeId,
      startDate: { $lte: requestedDate.toDate() },
      endDate: { $gte: requestedDate.toDate() },
    });

    if (!payrollPeriod) {
      return res.status(404).send("No payroll period found for this date");
    }

    // Get calculations from PayrollService
    const calculations = await PayrollService.calculatePayrollTotals(
      employeeId,
      payrollPeriod.endDate
    );

    // 🚨 SURGICAL FIX: Get payroll data using exact same approach as employeeManagement.js
    // This ensures custom income items are fetched correctly for the specific period
    const payroll = await Payroll.findOne({
      employee: employeeId,
      company: employee.company,  // Include company filter like employeeManagement.js
      month: payrollPeriod.endDate,
    });

    console.log('🔍 PAYSLIP: Payroll data fetch debug:', {
      employeeId,
      company: employee.company,
      month: payrollPeriod.endDate,
      payrollFound: !!payroll,
      customIncomeItemsCount: payroll?.customIncomeItems?.length || 0,
      payrollBasicSalary: payroll?.basicSalary
    });

    // 🚨 SURGICAL FIX: Use EXACT same period-specific approach as employeeProfile.ejs for basicSalary
    // PRINCIPLE: Each period should use ONLY its own data - no fallback logic needed
    // This matches how PayrollPeriod.basicSalary preserves historical data integrity

    // Determine finalized status correctly (matches model: isFinalized)
    const isFinalized = !!payrollPeriod?.isFinalized;

    console.log('🔍 PAYSLIP: Period-specific custom income query (like employeeProfile.ejs basicSalary approach):', {
      periodMonth: payrollPeriod.endDate,
      isFinalized,
      payrollFound: !!payroll,
      payrollCustomIncomeItems: payroll?.customIncomeItems?.length || 0
    });

    // Prefer period-specific snapshot for finalized periods (immutable)
    const periodCustomIncomeItems = Array.isArray(payrollPeriod?.customIncomeItems)
      ? payrollPeriod.customIncomeItems
      : [];

    // Start with correct source per status
    let displayCustomIncomeItems = isFinalized
      ? periodCustomIncomeItems // finalized → use snapshot on PayrollPeriod
      : (Array.isArray(payroll?.customIncomeItems) ? payroll.customIncomeItems : []); // draft → use Payroll for that month

    console.log('🔎 PAYSLIP: Custom income source summary', {
      isFinalized,
      periodItems: periodCustomIncomeItems.length,
      payrollItems: Array.isArray(payroll?.customIncomeItems) ? payroll.customIncomeItems.length : 0
    });

    // Allow fallback ONLY for non-finalized periods to improve UX while keeping history immutable
    if (!isFinalized && displayCustomIncomeItems.length === 0) {
      try {
        console.log('🔍 PAYSLIP: Searching for fallback custom income (non-finalized period only)...');
        const latestWithCI = await Payroll.findOne({
          employee: employeeId,
          company: employee.company,
          "customIncomeItems.0": { $exists: true }
        })
          .sort({ month: -1 })
          .select('customIncomeItems month')
          .lean();

        console.log('🔍 PAYSLIP: Fallback query result:', {
          found: !!latestWithCI,
          month: latestWithCI?.month,
          customIncomeItemsCount: latestWithCI?.customIncomeItems?.length || 0
        });

        if (latestWithCI?.customIncomeItems?.length > 0) {
          displayCustomIncomeItems = latestWithCI.customIncomeItems;
          console.log('✅ PAYSLIP: Using fallback customIncomeItems from month:', latestWithCI.month);
        }
      } catch (e) {
        console.warn('⚠️ PAYSLIP: Custom Income fallback lookup failed:', e?.message || e);
      }
    } else if (isFinalized && displayCustomIncomeItems.length === 0) {
      console.log('🔒 PAYSLIP: FINALIZED PERIOD - No fallback used. This period has no custom income.');
    }

    console.log('✅ PAYSLIP: Custom income items selected for rendering:', {
      periodMonth: payrollPeriod.endDate,
      isFinalized,
      customIncomeItemsCount: displayCustomIncomeItems.length,
      items: displayCustomIncomeItems.map(item => ({
        name: item.name,
        amount: item.amount,
        calculatedAmount: item.calculatedAmount,
        monthlyAmount: item.monthlyAmount
      }))
    });

    // Destructure all needed values - Fixed data structure mapping
    const {
      totalIncome,  // Fixed: Changed from 'grossIncome: totalIncome' to 'totalIncome'
      basicSalary,  // Added: Extract basic salary from calculations
      travelExpenses, // Added: Extract travel expenses from calculations
      deductions: {
        statutory: { paye: payrollServicePAYE, uif: payrollServiceUIF },
        courtOrders: { maintenanceOrder },
        total: totalDeductions,
      },
      netPay,
    } = calculations;

    // SURGICAL FIX: Use exact same basic salary source as employeeManagement.js
    // employeeManagement.js uses: let basicSalary = Number(payroll?.basicSalary) || defaultBasicSalary;
    const basicSalaryValue = Number(payroll?.basicSalary) || 0;

    // 🚨 SURGICAL FIX: Use EXACT same custom income calculation as employeeManagement.js (line 3473)
    // Use displayCustomIncomeItems (includes fallback logic) instead of payroll.customIncomeItems
    const customIncomeForUIF = (displayCustomIncomeItems && Array.isArray(displayCustomIncomeItems))
      ? displayCustomIncomeItems.reduce((total, item) => {
          let amount = 0;
          if (item.calculatedAmount && item.calculatedAmount > 0) {
            amount = Number(item.calculatedAmount);
          } else if (typeof item.amount === 'number') {
            amount = Number(item.amount);
          } else if (typeof item.monthlyAmount === 'number') {
            amount = Number(item.monthlyAmount);
          }
          return total + amount;
        }, 0)
      : 0;

    console.log('🎯 PAYSLIP: Custom income calculation (exact employeeManagement.js logic):', {
      basicSalary: basicSalaryValue,
      payrollCustomIncomeItems: payroll?.customIncomeItems?.length || 0,
      displayCustomIncomeItems: displayCustomIncomeItems?.length || 0,
      customIncomeForUIF,
      employee: `${employee.firstName} ${employee.lastName}`,
      customIncomeItems: displayCustomIncomeItems?.map(item => ({
        name: item.name,
        calculatedAmount: item.calculatedAmount,
        amount: item.amount,
        monthlyAmount: item.monthlyAmount,
        finalAmount: item.calculatedAmount > 0 ? item.calculatedAmount : (typeof item.amount === 'number' ? item.amount : item.monthlyAmount)
      })) || []
    });

    // 🚨 SURGICAL FIX: Define customIncomeItems for use in PDF generation
    // Use displayCustomIncomeItems (includes fallback logic) for PDF generation
    const customIncomeItems = displayCustomIncomeItems || [];

    // Calculate comprehensive totals including custom income
    const comprehensiveGrossIncome = basicSalaryValue + customIncomeForUIF;
    const comprehensiveUIF = Math.min(comprehensiveGrossIncome * 0.01, 177.12);

    console.log('✅ PAYSLIP: Comprehensive calculation applied:', {
      basicSalaryValue,
      customIncomeForUIF,
      comprehensiveGrossIncome,
      originalTotalIncome: totalIncome,
      comprehensiveUIF
    });

    // CONSOLIDATED: Calculate km reimbursement variables once for reuse throughout function
    const kmsTravelled = payrollPeriod?.data?.get('travelExpenses')?.kmsTravelled || 0;
    const userRatePerKm = payroll?.travelAllowance?.ratePerKm || 0;
    const sarsRatePerKm = 4.76; // SARS prescribed rate per km (should be configurable)
    const kmReimbursementTotal = userRatePerKm * kmsTravelled;
    const kmReimbursementSarsAllowable = sarsRatePerKm * kmsTravelled;
    const kmReimbursementTaxableAmount = Math.max(0, kmReimbursementTotal - kmReimbursementSarsAllowable);
    const kmReimbursementNonTaxableAmount = kmReimbursementSarsAllowable;

    // SURGICAL FIX: Use EXACT same calculation logic as employeeManagement.js Calculator Card
    // Calculate pro-rata details for calculation (same as employeeManagement.js)
    const proRataDetails = payrollCalculations.calculateProratedSalary(
      employee.doa,
      basicSalaryValue,
      employee,
      payrollPeriod.startDate,
      payrollPeriod.endDate
    );



    // SURGICAL FIX: Get PayrollService calculations using same method as employeeManagement.js
    const currentPeriodCalculations = await PayrollService.calculatePayrollTotals(
      employeeId,
      payrollPeriod.endDate,
      employee.company._id
    );

    // Get deductions result as fallback (same as employeeManagement.js)
    const baseTaxableIncome = payrollCalculations.calculateTotalTaxableIncome(
      payroll || {},
      employee.payFrequency?.frequency || "monthly"
    );

    const deductionsResult = payrollCalculations.calculateTotalDeductions(
      payroll || {},
      baseTaxableIncome,
      employee.payFrequency?.frequency || "monthly",
      payrollPeriod?.startDate,
      payrollPeriod?.endDate
    );

    const deductionsResultPAYE = deductionsResult.paye || 0;
    const deductionsResultUIF = deductionsResult.uif || 0;

    // SURGICAL FIX: Use EXACT same calculation logic as employeeManagement.js Calculator Card
    // Calculate annual salary based on actual frequency (same as employeeManagement.js)
    const periodsPerYear = employee.payFrequency.frequency === 'weekly' ? 52 :
                          employee.payFrequency.frequency === 'biweekly' ? 26 : 12;

    // Calculate PAYE using enhanced calculation (same as employeeManagement.js)
    const travelAllowanceAmountForPAYE = payroll?.travelAllowance?.fixedAllowanceAmount || 0;
    const travelAllowanceTaxableRateForPAYE = payroll?.travelAllowance?.only20PercentTax ? 0.2 : 0.8;

    // Get travel expenses amount from current period (same as employeeManagement.js)
    const travelExpensesAmount = payrollPeriod?.data?.get('travelExpenses')?.expenses || 0;
    const travelExpensesTaxableAmount = travelExpensesAmount * travelAllowanceTaxableRateForPAYE;

    // Use YTD PAYE calculation (same as employeeManagement.js and PayrollService.js)
    const ytdPAYEResult = await calculateYTDPAYEForPayslip(
      employee._id,
      payrollPeriod.endDate,
      employee.payFrequency.frequency,
      payrollCalculations.calculateAge(employee.dob)
    );

    // Use YTD calculation result as base
    let finalPAYE = ytdPAYEResult.currentPAYE;

    // Fallback: If YTD calculation returns 0, use traditional calculation
    if (finalPAYE === 0 && basicSalaryValue > 0) {
      console.log('⚠️ YTD PAYE returned 0, using traditional fallback calculation');
      const traditionalAnnualSalary = basicSalaryValue * periodsPerYear;
      const traditionalPayeResult = await payrollCalculations.calculateEnhancedPAYE({
        annualSalary: traditionalAnnualSalary,
        age: payrollCalculations.calculateAge(employee.dob),
        frequency: employee.payFrequency.frequency,
        accommodationBenefit: payroll?.accommodationBenefit || 0,
        travelAllowance: travelAllowanceAmountForPAYE,
        travelAllowanceTaxableRate: travelAllowanceTaxableRateForPAYE,
        periodEndDate: payrollPeriod?.endDate
      });
      finalPAYE = traditionalPayeResult.monthlyPAYE || traditionalPayeResult.periodPAYE || 0;
      console.log('Traditional fallback PAYE:', finalPAYE);
    }

    // Apply pro-rata to YTD PAYE result
    const proratedPAYE = (finalPAYE * proRataDetails.proratedPercentage) / 100;

    // DEBUG: Log YTD PAYE calculation details
    console.log('=== PAYSLIP YTD PAYE CALCULATION DEBUG ===');
    console.log('basicSalaryValue:', basicSalaryValue);
    console.log('YTD Income:', ytdPAYEResult.ytdIncome);
    console.log('Annualized Income:', ytdPAYEResult.annualizedIncome);
    console.log('YTD Tax should be:', ytdPAYEResult.ytdTax);
    console.log('Tax already paid:', ytdPAYEResult.taxAlreadyPaid);
    console.log('Base YTD PAYE:', ytdPAYEResult.currentPAYE);
    console.log('Final PAYE (with fallback):', finalPAYE);
    console.log('proRataDetails.proratedPercentage:', proRataDetails.proratedPercentage);
    console.log('proratedPAYE (YTD calculation):', proratedPAYE);
    console.log('Period number in tax year:', ytdPAYEResult.periodNumber);

    // Calculate UIF using the same method as employeeManagement.js
    const calculatedProratedAmount = parseFloat(proRataDetails.proratedSalary);
    const proratedUIF = Math.min(
      parseFloat((calculatedProratedAmount * 0.01).toFixed(2)),
      177.12
    );



    // CRITICAL FIX: For finalized periods, use stored PAYE value to preserve historical accuracy
    // For unfinalized periods, use YTD calculation (same as employeeManagement.js)
    const isCurrentPeriodFinalized = payrollPeriod?.isFinalized;
    const storedPAYE = payrollPeriod?.PAYE;

    console.log('=== PAYSLIP FINALIZED PERIOD CHECK ===');
    console.log('Is current period finalized:', isCurrentPeriodFinalized);
    console.log('Stored PAYE in database:', storedPAYE);
    console.log('YTD calculated PAYE:', proratedPAYE);
    console.log('KM reimbursement taxable amount:', kmReimbursementTaxableAmount);

    const paye = Number(
      kmReimbursementTaxableAmount > 0
        ? proratedPAYE  // Always use fresh calculation when km reimbursement exists
        : (isCurrentPeriodFinalized && storedPAYE > 0)
          ? storedPAYE  // Use historical PAYE for finalized periods
          : (proratedPAYE ||  // Use YTD calculation for unfinalized periods
             currentPeriodCalculations.deductions?.statutory?.paye ||
             deductionsResultPAYE ||
             0)
    ).toFixed(2);

    console.log('Final PAYE used for payslip:', paye);
    console.log('Source:', kmReimbursementTaxableAmount > 0 ? 'Fresh (km reimbursement)' :
                         (isCurrentPeriodFinalized && storedPAYE > 0) ? 'Stored (finalized)' : 'YTD calculation');

    const uif = Number(
      comprehensiveUIF ||
      currentPeriodCalculations.deductions?.statutory?.uif ||
      deductionsResultUIF ||
      proratedUIF
    );

    // CRITICAL FIX: Use gross PAYE for now, will recalculate total deductions after medical aid tax credits are calculated
    // This ensures Total Deductions reflects the net PAYE amount after tax credits are applied
    const tempTotalDeductions = parseFloat(payrollServicePAYE) + parseFloat(uif) + (maintenanceOrder || 0);









    // Extract Loss of Income data
    const lossOfIncomeAmount = (payroll?.lossOfIncomeEnabled && payroll?.lossOfIncome) ? payroll.lossOfIncome : 0;

    // Extract Travel Allowance data for SDL calculation
    const travelAllowanceAmount = payroll?.travelAllowance?.fixedAllowanceAmount || 0;
    const travelAllowanceTaxableRate = payroll?.travelAllowance?.only20PercentTax ? 0.2 : 0.8;
    const travelAllowanceTaxableAmount = travelAllowanceAmount * travelAllowanceTaxableRate;

    // Extract Travel Expenses data from calculations (use already declared travelExpensesAmount)
    const travelExpensesTaxableAmountFromCalc = travelExpenses?.taxableAmount || 0;
    const travelExpensesNonTaxableAmount = travelExpenses?.nonTaxableAmount || 0;

    // Calculate km reimbursement using SARS-compliant method (using consolidated variables already calculated above)

    // Calculate combined travel allowance and expenses (excluding km reimbursement for separate display)
    const combinedTravelAmount = travelAllowanceAmount + travelExpensesAmount;
    const combinedTravelTaxableAmount = travelAllowanceTaxableAmount + travelExpensesTaxableAmountFromCalc;

    // PENSION FUND CALCULATIONS - Use same logic as employeeProfile.ejs and employeeManagement.js
    let pensionFundEmployeeContribution = 0;
    let pensionFundEmployerContribution = 0;

    if (payroll?.pensionFund) {
      if (payroll.pensionFund.contributionCalculation === 'fixedAmount') {
        pensionFundEmployeeContribution = Number(payroll.pensionFund.fixedContributionEmployee || 0);
        pensionFundEmployerContribution = Number(payroll.pensionFund.fixedContributionEmployer || 0);
      } else if (payroll.pensionFund.contributionCalculation === 'percentageRFI' && employee.rfiConfig?.lastCalculation?.amount) {
        // Calculate based on RFI percentage
        const rfiAmount = Number(employee.rfiConfig.lastCalculation.amount || 0);
        const rfiEmployeePercentage = Number(payroll.pensionFund.rfiEmployee || 0);
        const rfiEmployerPercentage = Number(payroll.pensionFund.rfiEmployer || 0);
        pensionFundEmployeeContribution = (rfiAmount * rfiEmployeePercentage) / 100;
        pensionFundEmployerContribution = (rfiAmount * rfiEmployerPercentage) / 100;
      }
    }

    // Combined pension fund contribution for retirement deduction (affects taxable income)
    const pensionFundRetirementDeduction = pensionFundEmployeeContribution + pensionFundEmployerContribution;
    const combinedTravelNonTaxableAmount = (travelAllowanceAmount - travelAllowanceTaxableAmount) + travelExpensesNonTaxableAmount;

    // Calculate medical aid amounts using same logic as Calculator Card
    const medicalAidEmployerContribution = payroll?.medical?.employerContribution || 0;

    // When employee handles payment: employer contribution becomes paid-out allowance
    // When company handles payment: employer contribution is taxable benefit
    const medicalAidPaidOutAllowance = (payroll?.medical?.employeeHandlesPayment && medicalAidEmployerContribution > 0)
      ? medicalAidEmployerContribution
      : 0;

    const medicalAidTaxableBenefit = (!payroll?.medical?.employeeHandlesPayment && medicalAidEmployerContribution > 0)
      ? medicalAidEmployerContribution
      : 0;

    // Calculate income using same logic as Calculator Card with comprehensive custom income
    const baseIncomeAmount = comprehensiveGrossIncome || totalIncome || basicSalaryValue || 0;
    const safeTotalIncome = baseIncomeAmount; // For display purposes

    // Calculate total income including allowances (same as employeeManagement.js)
    const totalIncomeWithAllowances = baseIncomeAmount + medicalAidPaidOutAllowance;

    // Calculate taxable income for PAYE calculation (FIXED: use same logic as employeeManagement.js)
    // SURGICAL FIX: Only subtract employee pension fund contribution from taxable income (not employer contribution)
    // employeeProfile.ejs subtracts only employee contribution, not total retirement deduction
    const baseTaxableIncomeForPAYE = payroll?.medical?.employeeHandlesPayment
      ? totalIncomeWithAllowances  // Employee pays: use total income (includes paid-out allowance) - FIXED!
      : safeTotalIncome + medicalAidTaxableBenefit;  // Company pays: add taxable benefit

    const taxableIncomeForPAYE = baseTaxableIncomeForPAYE - pensionFundEmployeeContribution;

    // CRITICAL FIX: Use temporary total deductions for now, will recalculate net pay after medical aid calculations
    const tempNetPay = safeTotalIncome - tempTotalDeductions;

    // Calculate total income for display (excluding combined travel allowance, expenses, km reimbursement, and accommodation benefit for employee clarity)
    const accommodationBenefitAmount = payroll?.accommodationBenefit || 0;
    const totalIncomeForDisplay = safeTotalIncome - combinedTravelAmount - kmReimbursementTotal - accommodationBenefitAmount;

    // Calculate SDL base (exclude Loss of Income and travel amounts, but include only taxable portions)
    // SDL should be calculated on employment income + travel taxable amounts + km reimbursement taxable amount only, not insurance payouts or non-taxable allowances
    const sdlBase = safeTotalIncome - lossOfIncomeAmount - combinedTravelAmount - kmReimbursementTotal + combinedTravelTaxableAmount + kmReimbursementTaxableAmount;
    const sdl = Math.max(0, sdlBase) * 0.01;




    // Calculate medical aid credit using safe total income (use payroll data like employeeManagement.js)
    const medicalAidImpact = await PayrollService.calculateMedicalAidTaxImpact(
      payroll?.medical || {},
      employee.payFrequency.frequency,
      safeTotalIncome
    );

    // Calculate medical aid tax credits using PayrollService method (conditional based on checkbox)
    const applyTaxCredits = !payroll?.medical?.dontApplyTaxCredits;
    const medicalAidTaxCredit = applyTaxCredits
      ? await PayrollService.calculateMedicalAidTaxCredit(
          payroll || {},
          employee.payFrequency?.frequency || "monthly",
          payrollPeriod?.endDate
        )
      : 0;

    const medicalAidCredit = medicalAidTaxCredit || medicalAidImpact.taxCredit || 0;

    // CRITICAL FIX: For finalized periods, use stored PAYE; for unfinalized, use prorated YTD calculation
    // Apply medical aid tax credits appropriately (same as employeeManagement.js)
    const grossPAYE = (isCurrentPeriodFinalized && storedPAYE > 0)
      ? storedPAYE  // Use historical PAYE for finalized periods
      : proratedPAYE;  // Use prorated YTD calculation for unfinalized periods

    // Calculate annual taxable income for logging purposes (maintain compatibility)
    const annualTaxableIncome = taxableIncomeForPAYE * 12;

    // DEBUG: Log finalized period decision
    console.log('=== PAYSLIP PAYE SOURCE DECISION ===');
    console.log('Using stored PAYE:', (isCurrentPeriodFinalized && storedPAYE > 0));
    console.log('Gross PAYE used:', grossPAYE);
    console.log('YTD-based proratedPAYE (for comparison):', proratedPAYE);
    console.log('payrollServicePAYE (for comparison):', payrollServicePAYE);
    console.log('Source used:', (isCurrentPeriodFinalized && storedPAYE > 0) ? 'Stored (finalized)' : 'YTD calculation');
    const netPAYE = Math.max(0, grossPAYE - medicalAidTaxCredit);
    // Use gross PAYE when tax credits are disabled, net PAYE when enabled
    const payeForDisplay = applyTaxCredits ? netPAYE : grossPAYE;

    // Calculate medical aid amounts early for debug logging
    const medicalAidTotal = payroll?.medical?.medicalAid || 0;
    // medicalAidEmployerContribution already declared above - no need to redeclare
    // const medicalAidEmployerContribution = payroll?.medical?.employerContribution || 0;
    // medicalAidTaxableBenefit already declared above - no need to redeclare

    // Calculate medical aid employee deduction using same logic as Calculator Card
    const medicalAidEmployeeDeduction = payroll?.medical?.employeeHandlesPayment
      ? 0  // Employee pays - don't show in deductions
      : (medicalAidTotal
          ? (medicalAidTotal - medicalAidEmployerContribution)
          : 0);

    // Use medicalAidEmployeeDeduction for consistency with Calculator Card
    const medicalAidAmount = medicalAidEmployeeDeduction;

    // Recalculate total deductions with net PAYE (after medical aid tax credits) and pension fund employee contribution
    const updatedTotalDeductions = parseFloat(payeForDisplay) + parseFloat(uif) + (maintenanceOrder || 0) + parseFloat(medicalAidAmount) + parseFloat(pensionFundEmployeeContribution || 0);

    // Recalculate net pay with updated total deductions (after medical aid tax credits applied)
    // Include medical aid paid-out allowance in net pay calculation
    const updatedNetPay = safeTotalIncome + medicalAidPaidOutAllowance - updatedTotalDeductions;

    // Debug medical aid data in payslip
    console.log('=== PAYSLIP MEDICAL AID DEBUG ===');
    console.log('Payroll medical data:', payroll?.medical);
    console.log('Base income amount (for display):', baseIncomeAmount);
    console.log('Safe total income (for display):', safeTotalIncome);
    console.log('Total income with allowances:', totalIncomeWithAllowances);
    console.log('Medical aid paid-out allowance:', medicalAidPaidOutAllowance);
    console.log('Medical aid taxable benefit:', medicalAidTaxableBenefit);
    console.log('Medical aid employee deduction:', medicalAidEmployeeDeduction);
    console.log('Taxable income for PAYE (FIXED):', taxableIncomeForPAYE);
    console.log('Annual taxable income for PAYE (FIXED):', annualTaxableIncome);
    console.log('Medical aid employer contribution:', medicalAidEmployerContribution);
    console.log('Medical aid taxable benefit:', medicalAidTaxableBenefit);
    console.log('Apply tax credits:', applyTaxCredits);
    console.log('Medical aid tax credit:', medicalAidTaxCredit);
    console.log('Medical aid total:', medicalAidTotal);
    console.log('Medical aid amount (employee deduction):', medicalAidAmount);
    console.log('Gross PAYE (before tax credits):', grossPAYE);
    console.log('Net PAYE (after tax credits):', netPAYE);
    console.log('PAYE for display (conditional):', payeForDisplay);
    console.log('Updated total deductions:', updatedTotalDeductions);
    console.log('Updated net pay (includes medical aid allowance):', updatedNetPay);

    // FIXED: Format dates consistently using BusinessDate fields to prevent timezone issues
    // ISSUE RESOLVED: Period end date now shows correct last day of month (e.g., 31/05/2025 instead of 01/06/2025)
    const formattedStartDate = payrollPeriod.startDateBusiness ?
      moment(payrollPeriod.startDateBusiness).format("DD/MM/YYYY") :
      moment(payrollPeriod.startDate).format("DD/MM/YYYY");

    const formattedEndDate = payrollPeriod.endDateBusiness ?
      moment(payrollPeriod.endDateBusiness).format("DD/MM/YYYY") :
      moment(payrollPeriod.endDate).format("DD/MM/YYYY");

    const periodDisplay = `${formattedStartDate} - ${formattedEndDate}`;

    // Add debug logging with corrected values



    // Create PDF document
    const pdfDoc = await PDFDocument.create();
    const page = pdfDoc.addPage([595.28, 841.89]); // A4 size
    const { width, height } = page.getSize();

    // Load fonts
    const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const helveticaBoldFont = await pdfDoc.embedFont(
      StandardFonts.HelveticaBold
    );

    // Define helper functions first
    const drawText = (text, x, y, options = {}) => {
      const {
        font = helveticaFont,
        size = 8,
        color = rgb(0, 0, 0),
        align = "left",
      } = options;

      // Handle numbers and format them safely
      if (typeof text === "number") {
        text = text.toLocaleString("en-ZA", {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        });
      }

      const textWidth = font.widthOfTextAtSize(text.toString(), size);
      const textX =
        align === "center"
          ? x - textWidth / 2
          : align === "right"
          ? x - textWidth
          : x;
      page.drawText(text.toString(), { x: textX, y, font, size, color });
    };

    // FIXED: Helper function with dynamic column positioning to prevent overlap
    // ISSUE RESOLVED: SDL/UIF-Employer amounts no longer overlap with Basic Salary/Total Income
    const drawTextWithSpace = (label, amount, x, y, options = {}) => {
      drawText(label, x, y, options);

      // DYNAMIC COLUMN POSITIONING: Use correct amount column based on label position
      // This prevents the overlap issue where all amounts were positioned at X=280
      let amountColumnX;
      if (x <= tableLayout.col2) {
        // Left side labels (Income, Deductions) use left amount column
        amountColumnX = tableLayout.col2; // 250px - prevents overlap
      } else {
        // Right side labels (Employer Contributions, Tax Credits) use right amount column
        amountColumnX = tableLayout.col4; // 500px - clear separation
      }
      // RESULT: 250px separation between left (250px) and right (500px) amount columns

      drawText(formatCurrency(amount), amountColumnX, y, {
        ...options,
        align: "right",
      });
    };

    // Helper function to format currency safely
    const formatCurrency = (amount) => {
      const safeAmount = Number(amount) || 0;
      return new Intl.NumberFormat("en-ZA", {
        style: "currency",
        currency: "ZAR",
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(safeAmount);
    };

    // Add DRAFT watermark if period is not finalized
    if (!payrollPeriod.isFinalized) {
      // Draw diagonal DRAFT text
      const draftText = "DRAFT";
      const fontSize = 60;
      const draftWidth = helveticaBoldFont.widthOfTextAtSize(
        draftText,
        fontSize
      );

      // Calculate rotation in radians (45 degrees)
      const rotationAngle = Math.PI / 4; // 45 degrees in radians

      page.drawText(draftText, {
        x: (width - draftWidth) / 2,
        y: height / 2,
        font: helveticaBoldFont,
        size: fontSize,
        color: rgb(0.9, 0.3, 0.3), // Light red color
        opacity: 0.3, // Semi-transparent
        rotate: {
          type: "degrees",
          angle: 45,
          origin: {
            x: width / 2,
            y: height / 2,
          },
        },
      });

      // Add draft notice at the top
      const noticeText = "This is a draft payslip - Not finalized";
      page.drawText(noticeText, {
        x: 50,
        y: height - 30,
        font: helveticaBoldFont,
        size: 10,
        color: rgb(0.9, 0.3, 0.3),
      });
    }

    // Add logo if it exists - Enhanced with consistent padding
    if (employee.company.employerDetails && employee.company.employerDetails.logo) {
      try {
        const logoPath = path.join(
          __dirname,
          "..",
          employee.company.employerDetails.logo
        );
        const logoFile = await fs.readFile(logoPath);
        const logoImage = await pdfDoc.embedPng(logoFile);
        const logoWidth = 80; // Reduced from 100 for better proportions
        const logoHeight = (logoWidth / logoImage.width) * logoImage.height;

        // Enhanced logo positioning with consistent top and bottom padding
        const logoPaddingTop = 30;    // Top padding for visual spacing
        const logoPaddingBottom = 20; // Bottom padding for visual spacing

        page.drawImage(logoImage, {
          x: 50,
          y: height - logoPaddingTop - logoHeight, // Consistent top padding
          width: logoWidth,
          height: logoHeight,
        });
      } catch (error) {
        console.error("Error loading logo:", error);
      }
    }

    // ENHANCED LAYOUT SYSTEM - Define layout zones to prevent overlapping
    const layoutZones = {
      header: { startY: height - 20, endY: height - 120 },
      companyInfo: { startY: height - 130, endY: height - 200 },
      employeeInfo: { startY: height - 210, endY: height - 280 },
      payslipData: { startY: height - 290, endY: 100 }
    };

    // Company header section (properly spaced from logo)
    const companyName = employee.company.employerDetails?.tradingName ||
                       employee.company.name ||
                       "Company Name";

    // Position company name with proper spacing from logo
    drawText(
      companyName,
      width / 2,
      layoutZones.header.startY - 40, // Clear spacing from top
      { font: helveticaBoldFont, size: 12, align: "center" }
    );

    // FIXED: Company address and employee details at same vertical level for balanced header
    const headerContentStartY = layoutZones.companyInfo.startY;
    const cityTown = employee.company.employerDetails?.physicalAddress?.cityTown || "City";
    const postalCode = employee.company.employerDetails?.physicalAddress?.code || "Postal Code";

    // Company address (right-aligned)
    drawText(
      cityTown,
      width - 50,
      headerContentStartY,
      { align: "right", size: 9 }
    );
    drawText(
      postalCode,
      width - 50,
      headerContentStartY - 20,
      { align: "right", size: 9 }
    );

    // FIXED: Employee details aligned with company address for balanced header layout
    const employeeStartY = headerContentStartY; // Same Y position as company address
    const lineSpacing = 18; // Consistent line spacing

    // VALIDATION: Ensure all critical variables are defined
    if (!employeeStartY || !lineSpacing) {
      console.error('❌ Critical layout variables undefined:', { employeeStartY, lineSpacing });
      throw new Error('Layout variables not properly initialized');
    }

    // Period information (prominent display)
    drawText(`Period: ${periodDisplay}`, 50, employeeStartY, {
      font: helveticaBoldFont,
      size: 10,
    });

    // Employee details with consistent spacing and alignment
    drawText(
      `Employee Name: ${employee.firstName} ${employee.lastName}`,
      50,
      employeeStartY - lineSpacing,
      { size: 9 }
    );
    drawText(
      `Pay Frequency: ${
        employee.payFrequency.frequency.charAt(0).toUpperCase() +
        employee.payFrequency.frequency.slice(1)
      }`,
      50,
      employeeStartY - (lineSpacing * 2),
      { size: 9 }
    );

    // Safely format employment date
    const employmentDate = employee.doa ?
        new Date(employee.doa).toLocaleDateString("en-ZA") :
        "Not specified";

    drawText(
      `Employment Date: ${employmentDate}`,
      50,
      employeeStartY - (lineSpacing * 3),
      { size: 9 }
    );

    // Horizontal line under employee details - FIXED: Use employeeStartY instead of undefined startY
    const lineY = employeeStartY - (lineSpacing * 4) - 10;

    // VALIDATION: Ensure lineY is properly calculated
    if (!lineY || lineY < 0) {
      console.error('❌ Invalid lineY calculation:', { employeeStartY, lineSpacing, lineY });
      throw new Error('Line positioning calculation failed');
    }
    page.drawLine({
      start: { x: 50, y: lineY },
      end: { x: width - 50, y: lineY },
      thickness: 1,
      color: rgb(0, 0, 0),
    });

    // FIXED: Enhanced table layout with proper column spacing to prevent overlap
    // ISSUE RESOLVED: SDL and UIF Employer amounts no longer overlap with Basic Salary/Total Income
    const tableLayout = {
      startY: lineY - 30,        // More space from employee details
      lineHeight: 22,            // Increased line height for better readability
      sectionSpacing: 35,        // Space between major sections
      col1: 50,                  // Left column for labels
      col2: 250,                 // Left amount column (FIXED: reduced from 280 to prevent overlap)
      col3: 320,                 // Right section start (FIXED: increased from 350 for better spacing)
      col4: 500                  // Right amount column (FIXED: adjusted from 530 for proper separation)
      // RESULT: 70px spacing between sections prevents amount overlap
    };

    // VALIDATION: Ensure table layout is properly initialized
    if (!tableLayout.startY || !tableLayout.lineHeight || !tableLayout.col1) {
      console.error('❌ Table layout variables undefined:', tableLayout);
      throw new Error('Table layout not properly initialized');
    }

    // Define clear section boundaries to prevent overlapping
    const sections = {
      income: {
        startY: tableLayout.startY,
        title: "INCOME",
        items: (lossOfIncomeAmount > 0 ? 3 : 2) + (customIncomeItems ? customIncomeItems.length : 0)  // Basic Salary + Custom Income Items (dynamic) + Loss of Income (if applicable) + Total Income
      },
      allowances: {
        startY: tableLayout.startY - (tableLayout.lineHeight * (lossOfIncomeAmount > 0 ? 5 : 4)),
        title: "ALLOWANCES",
        items: (combinedTravelAmount > 0 || kmReimbursementTotal > 0) ? 3 : 0  // Show if combined travel OR km reimbursement exists
      },
      deductions: {
        startY: tableLayout.startY - (tableLayout.lineHeight * ((lossOfIncomeAmount > 0 ? 5 : 4) + (combinedTravelAmount > 0 ? 4 : 0) + (kmReimbursementTotal > 0 ? (kmReimbursementTaxableAmount > 0 ? 3 : 2) : 0))),
        title: "DEDUCTIONS",
        items: 4  // Medical Aid + UIF + PAYE + Total
      },
      employer: {
        startY: tableLayout.startY,
        title: "EMPLOYER CONTRIBUTIONS",
        items: 2  // SDL + UIF Employer
      },
      taxCredit: {
        startY: tableLayout.startY - (tableLayout.lineHeight * 3),
        title: "TAX CREDITS",
        items: 1  // Medical Aid Tax Credit
      }
    };

    // SECTION HEADERS with enhanced styling and clear separation
    // Income section header
    drawText(sections.income.title, tableLayout.col1, sections.income.startY, {
      font: helveticaBoldFont,
      size: 10,
    });

    // Draw underline for Income section (adjusted for new column spacing)
    page.drawLine({
      start: { x: tableLayout.col1, y: sections.income.startY - 5 },
      end: { x: tableLayout.col2 + 30, y: sections.income.startY - 5 },
      thickness: 1,
      color: rgb(0.3, 0.3, 0.3),
    });

    // Employer Contribution section header
    drawText(sections.employer.title, tableLayout.col3, sections.employer.startY, {
      font: helveticaBoldFont,
      size: 10,
    });

    // Draw underline for Employer section (adjusted for new column spacing)
    page.drawLine({
      start: { x: tableLayout.col3, y: sections.employer.startY - 5 },
      end: { x: tableLayout.col4 + 30, y: sections.employer.startY - 5 },
      thickness: 1,
      color: rgb(0.3, 0.3, 0.3),
    });

    // INCOME SECTION with enhanced spacing and organization
    let currentIncomeY = sections.income.startY - tableLayout.lineHeight;

    // Basic Salary (clean single line)
    drawTextWithSpace(
      "Basic Salary",
      basicSalaryValue,
      tableLayout.col1,
      currentIncomeY,
      { size: 9 }
    );
    currentIncomeY -= tableLayout.lineHeight;

    // Custom Income Items (dynamic)
    if (customIncomeItems && customIncomeItems.length > 0) {
      customIncomeItems.forEach(item => {
        const itemAmount = item.calculatedAmount || item.amount || item.monthlyAmount || 0;
        if (itemAmount > 0) {
          drawTextWithSpace(
            item.name,
            itemAmount,
            tableLayout.col1,
            currentIncomeY,
            { size: 9 }
          );
          currentIncomeY -= tableLayout.lineHeight;
        }
      });
    }

    // Loss of Income (if applicable)
    if (lossOfIncomeAmount > 0) {
      drawTextWithSpace(
        "Loss of Income",
        lossOfIncomeAmount,
        tableLayout.col1,
        currentIncomeY,
        { size: 9 }
      );
      currentIncomeY -= tableLayout.lineHeight;
    }

    // Commission (if applicable)
    const commissionAmount = payroll?.commission || 0;
    if (commissionAmount > 0) {
      drawTextWithSpace(
        "Commission",
        commissionAmount,
        tableLayout.col1,
        currentIncomeY,
        { size: 9 }
      );
      currentIncomeY -= tableLayout.lineHeight;
    }

    // Total Income with visual emphasis (excluding travel allowance for display clarity)
    drawTextWithSpace(
      "Total Income",
      totalIncomeForDisplay,
      tableLayout.col1,
      currentIncomeY,
      { font: helveticaBoldFont, size: 10 }
    );

    // ALLOWANCES SECTION
    if (combinedTravelAmount > 0 || kmReimbursementTotal > 0) {
      const allowancesY = sections.allowances.startY;

      // Allowances section header
      drawText(sections.allowances.title, tableLayout.col1, allowancesY, {
        font: helveticaBoldFont,
        size: 10,
      });

      // Draw underline for Allowances section
      page.drawLine({
        start: { x: tableLayout.col1, y: allowancesY - 5 },
        end: { x: tableLayout.col2 + 50, y: allowancesY - 5 },
        thickness: 1,
        color: rgb(0, 0, 0),
      });

      let currentAllowanceY = allowancesY - tableLayout.lineHeight;

      // Combined Travel Allowance Fixed And Costs (only show if exists)
      if (combinedTravelAmount > 0) {
        drawTextWithSpace(
          "Travel Allowance Fixed And Costs",
          combinedTravelAmount,
          tableLayout.col1,
          currentAllowanceY,
          { size: 9 }
        );
        currentAllowanceY -= tableLayout.lineHeight;

        // Combined Travel Taxable Portion
        drawTextWithSpace(
          `Taxable (${payroll?.travelAllowance?.only20PercentTax ? '20%' : '80%'})`,
          combinedTravelTaxableAmount,
          tableLayout.col1,
          currentAllowanceY,
          { size: 8, color: rgb(0.3, 0.3, 0.3) }
        );
        currentAllowanceY -= tableLayout.lineHeight;

        // Combined Travel Non-taxable Portion
        drawTextWithSpace(
          `Non-taxable (${payroll?.travelAllowance?.only20PercentTax ? '80%' : '20%'})`,
          combinedTravelNonTaxableAmount,
          tableLayout.col1,
          currentAllowanceY,
          { size: 8, color: rgb(0.3, 0.3, 0.3) }
        );
        currentAllowanceY -= tableLayout.lineHeight;
      }

      // KM REIMBURSEMENT SECTION (continue from current position)
      if (kmReimbursementTotal > 0) {
        // Travel Allowance Reimbursive (SARS allowable portion)
        drawTextWithSpace(
          "Travel Allowance Reimbursive",
          kmReimbursementNonTaxableAmount,
          tableLayout.col1,
          currentAllowanceY,
          { size: 9 }
        );
        currentAllowanceY -= tableLayout.lineHeight;

        // Travel Allowance Reimbursive - taxable (excess over SARS rate)
        if (kmReimbursementTaxableAmount > 0) {
          drawTextWithSpace(
            "Travel Allowance Reimbursive - taxable",
            kmReimbursementTaxableAmount,
            tableLayout.col1,
            currentAllowanceY,
            { size: 9 }
          );
        }
      }
    }

    // DEDUCTIONS SECTION with enhanced organization and spacing
    const deductionsY = sections.deductions.startY;

    // Deductions section header
    drawText(sections.deductions.title, tableLayout.col1, deductionsY, {
      font: helveticaBoldFont,
      size: 10,
    });

    // Draw underline for Deductions section (adjusted for new column spacing)
    page.drawLine({
      start: { x: tableLayout.col1, y: deductionsY - 5 },
      end: { x: tableLayout.col2 + 30, y: deductionsY - 5 },
      thickness: 1,
      color: rgb(0.3, 0.3, 0.3),
    });

    // Individual deduction items with consistent spacing
    let currentDeductionY = deductionsY - tableLayout.lineHeight;

    // Medical Aid (only show if amount > 0) - variable already declared earlier
    if (medicalAidAmount > 0) {
      drawTextWithSpace(
        "Medical Aid",
        medicalAidAmount,
        tableLayout.col1,
        currentDeductionY,
        { size: 9 }
      );
      currentDeductionY -= tableLayout.lineHeight;
    }

    // Pension Fund - Employee (only show if amount > 0)
    if (pensionFundEmployeeContribution > 0) {
      drawTextWithSpace(
        "Pension Fund - Employee",
        pensionFundEmployeeContribution,
        tableLayout.col1,
        currentDeductionY,
        { size: 9 }
      );
      currentDeductionY -= tableLayout.lineHeight;
    }





    // UIF - FIXED: Remove double formatCurrency() call
    drawTextWithSpace("UIF", uif, tableLayout.col1, currentDeductionY, { size: 9 });
    currentDeductionY -= tableLayout.lineHeight;

    // PAYE - Use net PAYE (after medical aid tax credits)
    drawTextWithSpace("PAYE", payeForDisplay, tableLayout.col1, currentDeductionY, { size: 9 });
    currentDeductionY -= tableLayout.lineHeight;

    // Total Deductions with visual emphasis - FIXED: Use updated total deductions
    drawTextWithSpace(
      "Total Deductions",
      updatedTotalDeductions,
      tableLayout.col1,
      currentDeductionY,
      { font: helveticaBoldFont, size: 10 }
    );

    // EMPLOYER CONTRIBUTIONS SECTION with proper spacing
    const employerY = sections.employer.startY - tableLayout.lineHeight;

    // SDL
    drawTextWithSpace("SDL", sdl, tableLayout.col3, employerY, { size: 9 });

    // UIF - Employer
    drawTextWithSpace(
      "UIF - Employer",
      uif,
      tableLayout.col3,
      employerY - tableLayout.lineHeight,
      { size: 9 }
    );

    // Pension Fund - Employer (only show if amount > 0)
    if (pensionFundEmployerContribution > 0) {
      drawTextWithSpace(
        "Pension Fund - Employer",
        pensionFundEmployerContribution,
        tableLayout.col3,
        employerY - (tableLayout.lineHeight * 2),
        { size: 9 }
      );
    }

    // TAX CREDITS SECTION with enhanced organization (only show if tax credits are enabled and there are credits)
    const taxCreditY = sections.taxCredit.startY; // Define outside conditional for benefits positioning

    if (applyTaxCredits && medicalAidCredit > 0) {
      // Tax Credit section header
      drawText(sections.taxCredit.title, tableLayout.col3, taxCreditY, {
        font: helveticaBoldFont,
        size: 10,
      });

      // Draw underline for Tax Credits section (adjusted for new column spacing)
      page.drawLine({
        start: { x: tableLayout.col3, y: taxCreditY - 5 },
        end: { x: tableLayout.col4 + 30, y: taxCreditY - 5 },
        thickness: 1,
        color: rgb(0.3, 0.3, 0.3),
      });

      // Medical Aid Tax Credit
      drawTextWithSpace(
        "Medical Aid Tax Credit",
        medicalAidCredit,
        tableLayout.col3,
        taxCreditY - tableLayout.lineHeight,
        { size: 9 }
      );
    }

    // ALLOWANCES SECTION
    const allowancesY = taxCreditY - (tableLayout.lineHeight * 3);

    // Only show Allowances section if there are allowances to display
    if (medicalAidPaidOutAllowance > 0) {
      // Allowances section header
      drawText("ALLOWANCES", tableLayout.col3, allowancesY, {
        font: helveticaBoldFont,
        size: 10,
      });

      // Draw underline for Allowances section
      page.drawLine({
        start: { x: tableLayout.col3, y: allowancesY - 5 },
        end: { x: tableLayout.col4 + 30, y: allowancesY - 5 },
        thickness: 1,
        color: rgb(0.3, 0.3, 0.3),
      });

      let allowanceItemY = allowancesY - tableLayout.lineHeight;

      // Medical Aid Paid Out Allowance
      if (medicalAidPaidOutAllowance > 0) {
        drawTextWithSpace(
          "Medical Aid Paid Out Allowance",
          medicalAidPaidOutAllowance,
          tableLayout.col3,
          allowanceItemY,
          { size: 9 }
        );
        allowanceItemY -= tableLayout.lineHeight;
      }
    }

    // BENEFITS SECTION
    const benefitsY = medicalAidPaidOutAllowance > 0
      ? allowancesY - (tableLayout.lineHeight * 4)
      : taxCreditY - (tableLayout.lineHeight * 3);

    // Calculate petrol card taxable amount
    const petrolCardSpendAmount = payrollPeriod?.data?.get('travelExpenses')?.petrolCardSpend || 0;
    const petrolCardTaxableRate = payroll?.travelAllowance?.only20PercentTax ? 0.2 : 0.8;
    const petrolCardTaxableAmount = petrolCardSpendAmount * petrolCardTaxableRate;

    // Medical aid variables already declared earlier

    // Only show Benefits section if there are benefits to display
    if (petrolCardTaxableAmount > 0 || (payroll?.accommodationBenefit && payroll.accommodationBenefit > 0) || medicalAidTaxableBenefit > 0 || pensionFundEmployerContribution > 0) {
      // Benefits section header
      drawText("BENEFITS", tableLayout.col3, benefitsY, {
        font: helveticaBoldFont,
        size: 10,
      });

      // Draw underline for Benefits section
      page.drawLine({
        start: { x: tableLayout.col3, y: benefitsY - 5 },
        end: { x: tableLayout.col4 + 30, y: benefitsY - 5 },
        thickness: 1,
        color: rgb(0.3, 0.3, 0.3),
      });

      let currentBenefitY = benefitsY - tableLayout.lineHeight;

      // Medical Aid Employer Contribution (if applicable)
      if (medicalAidTaxableBenefit > 0) {
        drawTextWithSpace(
          "Medical Aid Employer Contribution",
          medicalAidTaxableBenefit,
          tableLayout.col3,
          currentBenefitY,
          { size: 9 }
        );
        currentBenefitY -= tableLayout.lineHeight;
      }

      // Accommodation Benefit (if applicable)
      if (payroll?.accommodationBenefit && payroll.accommodationBenefit > 0) {
        drawTextWithSpace(
          "Accommodation Benefit",
          payroll.accommodationBenefit,
          tableLayout.col3,
          currentBenefitY,
          { size: 9 }
        );
        currentBenefitY -= tableLayout.lineHeight;
      }

      // Pension Fund Benefit - Non-taxable
      if (pensionFundEmployerContribution > 0) {
        drawTextWithSpace(
          "Pension Fund Benefit",
          pensionFundEmployerContribution,
          tableLayout.col3,
          currentBenefitY,
          { size: 9 }
        );
        currentBenefitY -= tableLayout.lineHeight;
      }

      // Petrol Card - Taxable Portion (if applicable)
      if (petrolCardTaxableAmount > 0) {
        drawTextWithSpace(
          "Petrol Card - Taxable Portion",
          petrolCardTaxableAmount,
          tableLayout.col3,
          currentBenefitY,
          { size: 9 }
        );
      }
    }

    // TAXABLE INCOME DEDUCTION SECTION
    if (pensionFundRetirementDeduction > 0) {
      // Position section below benefits section
      const taxableDeductionY = 400; // Fixed position for taxable income deduction section

      // Section header
      drawText("Taxable Income Deduction", 50, taxableDeductionY, {
        font: helveticaBoldFont,
        size: 10,
      });

      let currentTaxableDeductionY = taxableDeductionY - 20;

      // Retirement Deduction (combined employee + employer pension fund contributions)
      drawTextWithSpace(
        "Retirement Deduction",
        pensionFundRetirementDeduction,
        tableLayout.col1,
        currentTaxableDeductionY,
        { size: 9, color: rgb(0.8, 0.2, 0.2) } // Red color to indicate deduction
      );
    }

    // ENHANCED NET PAY SECTION - Prominent display at bottom
    const netPayY = 80; // Better positioning from bottom

    // Prominent separator line above Net Pay
    page.drawLine({
      start: { x: 50, y: netPayY + 30 },
      end: { x: width - 50, y: netPayY + 30 },
      thickness: 2,
      color: rgb(0, 0, 0),
    });

    // FIXED: Net Pay with enhanced styling and right-column amount positioning
    // ISSUE RESOLVED: Net Pay amount now aligns with right column (same as SDL, UIF-Employer)
    drawText("NET PAY", 50, netPayY, {
      font: helveticaBoldFont,
      size: 14,  // Larger font for prominence
    });

    // Position Net Pay amount in right column for visual consistency - FIXED: Use updated net pay
    drawText(formatCurrency(updatedNetPay), tableLayout.col4, netPayY, {
      font: helveticaBoldFont,
      size: 14,
      align: "right",
    });

    // Bottom border line
    page.drawLine({
      start: { x: 50, y: netPayY - 20 },
      end: { x: width - 50, y: netPayY - 20 },
      thickness: 1,
      color: rgb(0.5, 0.5, 0.5),
    });

    // Add finalization status to the footer
    const statusText = payrollPeriod.isFinalized
      ? "FINALIZED PAYSLIP"
      : "DRAFT PAYSLIP - NOT FOR OFFICIAL USE";

    drawText(statusText, width / 2, 30, {
      font: helveticaBoldFont,
      size: 8,
      color: payrollPeriod.isFinalized ? rgb(0, 0, 0) : rgb(0.9, 0.3, 0.3),
      align: "center",
    });

    // Finalize and send the PDF
    const pdfBytes = await pdfDoc.save();
    res.setHeader("Content-Type", "application/pdf");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=payslip_${employee._id}_${
        payrollPeriod.endDate.toISOString().split("T")[0]
      }.pdf`
    );
    res.send(Buffer.from(pdfBytes));
  } catch (err) {
    console.error("Error in payslip generation:", err);
    res.status(500).send("An error occurred while generating the payslip.");
  }
});

// Get pending payslips count
router.get(
  "/:companyCode/payroll/pending-count",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const company = await Employee.findOne({ code: companyCode });

      if (!company) {
        return res.status(404).json({ error: "Company not found" });
      }

      // Get all pending payslips
      const pendingPayslips = await PayrollPeriod.find({
        company: company._id,
        isFinalized: false,
      }).populate("employee", "firstName lastName");

      // Format payslips for response
      const formattedPayslips = pendingPayslips.map((p) => ({
        employeeName: `${p.employee.firstName} ${p.employee.lastName}`,
        period:
          p.startDate.toLocaleDateString("en-ZA") +
          " - " +
          p.endDate.toLocaleDateString("en-ZA"),
      }));

      res.json({
        pendingCount: pendingPayslips.length,
        pendingPayslips: formattedPayslips,
      });
    } catch (error) {
      console.error("Error getting pending payslips count:", error);
      res.status(500).json({ error: "Failed to get pending payslips count" });
    }
  }
);

// POST: Create new pay run from finalized periods
router.post(
  "/:companyCode/payruns",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode } = req.params;
      const { periodIds, releaseToSelfService } = req.body;


      // 1. Validate company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({ error: "Company not found" });
      }


      // 2. Validate periods
      const periods = await PayrollPeriod.find({
        _id: { $in: periodIds },
        company: company._id,
        isFinalized: true,
        status: { $ne: "locked" },
      }).populate({
        path: "employee",
        select: "firstName lastName",
        populate: {
          path: "company",
          populate: {
            path: "employerDetails",
          },
        },
      });


      if (periods.length === 0) {
        return res.status(400).json({
          error: "No valid payroll periods found for pay run creation",
        });
      }

      if (periods.length !== periodIds.length) {
        return res.status(400).json({
          error:
            "Some payroll periods were not found or are not eligible for pay run creation",
        });
      }

      // 3. Sort and validate period dates
      periods.sort((a, b) => a.startDate - b.startDate);
      const firstPeriod = periods[0];
      const lastPeriod = periods[periods.length - 1];

      // 4. Generate monthYear based on the last period's end date
      const monthYear = moment(lastPeriod.endDate).format("YYYY-MM");

      // 5. Check for existing pay run with same company and monthYear
      const existingPayRun = await PayRun.findOne({
        company: company._id,
        monthYear: monthYear,
        status: { $ne: "draft" }, // Only check for non-draft pay runs
      });

      if (existingPayRun) {
        return res.status(400).json({
          error: `A finalized pay run already exists for ${moment(
            lastPeriod.endDate
          ).format("MMMM YYYY")}`,
          details: {
            existingPayRun: {
              id: existingPayRun._id,
              period: existingPayRun.period,
              status: existingPayRun.status,
            },
          },
        });
      }

      // 6. Create new pay run with unique identifier
      const payRunIdentifier = `${company._id}_${monthYear}_${Date.now()}`;
      const payRun = new PayRun({
        company: company._id,
        createdBy: req.user._id,
        status: releaseToSelfService ? "released" : "draft",
        payrollPeriods: periodIds,
        startDate: firstPeriod.startDate,
        endDate: lastPeriod.endDate,
        frequency: firstPeriod.frequency,
        monthYear: monthYear,
        period: moment(lastPeriod.endDate).format("MMMM YYYY"),
        taxPeriod: moment(lastPeriod.endDate).format("YYYYMM"),
        paymentDate: moment().toDate(),
        payRunIdentifier: payRunIdentifier,
      });


      // 7. Save pay run in a transaction
      const session = await mongoose.startSession();
      let savedPayRun;

      try {
        await session.withTransaction(async () => {
          // Save pay run
          savedPayRun = await payRun.save({ session });

          // Update payroll periods
          const updateResult = await PayrollPeriod.updateMany(
            {
              _id: { $in: periodIds },
              company: company._id,
              isFinalized: true,
              status: { $ne: "locked" },
            },
            {
              $set: {
                status: "locked",
                payRun: savedPayRun._id,
                updatedAt: new Date(),
              },
            },
            { session }
          );


          if (updateResult.modifiedCount !== periods.length) {
            throw new Error("Failed to update all payroll periods");
          }
        });

        // Send success response
        res.json({
          success: true,
          message: "Pay run created successfully",
          payRun: {
            id: savedPayRun._id,
            status: savedPayRun.status,
            periodCount: periodIds.length,
            period: savedPayRun.period,
            paymentDate: savedPayRun.paymentDate,
          },
        });
      } catch (error) {
        throw error;
      } finally {
        session.endSession();
      }
    } catch (error) {
      console.error("Error creating pay run:", error);

      if (
        error.code === 11000 &&
        error.keyPattern &&
        error.keyPattern.monthYear
      ) {
        return res.status(400).json({
          error: `A pay run already exists for this month`,
          details: {
            monthYear: error.keyValue.monthYear,
            message: error.message,
          },
        });
      }

      res.status(500).json({
        error: "Failed to create pay run",
        details: error.message,
      });
    }
  }
);

// GET: Download all payslips for a pay run
router.get("/bulk-download/:payRunId", ensureAuthenticated, async (req, res) => {
  
  try {
    const { payRunId } = req.params;
    
    if (!payRunId) {
      return res.status(400).send("Invalid pay run ID");
    }
    
    // First, get the raw pay run to check if it has payslips
    const rawPayRun = await PayRun.findById(payRunId);
    
    if (!rawPayRun) {
      return res.status(404).send("Pay run not found");
    }
    
    
    // Find the pay run with populated payslips and employees
    const payRun = await PayRun.findById(payRunId)
      .populate({
        path: "payslips",
        populate: {
          path: "employee",
          select: "firstName lastName employeeId company payFrequency doa bankDetails",
          populate: [
            {
              path: "company",
              populate: {
                path: "employerDetails",
              },
            },
            "payFrequency",
          ],
        },
      });
      
    if (payRun.payslips && payRun.payslips.length > 0) {
    }

    // Check if user has access to this company
    const hasAccess = req.user.companies.some((companyId) =>
      companyId.equals(payRun.company)
    );

    if (!hasAccess) {
      return res.status(403).send("Not authorized to access these payslips");
    }
    
    // Check if we have populated payslips
    if (!payRun.payslips || payRun.payslips.length === 0 || 
        (payRun.payslips.length > 0 && !payRun.payslips[0].employee)) {
      
      // If we have payslip IDs but they didn't populate, there might be an issue with the references
      if (rawPayRun.payslips && rawPayRun.payslips.length > 0) {
        
        // Try to find the payslips directly
        const directPayslips = await Payslip.find({
          _id: { $in: rawPayRun.payslips }
        }).populate({
          path: "employee",
          select: "firstName lastName employeeId company payFrequency doa bankDetails",
          populate: [
            {
              path: "company",
              populate: {
                path: "employerDetails",
              },
            },
            "payFrequency",
          ],
        });
        
        
        // If no results, try with Payroll model as fallback
        let finalPayslips = directPayslips;
        if (!directPayslips || directPayslips.length === 0) {
          try {
            // Check if Payroll model exists
            const Payroll = mongoose.models.Payroll || mongoose.model("Payroll");
            
            const payrolls = await Payroll.find({
              company: payRun.company,
              payPeriods: { 
                $elemMatch: {
                  startDate: { $lte: payRun.endDate },
                  endDate: { $gte: payRun.startDate }
                }
              }
            }).populate({
              path: "employee",
              select: "firstName lastName employeeId company payFrequency doa bankDetails",
              populate: [
                {
                  path: "company",
                  populate: {
                    path: "employerDetails",
                  },
                },
                "payFrequency",
              ],
            });
            
            if (payrolls && payrolls.length > 0) {
              // Filter out payrolls without employee data
              const validPayrolls = payrolls.filter(payroll => payroll.employee);
              
              if (validPayrolls.length > 0) {
                // Convert payrolls to a format compatible with payslips
                const payrollsAsPayslips = validPayrolls.map(payroll => {
                  // Find the relevant pay period
                  const relevantPeriod = payroll.payPeriods.find(period => 
                    period.startDate <= payRun.endDate && period.endDate >= payRun.startDate
                  );
                  
                  if (!relevantPeriod) return null;
                  
                  return {
                    _id: payroll._id,
                    employee: payroll.employee,
                    startDate: relevantPeriod.startDate,
                    endDate: relevantPeriod.endDate,
                    basicSalary: relevantPeriod.basicSalary || payroll.basicSalary || 0,
                    grossPay: relevantPeriod.total || payroll.grossPay || 0,
                    netPay: (relevantPeriod.total - (relevantPeriod.paye || 0) - (relevantPeriod.uif || 0)) || payroll.netPay || 0,
                    totalDeductions: ((relevantPeriod.paye || 0) + (relevantPeriod.uif || 0)) || payroll.totalDeductions || 0,
                    allowances: payroll.allowances || [],
                    deductions: payroll.deductions || [],
                    overtime: payroll.overtime || [],
                    medical: payroll.medical || {},
                    isFinalized: relevantPeriod.isFinalized || false
                  };
                }).filter(p => p !== null);
                
                
                if (payrollsAsPayslips.length > 0) {
                  finalPayslips = payrollsAsPayslips;
                } else {
                }
              } else {
              }
            } else {
            }
          } catch (fallbackError) {
            console.error("Error in Payroll model fallback:", fallbackError);
          }
          
          // If still no results, try with PayrollPeriod as final fallback
          if (!finalPayslips || finalPayslips.length === 0) {
            try {
              // Try to find PayrollPeriod directly using the IDs from the payRun
              const PayrollPeriod = mongoose.models.PayrollPeriod || mongoose.model("PayrollPeriod");
              
              // First try with the payslip IDs directly (they might actually be PayrollPeriod IDs)
              let payrollPeriods = await PayrollPeriod.find({
                _id: { $in: payRun.payrollPeriods || [] }
              }).populate({
                path: "employee",
                select: "firstName lastName employeeId company payFrequency doa bankDetails",
                populate: [
                  {
                    path: "company",
                    populate: {
                      path: "employerDetails",
                    },
                  },
                  "payFrequency",
                ],
              });
              
              // If that doesn't work, try by date range and company
              if (!payrollPeriods || payrollPeriods.length === 0) {
                payrollPeriods = await PayrollPeriod.find({
                  company: payRun.company,
                  startDate: { $lte: payRun.endDate },
                  endDate: { $gte: payRun.startDate }
                }).populate({
                  path: "employee",
                  select: "firstName lastName employeeId company payFrequency doa bankDetails",
                  populate: [
                    {
                      path: "company",
                      populate: {
                        path: "employerDetails",
                      },
                    },
                    "payFrequency",
                  ],
                });
              }
              
              
              if (payrollPeriods && payrollPeriods.length > 0) {
                // Check if any period has an employee that is just an ID reference and not populated
                for (let period of payrollPeriods) {
                  if (period.employee && typeof period.employee === 'string' || 
                      (period.employee && !period.employee.firstName)) {
                    try {
                      // Try to populate the employee data
                      const employee = await Employee.findById(period.employee).populate({
                        path: "company",
                        populate: {
                          path: "employerDetails",
                        },
                      });
                      
                      if (employee) {
                        period.employee = employee;
                      } else {
                      }
                    } catch (err) {
                      console.error(`Error fetching employee data: ${err.message}`);
                    }
                  }
                }
                
                // First, ensure all employee references are fully populated with company data
                for (const period of payrollPeriods) {
                  if (period.employee && period.employee._id) {
                    try {
                      // Make sure employee has company data populated
                      if (!period.employee.company || typeof period.employee.company === 'string') {
                        
                        // Get company ID from different sources
                        const companyId = period.company || 
                                         (typeof period.employee.company === 'string' ? period.employee.company : null);
                                         
                        if (companyId) {
                          const Company = mongoose.models.Company || mongoose.model("Company");
                          try {
                            const company = await Company.findById(companyId);
                            if (company) {
                              period.employee.company = company;
                            }
                          } catch (err) {
                            console.error(`Error finding company: ${err.message}`);
                          }
                        }
                      }
                    } catch (err) {
                      console.error(`Error populating company for period ${period._id}:`, err);
                    }
                  }
                }
                
                // Filter out periods without employee data
                const validPeriods = payrollPeriods.filter(period => {
                  if (!period.employee) {
                    return false;
                  }
                  return true;
                });
                
                if (validPeriods.length > 0) {
                  // Convert periods to a format compatible with payslips
                  const periodsAsPayslips = validPeriods.map(async period => {
                    try {
                      
                      // Create deep copy of employee object to prevent reference issues
                      let employeeData;
                      try {
                        employeeData = JSON.parse(JSON.stringify(period.employee));
                      } catch (copyError) {
                        console.error(`Error during deep copy of employee data: ${copyError.message}`);
                        // Use the original reference if deep copy failed
                        employeeData = period.employee;
                      }
                      
                      // Create a properly structured payslip with embedded employee data
                      const payslipFromPeriod = {
                        _id: period._id,
                        employee: employeeData,
                        startDate: period.startDate,
                        endDate: period.endDate,
                        basicSalary: period.basicSalary || 0,
                        grossPay: period.grossPay || 0,
                        netPay: period.netPay || 0,
                        totalDeductions: period.totalDeductions || 0,
                        allowances: period.allowances || [],
                        deductions: period.deductions || [],
                        overtime: period.overtime || [],
                        medical: period.medical || {},
                        isFinalized: period.isFinalized || false,
                        // Add tax-related fields if they exist
                        paye: period.PAYE || 0,
                        uif: period.UIF || 0,
                        sdl: period.SDL || 0
                      };
                      
                      // Extra verification to make sure employee data is present
                      if (!payslipFromPeriod.employee || !payslipFromPeriod.employee._id) {
                        // As a last resort, use the original employee reference if the deep copy failed
                        payslipFromPeriod.employee = period.employee;
                      }
                      
                      // Ensure company data is properly structured
                      if (payslipFromPeriod.employee && 
                          payslipFromPeriod.employee.company && 
                          typeof payslipFromPeriod.employee.company === 'string') {
                        try {
                          const Company = mongoose.models.Company || mongoose.model("Company");
                          const company = await Company.findById(payslipFromPeriod.employee.company);
                          if (company) {
                            payslipFromPeriod.employee.company = company;
                          }
                        } catch (err) {
                          console.error(`Error fetching company for payslip:`, err);
                        }
                      }
                      
                      return payslipFromPeriod;
                    } catch (mapError) {
                      console.error(`Error in payslip map function: ${mapError.message}`);
                      // Return a minimal valid payslip to prevent array holes
                      return {
                        _id: period._id || "error",
                        employee: period.employee || null,
                        startDate: period.startDate,
                        endDate: period.endDate
                      };
                    }
                  });
                  
                  // Wait for all payslips to be processed with their async operations
                  
                  try {
                    const resolvedPayslips = await Promise.allSettled(periodsAsPayslips);
                    
                    if (resolvedPayslips.length > 0) {
                      finalPayslips = resolvedPayslips.filter(p => p.status === 'fulfilled').map(p => p.value);
                    } else {
                    }
                  } catch (promiseErr) {
                    console.error("Error resolving payslip promises:", promiseErr);
                    // Fallback to directly using the array without waiting for promises
                    const directPayslips = periodsAsPayslips.map(promise => {
                      try {
                        if (promise.then) {
                          return {
                            _id: "unknown",
                            employee: null
                          };
                        }
                        return promise;
                      } catch (err) {
                        console.error("Error extracting from promise:", err);
                        return null;
                      }
                    }).filter(Boolean);
                    
                    if (directPayslips.length > 0) {
                      finalPayslips = directPayslips;
                    }
                  }
                } else {
                }
              } else {
              }
            } catch (periodError) {
              console.error("Error in PayrollPeriod fallback:", periodError);
            }
          }
        }
        
        if (finalPayslips && finalPayslips.length > 0) {
          
          // Extra verification of finalPayslips before assignment
          finalPayslips = finalPayslips.map(payslip => {
            // Make sure each payslip has employee data before proceeding
            if (!payslip.employee) {
              // Try to find the corresponding period to get employee data
              const matchingPeriod = payrollPeriods?.find(p => p._id.toString() === payslip._id.toString());
              if (matchingPeriod && matchingPeriod.employee) {
                payslip.employee = JSON.parse(JSON.stringify(matchingPeriod.employee));
              }
            }
            return payslip;
          });
          
          // Count payslips with valid employee data
          const validPayslips = finalPayslips.filter(p => p.employee && p.employee._id);
          
          // Instead of just assigning, create a deep copy to ensure references are properly preserved
          payRun.payslips = JSON.parse(JSON.stringify(finalPayslips));
          
          // Extra verification after assignment to ensure data integrity
        } else {
          return res.status(404).send("No valid payslips or payroll data found for this pay run after all fallback attempts");
        }
      } else {
        return res.status(404).send("No valid payslips or payroll data found for this pay run");
      }
    } // Add closing bracket for the if statement starting at line 839

    // Create a ZIP file containing all payslips
    const archive = archiver('zip', {
      zlib: { level: 9 } // Maximum compression
    });

    // Handle archive warnings
    archive.on('warning', function(err) {
      if (err.code === 'ENOENT') {
      } else {
        console.error('Archive error:', err);
        // Don't throw as this would crash the stream
      }
    });

    archive.on('error', function(err) {
      console.error('Archive error:', err);
      if (!res.headersSent) {
        res.status(500).send('Error creating archive: ' + err.message);
      }
    });

    // Set the headers with a safe filename
    const startDate = payRun.payPeriodStart ? payRun.payPeriodStart.toISOString().split('T')[0] : 
                      payRun.startDate ? payRun.startDate.toISOString().split('T')[0] : 
                      new Date().toISOString().split('T')[0];
    
    const endDate = payRun.payPeriodEnd ? payRun.payPeriodEnd.toISOString().split('T')[0] : 
                    payRun.endDate ? payRun.endDate.toISOString().split('T')[0] : 
                    new Date().toISOString().split('T')[0];
    
    res.attachment(`Payslips_${startDate}_to_${endDate}.zip`);
    archive.pipe(res);

    // Track successful and failed payslips
    let successCount = 0;
    let failureCount = 0;

    // Extra safety check - verify that payRun.payslips all have employee data
    
    // Generate each payslip and add to the archive
    for (const payslip of payRun.payslips) {
      try {
        // Last chance data recovery: if employee is missing for any reason, try to rebuild it
        if (!payslip.employee && payslip._id) {
          try {
            // Try to find the corresponding period to get employee data
            const matchingPeriod = payrollPeriods?.find(p => p._id.toString() === payslip._id.toString());
            if (matchingPeriod && matchingPeriod.employee) {
              payslip.employee = JSON.parse(JSON.stringify(matchingPeriod.employee));
            } else {
              // As a last resort, try with the Employee model directly
              const Employee = mongoose.models.Employee || mongoose.model("Employee");
              
              // Try to get employee ID from multiple sources
              const employeeId = payslip.employeeId || 
                               (payslip.employee && typeof payslip.employee === 'object' ? payslip.employee._id : null) ||
                               (payslip.employee && typeof payslip.employee === 'string' ? payslip.employee : null);
              
              if (employeeId) {
                // Extract ID string if it's an ObjectId
                const idString = typeof employeeId === 'object' && employeeId.toString ? 
                               employeeId.toString() : employeeId;
                
                const employee = await Employee.findById(idString).populate('company');
                if (employee) {
                  payslip.employee = JSON.parse(JSON.stringify(employee));
                } else {
                }
              } else {
              }
            }
          } catch (err) {
            console.error(`Error during emergency recovery: ${err.message}`);
          }
        }
        
        // Additional debug logging to trace the issue
        
        // Enhanced employee data validation
        if (!payslip.employee) {
          failureCount++;
          continue;
        }
        
        // Make sure employee has required fields
        if (!payslip.employee._id || !payslip.employee.firstName || !payslip.employee.lastName) {
          failureCount++;
          continue;
        }

        // Enhanced logging for employee data debugging
        
        // Check if employee has company data
        if (!payslip.employee.company) {
          
          try {
            // Try to find the company from the company ID if it exists
            if (payslip.company) {
              const Company = mongoose.models.Company || mongoose.model("Company");
              const company = await Company.findById(payslip.company);
              if (company) {
                payslip.employee.company = company;
              }
            }
            
            // If we still don't have company data, check if we have a companyId in the employee
            if (!payslip.employee.company && payslip.employee.company) {
              const Company = mongoose.models.Company || mongoose.model("Company");
              const company = await Company.findById(payslip.employee.company);
              if (company) {
                payslip.employee.company = company;
              }
            }
          } catch (companyError) {
            console.error(`Error fetching company for payslip ${payslip._id}:`, companyError);
          }
        }
        
        // Get calculations from PayrollService
        const calculations = await PayrollService.calculatePayrollTotals(
          payslip.employee._id,
          payslip.endDate || payRun.payPeriodEnd || payRun.endDate
        );

        // Get payroll data to access Loss of Income
        const payroll = await Payroll.findOne({
          employee: payslip.employee._id,
          month: payslip.endDate || payRun.payPeriodEnd || payRun.endDate,
        });

        // Find the payroll period for manual calculations
        const payrollPeriod = await PayrollPeriod.findOne({
          employee: payslip.employee._id,
          startDate: { $lte: payslip.endDate || payRun.payPeriodEnd || payRun.endDate },
          endDate: { $gte: payslip.endDate || payRun.payPeriodEnd || payRun.endDate },
        });

        // Destructure all needed values with fallbacks - Fixed data structure mapping
        const {
          totalIncome = calculations?.totalIncome || payslip.basicSalary || payslip.grossPay || 0,
          basicSalary = calculations?.basicSalary || payslip.basicSalary || 0,
          travelExpenses = calculations?.travelExpenses || {}, // Added: Extract travel expenses from calculations
          deductions: {
            statutory: { paye: payrollServicePAYE = 0, uif: payrollServiceUIF = 0 } = {},
            courtOrders: { maintenanceOrder = 0 } = {},
            total: totalDeductions = payslip.totalDeductions || 0
          } = { statutory: {}, courtOrders: {} },
          netPay = payslip.netPay || (totalIncome - totalDeductions) || 0,
        } = calculations || {};

        // CONSOLIDATED: Calculate km reimbursement variables once for reuse throughout function
        const kmsTravelled = payrollPeriod?.data?.get('travelExpenses')?.kmsTravelled || 0;
        const userRatePerKm = payroll?.travelAllowance?.ratePerKm || 0;
        const sarsRatePerKm = 4.76; // SARS prescribed rate per km (should be configurable)
        const kmReimbursementTotal = userRatePerKm * kmsTravelled;
        const kmReimbursementSarsAllowable = sarsRatePerKm * kmsTravelled;
        const kmReimbursementTaxableAmount = Math.max(0, kmReimbursementTotal - kmReimbursementSarsAllowable);
        const kmReimbursementNonTaxableAmount = Math.min(kmReimbursementTotal, kmReimbursementSarsAllowable);

        // Apply same 3-tier priority logic as Calculator card for PAYE consistency
        // Calculate manual PAYE as fallback (same as employeeManagement.js)
        let manualPAYE = 0;
        let manualUIF = 0;

        if ((!payrollServicePAYE || payrollServicePAYE === 0) && payrollPeriod) {
          // Calculate pro-rata details for manual calculation
          const proRataDetails = payrollCalculations.calculateProratedSalary(
            payslip.employee.doa,
            basicSalaryValue,
            payslip.employee,
            payrollPeriod.startDate,
            payrollPeriod.endDate
          );

          // Calculate travel allowance taxable rate for PAYE
          const travelAllowanceTaxableRateForPAYE = payroll?.travelAllowance?.only20PercentTax ? 0.2 : 0.8;

          // Get travel expenses amount from current period (same as employeeManagement.js)
          const travelExpensesAmount = payrollPeriod?.data?.get('travelExpenses')?.expenses || 0;

          // Add travel expenses taxable amount to annual salary for PAYE calculation (same as employeeManagement.js)
          const travelExpensesTaxableAmount = travelExpensesAmount * travelAllowanceTaxableRateForPAYE;

          // Get petrol card spend amount from current period (same method as travel expenses)
          const petrolCardSpendAmount = payrollPeriod?.data?.get('travelExpenses')?.petrolCardSpend || 0;

          // Add petrol card spend taxable amount to annual salary for PAYE calculation (same method as travel expenses)
          const petrolCardSpendTaxableAmount = petrolCardSpendAmount * travelAllowanceTaxableRateForPAYE;

          // Add km reimbursement taxable amount to annual salary for PAYE calculation (using consolidated variables)

          const periodsPerYear = (payslip.employee.payFrequency?.frequency || "monthly") === 'weekly' ? 52 :
                               (payslip.employee.payFrequency?.frequency || "monthly") === 'biweekly' ? 26 : 12;
          // SURGICAL FIX: Use exact same basic salary source as employeeManagement.js - Mobile
          // employeeManagement.js uses: let basicSalary = Number(payroll?.basicSalary) || defaultBasicSalary;
          const mobileBasicSalary = Number(payrollForMobile?.basicSalary) || 0;

          // 🚨 SURGICAL FIX: Apply comprehensive wage component calculations like employeeProfile.ejs - Mobile
          // Calculate custom income dynamically from actual custom income items - Mobile
          let mobileCustomIncomeItems = [];
          let mobileCustomIncomeForUIF = 0;

          if (payrollForMobile && Array.isArray(payrollForMobile.customIncomeItems)) {
            mobileCustomIncomeItems = payrollForMobile.customIncomeItems.filter(item =>
              item.inputType === 'customRateQuantity' &&
              item.customIncomeId &&
              item.name &&
              !item.name.toLowerCase().includes('test')
            );

            // 🔧 DYNAMIC CUSTOM INCOME CALCULATION (like employeeProfile.ejs)
            mobileCustomIncomeForUIF = calculateCustomIncomeTotal(mobileCustomIncomeItems);
          }

          console.log('🎯 MOBILE PAYSLIP: Dynamic custom income calculation:', {
            payrollCustomIncomeItems: payrollForMobile?.customIncomeItems?.length || 0,
            filteredMobileCustomIncomeItems: mobileCustomIncomeItems.length,
            mobileCustomIncomeForUIF,
            items: mobileCustomIncomeItems.map(item => ({ name: item.name, amount: item.calculatedAmount || item.amount || item.monthlyAmount }))
          });

          // Calculate comprehensive totals including custom income - Mobile
          const mobileComprehensiveGrossIncome = mobileBasicSalary + mobileCustomIncomeForUIF;
          const mobileComprehensiveUIF = Math.min(mobileComprehensiveGrossIncome * 0.01, 177.12);

          console.log('✅ MOBILE PAYSLIP: Comprehensive calculation applied:', {
            mobileBasicSalary,
            mobileCustomIncomeForUIF,
            mobileComprehensiveGrossIncome,
            mobileComprehensiveUIF
          });
          const adjustedAnnualSalary = mobileBasicSalary * periodsPerYear;

          // Manual PAYE calculation (same as employeeManagement.js)
          const payeCalculation = await payrollCalculations.calculateEnhancedPAYE({
            annualSalary: adjustedAnnualSalary,
            age: payrollCalculations.calculateAge(payslip.employee.dob),
            frequency: payslip.employee.payFrequency?.frequency || "monthly",
            accommodationBenefit: payroll?.accommodationBenefit || 0,
            travelAllowance: payroll?.travelAllowance?.fixedAllowanceAmount || 0,
            travelAllowanceTaxableRate: travelAllowanceTaxableRateForPAYE,
            periodEndDate: payrollPeriod?.endDate
          });

          const periodPAYE = payeCalculation.periodPAYE;
          manualPAYE = (periodPAYE * proRataDetails.proratedPercentage) / 100;

          // Manual UIF calculation (same as employeeManagement.js)
          const calculatedProratedAmount = parseFloat(proRataDetails.proratedSalary);
          manualUIF = Math.min(
            parseFloat((calculatedProratedAmount * 0.01).toFixed(2)),
            177.12
          );
        }

        // Calculate deductions using calculateTotalDeductions as second priority
        let deductionsResultPAYE = 0;
        let deductionsResultUIF = 0;

        if ((!payrollServicePAYE || payrollServicePAYE === 0) && payrollPeriod) {
          const baseTaxableIncome = payrollCalculations.calculateTotalTaxableIncome(
            payroll || {},
            payslip.employee.payFrequency?.frequency || "monthly"
          );

          // Add current period's km reimbursement taxable amount to taxable income (using consolidated variables)

          // Add travel expenses and petrol card spend taxable amounts
          const travelAllowanceTaxableRateForDeductions = payroll?.travelAllowance?.only20PercentTax ? 0.2 : 0.8;
          const travelExpensesAmount = payrollPeriod?.data?.get('travelExpenses')?.expenses || 0;
          const travelExpensesTaxableAmount = travelExpensesAmount * travelAllowanceTaxableRateForDeductions;
          const petrolCardSpendAmount = payrollPeriod?.data?.get('travelExpenses')?.petrolCardSpend || 0;
          const petrolCardSpendTaxableAmount = petrolCardSpendAmount * travelAllowanceTaxableRateForDeductions;

          const adjustedTaxableIncome = baseTaxableIncome + kmReimbursementTaxableAmount + travelExpensesTaxableAmount + petrolCardSpendTaxableAmount;

          const deductionsResult = payrollCalculations.calculateTotalDeductions(
            payroll || {},
            adjustedTaxableIncome,
            payslip.employee.payFrequency?.frequency || "monthly",
            payrollPeriod?.startDate,
            payrollPeriod?.endDate
          );

          deductionsResultPAYE = deductionsResult.paye || 0;
          deductionsResultUIF = deductionsResult.uif || 0;
        }

        // SURGICAL FIX: Get PayrollService calculations using same method as employeeManagement.js
        const currentPeriodCalculations = await PayrollService.calculatePayrollTotals(
          payslip.employee._id,
          payrollPeriod.endDate,
          payslip.employee.company._id || payslip.employee.company
        );

        // CRITICAL FIX: If PayrollService PAYE doesn't include km reimbursement taxable amount, use manual calculation (using consolidated variables)

        // If km reimbursement exists but PayrollService PAYE is the base amount, force use of manual calculation
        const currentPayrollServicePAYE = currentPeriodCalculations.deductions?.statutory?.paye;
        const shouldUseManualPAYE = kmReimbursementTaxableAmount > 0 && currentPayrollServicePAYE && Math.abs(currentPayrollServicePAYE - 3483.08) < 0.1;

        // SURGICAL FIX: Use manual calculation when km reimbursement exists (same as single payslip)
        const paye = Number(
          (kmReimbursementTaxableAmount > 0 ? manualPAYE : null) ||
          currentPayrollServicePAYE ||
          deductionsResultPAYE ||
          manualPAYE ||
          payslip.paye ||
          0
        );

        const uif = Number(
          mobileComprehensiveUIF ||
          currentPeriodCalculations.deductions?.statutory?.uif ||
          deductionsResultUIF ||
          manualUIF ||
          payslip.uif ||
          0
        );

        // Calculate medical aid employee deduction using same logic as Calculator Card (mobile)
        const medicalAidTotalMobile = payrollForMobile?.medical?.medicalAid || 0;
        const medicalAidEmployeeDeductionMobile = payrollForMobile?.medical?.employeeHandlesPayment
          ? 0  // Employee pays - don't show in deductions
          : (medicalAidTotalMobile
              ? (medicalAidTotalMobile - medicalAidEmployerContributionMobile)
              : 0);

        // Use medicalAidEmployeeDeductionMobile for consistency with Calculator Card
        const medicalAidAmountMobile = medicalAidEmployeeDeductionMobile;

        // PENSION FUND CALCULATIONS FOR MOBILE - Use same logic as desktop payslip
        let pensionFundEmployeeContributionMobile = 0;
        let pensionFundEmployerContributionMobile = 0;

        if (payrollForMobile?.pensionFund) {
          if (payrollForMobile.pensionFund.contributionCalculation === 'fixedAmount') {
            pensionFundEmployeeContributionMobile = Number(payrollForMobile.pensionFund.fixedContributionEmployee || 0);
            pensionFundEmployerContributionMobile = Number(payrollForMobile.pensionFund.fixedContributionEmployer || 0);
          } else if (payrollForMobile.pensionFund.contributionCalculation === 'percentageRFI' && payslip.employee.rfiConfig?.lastCalculation?.amount) {
            // Calculate based on RFI percentage
            const rfiAmount = Number(payslip.employee.rfiConfig.lastCalculation.amount || 0);
            const rfiEmployeePercentage = Number(payrollForMobile.pensionFund.rfiEmployee || 0);
            const rfiEmployerPercentage = Number(payrollForMobile.pensionFund.rfiEmployer || 0);
            pensionFundEmployeeContributionMobile = (rfiAmount * rfiEmployeePercentage) / 100;
            pensionFundEmployerContributionMobile = (rfiAmount * rfiEmployerPercentage) / 100;
          }
        }

        // CRITICAL FIX: Recalculate totalDeductions and netPay with net PAYE (after medical aid tax credits) and UIF values (bulk download)
        const updatedTotalDeductions = parseFloat(payeForDisplayMobile) + parseFloat(uif) + (payslip.maintenanceOrder || 0) + parseFloat(medicalAidAmountMobile) + parseFloat(pensionFundEmployeeContributionMobile || 0);
        // Include medical aid paid-out allowance in net pay calculation (mobile)
        const updatedNetPay = safeTotalIncome + medicalAidPaidOutAllowanceMobile - updatedTotalDeductions;

        // Extract basic salary value properly (handle object structure)
        const basicSalaryValue = typeof basicSalary === 'object'
          ? (basicSalary.full || basicSalary.prorated || 0)
          : (basicSalary || 0);

        // Extract Loss of Income data
        const lossOfIncomeAmount = (payroll?.lossOfIncomeEnabled && payroll?.lossOfIncome) ? payroll.lossOfIncome : 0;

        // Extract Travel Allowance data for SDL calculation
        const travelAllowanceAmount = payroll?.travelAllowance?.fixedAllowanceAmount || 0;
        const travelAllowanceTaxableRate = payroll?.travelAllowance?.only20PercentTax ? 0.2 : 0.8;
        const travelAllowanceTaxableAmount = travelAllowanceAmount * travelAllowanceTaxableRate;

        // Extract Travel Expenses data from calculations
        const travelExpensesAmount = travelExpenses?.total || 0;
        const travelExpensesTaxableAmount = travelExpenses?.taxableAmount || 0;
        const travelExpensesNonTaxableAmount = travelExpenses?.nonTaxableAmount || 0;

        // Calculate km reimbursement using SARS-compliant method (using consolidated variables already calculated above)

        // Calculate combined travel allowance and expenses (excluding km reimbursement for separate display)
        const combinedTravelAmount = travelAllowanceAmount + travelExpensesAmount;
        const combinedTravelTaxableAmount = travelAllowanceTaxableAmount + travelExpensesTaxableAmount;
        const combinedTravelNonTaxableAmount = (travelAllowanceAmount - travelAllowanceTaxableAmount) + travelExpensesNonTaxableAmount;

        // Calculate medical aid amounts using same logic as Calculator Card (mobile)
        const medicalAidEmployerContributionMobile = payrollForMobile?.medical?.employerContribution || 0;

        // When employee handles payment: employer contribution becomes paid-out allowance
        // When company handles payment: employer contribution is taxable benefit
        const medicalAidPaidOutAllowanceMobile = (payrollForMobile?.medical?.employeeHandlesPayment && medicalAidEmployerContributionMobile > 0)
          ? medicalAidEmployerContributionMobile
          : 0;

        const medicalAidTaxableBenefitMobile = (!payrollForMobile?.medical?.employeeHandlesPayment && medicalAidEmployerContributionMobile > 0)
          ? medicalAidEmployerContributionMobile
          : 0;

        // Calculate income using same logic as Calculator Card (mobile) with comprehensive custom income
        const baseIncomeAmountMobile = mobileComprehensiveGrossIncome || totalIncome || basicSalaryValue || 0;
        const safeTotalIncome = baseIncomeAmountMobile; // For display purposes

        // Calculate total income including allowances (same as employeeManagement.js) - mobile
        const totalIncomeWithAllowancesMobile = baseIncomeAmountMobile + medicalAidPaidOutAllowanceMobile;

        // Calculate taxable income for PAYE calculation (mobile) - FIXED: use same logic as employeeManagement.js
        // SURGICAL FIX: Only subtract employee pension fund contribution from taxable income (not employer contribution) - Mobile
        // employeeProfile.ejs subtracts only employee contribution, not total retirement deduction
        const baseTaxableIncomeForPAYEMobile = payrollForMobile?.medical?.employeeHandlesPayment
          ? totalIncomeWithAllowancesMobile  // Employee pays: use total income (includes paid-out allowance) - FIXED!
          : safeTotalIncome + medicalAidTaxableBenefitMobile;  // Company pays: add taxable benefit

        const taxableIncomeForPAYEMobile = baseTaxableIncomeForPAYEMobile - pensionFundEmployeeContributionMobile;

        // Calculate total income for display (excluding combined travel allowance, expenses, km reimbursement, and accommodation benefit for employee clarity)
        const accommodationBenefitAmount = payroll?.accommodationBenefit || 0;
        const totalIncomeForDisplay = safeTotalIncome - combinedTravelAmount - kmReimbursementTotal - accommodationBenefitAmount;

        // Calculate SDL base (exclude Loss of Income and travel amounts, but include only taxable portions)
        // SDL should be calculated on employment income + travel taxable amounts + km reimbursement taxable amount only, not insurance payouts or non-taxable allowances
        const sdlBase = safeTotalIncome - lossOfIncomeAmount - combinedTravelAmount - kmReimbursementTotal + combinedTravelTaxableAmount + kmReimbursementTaxableAmount;
        const sdl = Math.max(0, sdlBase) * 0.01;

        // Get payroll data for medical aid calculations (same as desktop version)
        const payrollForMobile = await Payroll.findOne({
          employee: payslip.employee._id,
          month: payslip.endDate || payslip.startDate
        });

        // Calculate medical aid credit using safe total income (use payroll data like employeeManagement.js)
        const medicalAidImpact = await PayrollService.calculateMedicalAidTaxImpact(
          payrollForMobile?.medical || {},
          payslip.employee.payFrequency?.frequency || 'monthly',
          safeTotalIncome
        );

        // Calculate medical aid tax credits using PayrollService method (conditional based on checkbox)
        const applyTaxCreditsMobile = !payrollForMobile?.medical?.dontApplyTaxCredits;
        const medicalAidTaxCredit = applyTaxCreditsMobile
          ? await PayrollService.calculateMedicalAidTaxCredit(
              payrollForMobile || {},
              payslip.employee.payFrequency?.frequency || "monthly",
              payslip.month
            )
          : 0;

        const medicalAidCredit = medicalAidTaxCredit || medicalAidImpact?.taxCredit || 0;

        // Use YTD PAYE calculation for mobile payslip (same as employeeManagement.js)
        const ytdPAYEResultMobile = await calculateYTDPAYEForPayslip(
          payslip.employee._id,
          payrollPeriod.endDate,
          payslip.employee.payFrequency?.frequency || "monthly",
          payslip.employee.age || 35
        );

        // CRITICAL FIX: For finalized periods, use stored PAYE; for unfinalized, use YTD calculation (mobile)
        const isCurrentPeriodFinalizedMobile = payrollPeriod?.isFinalized;
        const storedPAYEMobile = payrollPeriod?.PAYE;

        console.log('=== MOBILE PAYSLIP FINALIZED PERIOD CHECK ===');
        console.log('Is current period finalized (mobile):', isCurrentPeriodFinalizedMobile);
        console.log('Stored PAYE in database (mobile):', storedPAYEMobile);
        console.log('YTD calculated PAYE (mobile):', ytdPAYEResultMobile.currentPAYE);

        let finalPAYEMobile = (isCurrentPeriodFinalizedMobile && storedPAYEMobile > 0)
          ? storedPAYEMobile  // Use historical PAYE for finalized periods
          : ytdPAYEResultMobile.currentPAYE;  // Use YTD calculation for unfinalized periods

        // Fallback: If YTD calculation returns 0, use manual calculation
        if (finalPAYEMobile === 0 && manualPAYE > 0) {
          console.log('⚠️ YTD PAYE returned 0 for mobile, using manual calculation fallback');
          finalPAYEMobile = manualPAYE;
        }

        // Apply pro-rata to YTD PAYE result for mobile (same as main payslip)
        const proratedPAYEMobile = (isCurrentPeriodFinalizedMobile && storedPAYEMobile > 0)
          ? finalPAYEMobile  // Stored PAYE is already prorated
          : (finalPAYEMobile * proRataDetails.proratedPercentage) / 100;  // Apply proration to YTD result

        const grossPAYEMobile = Number(proratedPAYEMobile || payslip.paye || 0);

        console.log('Final PAYE used for mobile payslip:', grossPAYEMobile);
        console.log('Prorated PAYE mobile:', proratedPAYEMobile);
        console.log('Source (mobile):', (isCurrentPeriodFinalizedMobile && storedPAYEMobile > 0) ? 'Stored (finalized)' : 'YTD calculation (prorated)');
        const netPAYEMobile = Math.max(0, grossPAYEMobile - medicalAidTaxCredit);
        // Use gross PAYE when tax credits are disabled, net PAYE when enabled (mobile)
        const payeForDisplayMobile = applyTaxCreditsMobile ? netPAYEMobile : grossPAYEMobile;

        // FIXED: Format dates consistently using BusinessDate fields to prevent timezone issues
        // ISSUE RESOLVED: Period end date now shows correct last day of month
        const formattedStartDate = payslip.startDateBusiness ?
          moment(payslip.startDateBusiness).format("DD/MM/YYYY") :
          moment(payslip.startDate || payRun.startDate || payRun.payPeriodStart || new Date()).format("DD/MM/YYYY");

        const formattedEndDate = payslip.endDateBusiness ?
          moment(payslip.endDateBusiness).format("DD/MM/YYYY") :
          moment(payslip.endDate || payRun.endDate || payRun.payPeriodEnd || new Date()).format("DD/MM/YYYY");

        const periodDisplay = `${formattedStartDate} - ${formattedEndDate}`;
        
        // Create PDF document
        const pdfDoc = await PDFDocument.create();
        const page = pdfDoc.addPage([595.28, 841.89]); // A4 size
        const { width, height } = page.getSize();

        // Load fonts
        const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
        const helveticaBoldFont = await pdfDoc.embedFont(
          StandardFonts.HelveticaBold
        );

        // Define helper functions for this payslip
        const drawText = (text, x, y, options = {}) => {
          const {
            font = helveticaFont,
            size = 10,
            color = rgb(0, 0, 0),
            align = "left",
          } = options;

          const textWidth = font.widthOfTextAtSize(text, size);
          let xPos = x;

          if (align === "right") {
            xPos = x - textWidth;
          } else if (align === "center") {
            xPos = x - textWidth / 2;
          }

          page.drawText(text || "", {
            x: xPos,
            y,
            size,
            font,
            color,
          });
        };

        // FIXED: Helper function with dynamic column positioning to prevent overlap
        const drawTextWithSpace = (label, amount, x, y, options = {}) => {
          drawText(label, x, y, options);

          // DYNAMIC COLUMN POSITIONING: Use correct amount column based on label position
          let amountColumnX;
          if (x <= 250) { // tableLayout.col2 equivalent
            // Left side labels use left amount column
            amountColumnX = 250; // Left amount column
          } else {
            // Right side labels use right amount column
            amountColumnX = 500; // Right amount column
          }

          drawText(amount, amountColumnX, y, { ...options, align: "right" });
        };

        // Helper function to format currency safely
        const formatCurrency = (amount) => {
          if (amount === undefined || amount === null) {
            return "R0.00";
          }
          return new Intl.NumberFormat("en-ZA", {
            style: "currency",
            currency: "ZAR",
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }).format(amount);
        };

        // Company details
        const companyName = payslip.employee.company?.name || "Company Name";
        const companyAddress = payslip.employee.company?.address || "Company Address";
        const companyRegistration = payslip.employee.company?.registrationNumber || "";
        const companyLogo = payslip.employee.company?.logo;
        

        // Employee details
        const employeeName = `${payslip.employee.firstName || ""} ${payslip.employee.lastName || ""}`.trim() || "Employee Name";
        const employeeId = payslip.employee.employeeId || "Employee ID";
        const employeeDoa = payslip.employee.doa
          ? moment(payslip.employee.doa).format("DD/MM/YYYY")
          : "N/A";
          

        // Set up the document structure
        const margin = 40;
        const tableCol1 = margin;
        const tableCol2 = width - margin;
        const headerHeight = height - margin;

        // Add company logo if it exists
        if (companyLogo) {
          try {
            const logoPath = path.join(__dirname, "..", companyLogo);
            const logoFile = await fs.readFile(logoPath);
            const logoImage = await pdfDoc.embedPng(logoFile);
            const logoWidth = 80; // Reduced from 100 for better proportions
            const logoHeight = (logoWidth / logoImage.width) * logoImage.height;
            page.drawImage(logoImage, {
              x: margin,
              y: height - 20 - logoHeight, // Raised higher on the page
              width: logoWidth,
              height: logoHeight,
            });
          } catch (error) {
            console.error("Error loading logo:", error);
          }
        }

        // Company name (centered, 8pt)
        drawText(
          companyName,
          width / 2,
          height - 50,
          { font: helveticaBoldFont, size: 8, align: "center" }
        );

        // Company address (right-aligned)
        const addressY = height - 100;
        const cityTown = payslip.employee.company?.physicalAddress?.cityTown || "City";
        const postalCode = payslip.employee.company?.physicalAddress?.code || "Postal Code";
        
        drawText(
          cityTown,
          width - 50,
          addressY,
          { align: "right" }
        );
        drawText(
          postalCode,
          width - 50,
          addressY - 15,
          { align: "right" }
        );

        // Update the period display based on frequency
        drawText(`Period: ${periodDisplay}`, 50, height - 145, {
          font: helveticaBoldFont,
          size: 8,
        });

        // Update employee details section with period-specific information
        const startY = height - 100;
        drawText(
          `Employee Name: ${employeeName}`,
          50,
          startY
        );
        drawText(
          `Employee ID: ${employeeId}`,
          50,
          startY - 15
        );
        drawText(
          `Date of Appointment: ${employeeDoa}`,
          50,
          startY - 30
        );

        // Horizontal line under employee details
        const lineY = startY - 50;
        page.drawLine({
          start: { x: 50, y: lineY },
          end: { x: width - 50, y: lineY },
          thickness: 1,
          color: rgb(0, 0, 0),
        });

        // Create a table for income, deductions, and employer contributions
        const tableStartY = lineY - 20;
        const lineHeight = 20;
        const tableCol3 = 350;
        const tableCol4 = 450;

        // Headers (all bold headers now 8pt)
        drawText("Income", tableCol1, tableStartY, {
          font: helveticaBoldFont,
          size: 8,
        });
        drawText("Employer Contribution", tableCol3, tableStartY, {
          font: helveticaBoldFont,
          size: 8,
        });

        // Income - Using corrected values
        let currentIncomeLineY = tableStartY - lineHeight;

        // Basic Salary
        drawTextWithSpace(
          "Basic Salary",
          basicSalaryValue,
          tableCol1,
          currentIncomeLineY
        );
        currentIncomeLineY -= lineHeight;

        // Custom Income Items (dynamic) - Mobile
        if (mobileCustomIncomeItems && mobileCustomIncomeItems.length > 0) {
          mobileCustomIncomeItems.forEach(item => {
            const itemAmount = item.calculatedAmount || item.amount || item.monthlyAmount || 0;
            if (itemAmount > 0) {
              drawTextWithSpace(
                item.name,
                itemAmount,
                tableCol1,
                currentIncomeLineY
              );
              currentIncomeLineY -= lineHeight;
            }
          });
        }

        // Commission (if applicable)
        const commissionAmount = payroll?.commission || 0;
        if (commissionAmount > 0) {
          drawTextWithSpace(
            "Commission",
            commissionAmount,
            tableCol1,
            currentIncomeLineY
          );
          currentIncomeLineY -= lineHeight;
        }

        // Total Income
        drawText(
          formatCurrency(totalIncomeForDisplay),
          tableCol2 - 8,
          currentIncomeLineY,
          { font: helveticaBoldFont, size: 8, align: "right" }
        );

        // Deductions - Use dynamic positioning
        currentIncomeLineY -= lineHeight; // Add space between income and deductions

        drawText("Deduction", tableCol1, currentIncomeLineY, {
          font: helveticaBoldFont,
          size: 8,
        });
        currentIncomeLineY -= lineHeight;

        drawText("Medical Aid", tableCol1, currentIncomeLineY);
        drawText(
          formatCurrency(payslip.medical?.medicalAid || 0),
          tableCol2 - 8,
          currentIncomeLineY,
          { align: "right" }
        );
        currentIncomeLineY -= lineHeight;

        drawText("UIF", tableCol1, currentIncomeLineY);
        drawTextWithSpace("UIF", uif, tableCol1, currentIncomeLineY);
        currentIncomeLineY -= lineHeight;

        drawText("PAYE", tableCol1, currentIncomeLineY);
        drawTextWithSpace("PAYE", payeForDisplayMobile, tableCol1, currentIncomeLineY);
        currentIncomeLineY -= lineHeight;

        // Total Deductions - FIXED: Use updated total deductions
        drawText("Total Deductions:", tableCol1, currentIncomeLineY, {
          font: helveticaBoldFont,
          size: 8,
        });
        drawTextWithSpace(
          "Total Deductions:",
          updatedTotalDeductions,
          tableCol1,
          currentIncomeLineY,
          { font: helveticaBoldFont, size: 8 }
        );

        // Employer Contributions
        drawText("Employer Contribution", tableCol3, tableStartY, {
          font: helveticaBoldFont,
          size: 8,
        });
        drawTextWithSpace("SDL", sdl, tableCol3, tableStartY - lineHeight);
        drawTextWithSpace(
          "UIF - Employer",
          uif,
          tableCol3,
          tableStartY - 2 * lineHeight
        );

        // Tax Credit
        // Tax Credit section (only show if tax credits are enabled and there are credits)
        if (applyTaxCreditsMobile && medicalAidCredit > 0) {
          drawText("Tax Credit", tableCol3, tableStartY - 4 * lineHeight, {
            font: helveticaBoldFont,
            size: 8,
          });
          drawTextWithSpace(
            "Medical Aid Tax Credit",
            medicalAidCredit,
            tableCol3,
            tableStartY - 5 * lineHeight
          );
        }

        // NETT PAY (moved to bottom of the page)
        const nettPayY = 50; // Adjust this value to position it at the bottom

        page.drawLine({
          start: { x: 50, y: nettPayY + 25 },
          end: { x: 545, y: nettPayY + 25 },
          thickness: 1,
          color: rgb(0, 0, 0),
        });

        drawText("NETT PAY", 50, nettPayY, { font: helveticaBoldFont, size: 8 });
        drawTextWithSpace("NETT PAY", updatedNetPay, 50, nettPayY, {
          font: helveticaBoldFont,
          size: 8,
        });

        page.drawLine({
          start: { x: 50, y: nettPayY - 5 },
          end: { x: 545, y: nettPayY - 5 },
          thickness: 1,
          color: rgb(0, 0, 0),
        });

        // Add finalization status to the footer
        const statusText = payslip.isFinalized
          ? "FINALIZED PAYSLIP"
          : "DRAFT PAYSLIP - NOT FOR OFFICIAL USE";

        drawText(statusText, width / 2, 30, {
          font: helveticaBoldFont,
          size: 8,
          color: payslip.isFinalized ? rgb(0, 0, 0) : rgb(0.9, 0.3, 0.3),
          align: "center",
        });

        // Draw the payslip period box
        const periodY = height - 150;
        page.drawRectangle({
          x: 50,
          y: periodY - 25,
          width: width - 100,
          height: 40,
          borderColor: rgb(0.7, 0.7, 0.7),
          borderWidth: 1,
        });

        drawText(
          "PAYSLIP",
          width / 2,
          periodY,
          { font: helveticaBoldFont, size: 14, align: "center" }
        );

        drawText(
          `Period: ${periodDisplay}`,
          width / 2,
          periodY - 15,
          { size: 10, align: "center" }
        );

        // Draw earnings section
        const earningsY = periodY - 70;
        drawText(
          "EARNINGS",
          50,
          earningsY,
          { font: helveticaBoldFont, size: 12 }
        );

        // Draw line under earnings title
        page.drawLine({
          start: { x: 50, y: earningsY - 5 },
          end: { x: width - 50, y: earningsY - 5 },
          thickness: 1,
          color: rgb(0.7, 0.7, 0.7),
        });

        // Draw earnings items
        let currentY = earningsY - 25;

        // Basic salary - Using corrected values
        const basicSalaryForDisplay = basicSalaryValue || payslip.basicSalary || payslip.grossPay || safeTotalIncome || 0;
        drawTextWithSpace(
          "Basic Salary",
          basicSalaryForDisplay,
          50,
          currentY
        );
        currentY -= 15;

        // Custom Income Items (dynamic) - PDF
        if (customIncomeItems && customIncomeItems.length > 0) {
          customIncomeItems.forEach(item => {
            const itemAmount = item.calculatedAmount || item.amount || item.monthlyAmount || 0;
            if (itemAmount > 0) {
              drawTextWithSpace(
                item.name,
                itemAmount,
                50,
                currentY
              );
              currentY -= 15;
            }
          });
        }

        // Loss of Income (if applicable)
        if (lossOfIncomeAmount > 0) {
          drawTextWithSpace(
            "Loss of Income",
            lossOfIncomeAmount,
            50,
            currentY
          );
          currentY -= 15;
        }

        // Add allowances if they exist
        if (payslip.allowances && payslip.allowances.length > 0) {
          for (const allowance of payslip.allowances) {
            if (allowance.amount > 0) {
              drawTextWithSpace(
                allowance.name || "Allowance",
                allowance.amount,
                50,
                currentY
              );
              currentY -= 15;
            }
          }
        }

        // Add km reimbursement if it exists
        if (kmReimbursementTotal > 0) {
          // Travel Allowance Reimbursive (SARS allowable portion)
          drawTextWithSpace(
            "Travel Allowance Reimbursive",
            formatCurrency(kmReimbursementNonTaxableAmount),
            50,
            currentY
          );
          currentY -= 15;

          // Travel Allowance Reimbursive - taxable (excess over SARS rate)
          if (kmReimbursementTaxableAmount > 0) {
            drawTextWithSpace(
              "Travel Allowance Reimbursive - taxable",
              formatCurrency(kmReimbursementTaxableAmount),
              50,
              currentY
            );
            currentY -= 15;
          }
        }

        // Add overtime if it exists
        if (payslip.overtime && payslip.overtime.length > 0) {
          for (const ot of payslip.overtime) {
            if (ot.amount > 0) {
              drawTextWithSpace(
                `Overtime (${ot.hours || 0} hours)`,
                ot.amount,
                50,
                currentY
              );
              currentY -= 15;
            }
          }
        }

        // Add total earnings
        currentY -= 10;
        page.drawLine({
          start: { x: 50, y: currentY + 5 },
          end: { x: width - 50, y: currentY + 5 },
          thickness: 1,
          color: rgb(0.7, 0.7, 0.7),
        });

        drawTextWithSpace(
          "Total Earnings",
          formatCurrency(totalIncomeForDisplay),
          50,
          currentY,
          { font: helveticaBoldFont }
        );

        // Draw deductions section (adjust spacing for km reimbursement)
        const kmReimbursementSpacing = kmReimbursementTotal > 0 ? (kmReimbursementTaxableAmount > 0 ? 45 : 30) : 0;
        const deductionsY = currentY - 40 - kmReimbursementSpacing;
        drawText(
          "DEDUCTIONS",
          50,
          deductionsY,
          { font: helveticaBoldFont, size: 12 }
        );

        // Draw line under deductions title
        page.drawLine({
          start: { x: 50, y: deductionsY - 5 },
          end: { x: width - 50, y: deductionsY - 5 },
          thickness: 1,
          color: rgb(0.7, 0.7, 0.7),
        });

        // Draw deductions items
        currentY = deductionsY - 25;



        // PAYE - Use net PAYE (after medical aid tax credits)
        drawTextWithSpace("PAYE", payeForDisplayMobile, 50, currentY);
        currentY -= 15;

        // UIF - FIXED: Remove double formatCurrency() call
        drawTextWithSpace("UIF", uif, 50, currentY);
        currentY -= 15;

        // SDL
        drawTextWithSpace("SDL", formatCurrency(sdl), 50, currentY);
        currentY -= 15;

        // Add medical aid if it exists
        if (payslip.medical && payslip.medical.amount > 0) {
          drawTextWithSpace(
            "Medical Aid",
            formatCurrency(payslip.medical.amount || 0),
            50,
            currentY
          );
          currentY -= 15;
        }

        // Medical aid tax credit (only show if tax credits are enabled and amount > 0)
        if (applyTaxCreditsMobile && medicalAidCredit > 0) {
          drawTextWithSpace(
            "Medical Aid Tax Credit",
            formatCurrency(-medicalAidCredit),
            50,
            currentY
          );
          currentY -= 15;
        }

        // Add deductions if they exist
        if (payslip.deductions && payslip.deductions.length > 0) {
          for (const deduction of payslip.deductions) {
            if (deduction.amount > 0) {
              drawTextWithSpace(
                deduction.name || "Deduction",
                formatCurrency(deduction.amount),
                50,
                currentY
              );
              currentY -= 15;
            }
          }
        }

        // Add maintenance order if it exists
        if (maintenanceOrder > 0) {
          drawTextWithSpace(
            "Maintenance Order",
            formatCurrency(maintenanceOrder),
            50,
            currentY
          );
          currentY -= 15;
        }

        // Add total deductions
        currentY -= 10;
        page.drawLine({
          start: { x: 50, y: currentY + 5 },
          end: { x: width - 50, y: currentY + 5 },
          thickness: 1,
          color: rgb(0.7, 0.7, 0.7),
        });

        drawTextWithSpace(
          "Total Deductions",
          updatedTotalDeductions,
          50,
          currentY,
          { font: helveticaBoldFont }
        );

        // ALLOWANCES SECTION (Mobile)
        const allowancesYMobile = currentY - 40;

        // Only show Allowances section if there are allowances to display
        if (medicalAidPaidOutAllowanceMobile > 0) {
          drawText(
            "ALLOWANCES",
            50,
            allowancesYMobile,
            { font: helveticaBoldFont }
          );

          let allowanceItemYMobile = allowancesYMobile - 20;

          // Medical Aid Paid Out Allowance
          if (medicalAidPaidOutAllowanceMobile > 0) {
            drawTextWithSpace(
              "Medical Aid Paid Out Allowance",
              medicalAidPaidOutAllowanceMobile,
              50,
              allowanceItemYMobile,
              { font: helveticaFont, size: 8 }
            );
            allowanceItemYMobile -= 15;
          }
        }

        // BENEFITS SECTION
        const benefitsY = medicalAidPaidOutAllowanceMobile > 0
          ? allowancesYMobile - 60
          : currentY - 40;

        // Calculate petrol card taxable amount
        const petrolCardSpendAmount = payrollPeriod?.data?.get('travelExpenses')?.petrolCardSpend || 0;
        const petrolCardTaxableRate = payroll?.travelAllowance?.only20PercentTax ? 0.2 : 0.8;
        const petrolCardTaxableAmount = petrolCardSpendAmount * petrolCardTaxableRate;

        // Only show Benefits section if there are benefits to display
        if (petrolCardTaxableAmount > 0 || (payroll?.accommodationBenefit && payroll.accommodationBenefit > 0)) {
          drawText(
            "BENEFITS",
            50,
            benefitsY,
            { font: helveticaBoldFont, size: 12 }
          );

          // Draw line under benefits title
          page.drawLine({
            start: { x: 50, y: benefitsY - 5 },
            end: { x: width - 50, y: benefitsY - 5 },
            thickness: 1,
            color: rgb(0.7, 0.7, 0.7),
          });

          let currentBenefitY = benefitsY - 25;

          // Accommodation Benefit (if applicable)
          if (payroll?.accommodationBenefit && payroll.accommodationBenefit > 0) {
            drawTextWithSpace(
              "Accommodation Benefit",
              formatCurrency(payroll.accommodationBenefit),
              50,
              currentBenefitY
            );
            currentBenefitY -= 15;
          }

          // Petrol Card - Taxable Portion (if applicable)
          if (petrolCardTaxableAmount > 0) {
            drawTextWithSpace(
              "Petrol Card - Taxable Portion",
              formatCurrency(petrolCardTaxableAmount),
              50,
              currentBenefitY
            );
          }
        }

        // Draw net pay section
        const netPayY = benefitsY - 80;
        drawText(
          "NET PAY",
          50,
          netPayY,
          { font: helveticaBoldFont, size: 12 }
        );

        // Draw line under net pay title
        page.drawLine({
          start: { x: 50, y: netPayY - 5 },
          end: { x: width - 50, y: netPayY - 5 },
          thickness: 1,
          color: rgb(0.7, 0.7, 0.7),
        });

        // FIXED: Draw net pay amount in right column for visual consistency
        // ISSUE RESOLVED: Net Pay amount now aligns with right column amounts
        drawText("Net Pay", 50, netPayY - 25, {
          font: helveticaBoldFont,
          size: 12
        });

        // Position Net Pay amount in right column (X=500px) - FIXED: Use updated net pay
        drawText(formatCurrency(updatedNetPay), 500, netPayY - 25, {
          font: helveticaBoldFont,
          size: 12,
          align: "right"
        });

        // Add bank details if they exist
        const bankDetailsY = netPayY - 70;
        if (payslip.employee.bankDetails) {
          drawText(
            "BANK DETAILS",
            50,
            bankDetailsY,
            { font: helveticaBoldFont, size: 12 }
          );

          // Draw line under bank details title
          page.drawLine({
            start: { x: 50, y: bankDetailsY - 5 },
            end: { x: width - 50, y: bankDetailsY - 5 },
            thickness: 1,
            color: rgb(0.7, 0.7, 0.7),
          });

          currentY = bankDetailsY - 25;
          const bankDetails = payslip.employee.bankDetails;

          drawText(`Bank: ${bankDetails.bankName || "Not specified"}`, 50, currentY);
          currentY -= 15;

          drawText(`Account Number: ${bankDetails.accountNumber || "Not specified"}`, 50, currentY);
          currentY -= 15;

          drawText(`Account Type: ${bankDetails.accountType || "Not specified"}`, 50, currentY);
          currentY -= 15;

          drawText(`Branch Code: ${bankDetails.branchCode || "Not specified"}`, 50, currentY);
        }

        // Add footer with company registration
        const footerY = 50;
        if (companyRegistration) {
          drawText(
            `Company Registration: ${companyRegistration}`,
            width / 2,
            footerY,
            { size: 8, align: "center" }
          );
        }

        // Add DRAFT watermark if period is not finalized
        if (!payslip.isFinalized) {
          // Draw diagonal DRAFT text
          const draftText = "DRAFT";
          const fontSize = 60;
          const draftWidth = helveticaBoldFont.widthOfTextAtSize(
            draftText,
            fontSize
          );

          page.drawText(draftText, {
            x: (width - draftWidth) / 2,
            y: height / 2,
            font: helveticaBoldFont,
            size: fontSize,
            color: rgb(0.9, 0.3, 0.3), // Light red color
            opacity: 0.3, // Semi-transparent
            rotate: {
              type: "degrees",
              angle: 45,
              origin: {
                x: width / 2,
                y: height / 2,
              },
            },
          });

          // Add draft notice at the top
          const noticeText = "This is a draft payslip - Not finalized";
          page.drawText(noticeText, {
            x: 50,
            y: height - 30,
            font: helveticaBoldFont,
            size: 10,
            color: rgb(0.9, 0.3, 0.3),
          });
        }

        // Serialize the PDF
        const pdfBytes = await pdfDoc.save();
        

        // Add the PDF to the archive
        const filename = `Payslip_${employeeName.replace(/\s+/g, "_")}_${formattedEndDate.replace(/\//g, "-")}.pdf`;
        archive.append(Buffer.from(pdfBytes), { name: filename });
        successCount++;
      } catch (error) {
        console.error(`Error generating payslip PDF: ${error.message}`, error);
        console.error(`Failed payslip details:`, {
          employeeId: payslip.employee?._id,
          employeeName: payslip.employee ? `${payslip.employee.firstName || ''} ${payslip.employee.lastName || ''}` : 'Unknown',
          payslipId: payslip._id
        });
        failureCount++;
      }
    }
    // Finalize the archive
    await archive.finalize();
    
  } catch (error) {
    console.error("Error generating bulk payslips:", error);
    
    // Only send error response if headers haven't been sent yet
    if (!res.headersSent) {
      res.status(500).send(`Error generating payslips: ${error.message}`);
    } else {
      // If headers were already sent, we can't send a proper error response
      // Just end the response to prevent hanging connections
      res.end();
    }
  }
});

// Helper function to try the Payroll model as a fallback
async function tryPayrollFallback(payRun, res) {
  try {
    const Payroll = mongoose.models.Payroll || mongoose.model("Payroll");
    const payrolls = await Payroll.find({
      company: payRun.company,
      payPeriods: { 
        $elemMatch: {
          startDate: { $lte: payRun.endDate },
          endDate: { $gte: payRun.startDate }
        }
      }
    }).populate({
      path: "employee",
      select: "firstName lastName employeeId company payFrequency doa bankDetails",
      populate: [
        {
          path: "company",
          populate: {
            path: "employerDetails",
          },
        },
        "payFrequency",
      ],
    });
    
    
    if (payrolls && payrolls.length > 0) {
      // Filter out payrolls without employee data
      const validPayrolls = payrolls.filter(payroll => payroll.employee);
      
      if (validPayrolls.length > 0) {
        // Convert payrolls to a format compatible with payslips
        const payrollsAsPayslips = validPayrolls.map(payroll => {
          // Find the relevant pay period
          const relevantPeriod = payroll.payPeriods.find(period => 
            period.startDate <= payRun.endDate && period.endDate >= payRun.startDate
          );
          
          if (!relevantPeriod) return null;
          
          return {
            _id: payroll._id,
            employee: payroll.employee,
            startDate: relevantPeriod.startDate,
            endDate: relevantPeriod.endDate,
            basicSalary: relevantPeriod.basicSalary || payroll.basicSalary || 0,
            grossPay: relevantPeriod.total || payroll.grossPay || 0,
            netPay: (relevantPeriod.total - (relevantPeriod.paye || 0) - (relevantPeriod.uif || 0)) || payroll.netPay || 0,
            totalDeductions: ((relevantPeriod.paye || 0) + (relevantPeriod.uif || 0)) || payroll.totalDeductions || 0,
            allowances: payroll.allowances || [],
            deductions: payroll.deductions || [],
            overtime: payroll.overtime || [],
            medical: payroll.medical || {},
            isFinalized: relevantPeriod.isFinalized || false
          };
        }).filter(p => p !== null);
        
        
        if (payrollsAsPayslips.length > 0) {
          payRun.payslips = payrollsAsPayslips;
          return { success: true, payRun };
        }
      }
    }
    
    return { success: false };
  } catch (error) {
    console.error("Error in tryPayrollFallback:", error);
    return { success: false, error };
  }
}

// Export YTD calculation function for use in other routes
module.exports = router;
module.exports.calculateYTDPAYEForPayslip = calculateYTDPAYEForPayslip;
