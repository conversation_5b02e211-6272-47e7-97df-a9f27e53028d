<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Pay Run</title>
    <script
      type="module"
      src="https://cdn.jsdelivr.net/npm/ionicons@5.5.2/dist/ionicons/ionicons.esm.js"
    ></script>
    <script
      nomodule=""
      src="https://cdn.jsdelivr.net/npm/ionicons@5.5.2/dist/ionicons/ionicons.js"
    ></script>

    <!-- JSPDF -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.4.0/jspdf.umd.min.js"></script>

    <!-- Stylesheets -->
    <link rel="stylesheet" href="/design.css" />
    <link rel="stylesheet" href="/css/employeeManagement.css" />
    <link rel="stylesheet" href="/payrollHub.css" />
    <link rel="stylesheet" href="/styles.css" />
    <link rel="stylesheet" href="/header.css" />
    <link rel="stylesheet" href="/finalize.css" />
    <link rel="stylesheet" href="/regularinputs.css" />
    <link rel="stylesheet" href="/flashMessage.css" />

    <!-- FontAwesome CDN -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css"
    />

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Nunito+Sans:opsz,wght@6..12,200&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Familjen+Grotesk&display=swap"
      rel="stylesheet"
    />

    <!-- Phosphor Icons -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>

    <style>
      .payrun-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }

      .header-left {
        display: flex;
        flex-direction: column;
        gap: 10px;
      }

      .back-button {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        color: #495057;
        text-decoration: none;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.2s ease;
        width: fit-content;
      }

      .back-button:hover {
        background-color: #e9ecef;
        border-color: #adb5bd;
        color: #212529;
        text-decoration: none;
      }

      .back-button i {
        font-size: 16px;
      }
    </style>
  </head>
  <body>
    <%- include('partials/header') %> <%- include('partials/sidebar') %>
    <main>
      <div class="payrun-container">
        <!-- Header Section with Back Button -->
        <div class="payrun-header">
          <div class="header-left">
            <a href="/<%= payRun.company %>/payrollhub" class="back-button">
              <i class="ph ph-arrow-left"></i>
              Back to Payroll Hub
            </a>
            <h2>Pay Run: <%= payRun.payslips.length %> payslips</h2>
          </div>
          <div class="total-nett-pay">
            <p>Total Net Pay: R <%= Number(totalNetPay).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></p>
          </div>
        </div>

        <!-- Table Section -->
        <div class="payrun-card">
          <table class="payrun-table">
            <thead>
              <tr>
                <th>Payslips</th>
                <th>All</th>
                <th>EFT</th>
                <th>Cash</th>
                <th>Cheque</th>
                <th>Accounting Info</th>
                <th>Beneficiaries</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><%= payRun.payslips.length %> Payslips</td>
                <td><%= payRun.payslips.length %></td>
                <td>
                  <%= payRun.payslips.filter(p => p.paymentMethod ===
                  'EFT').length %>
                </td>
                <td>
                  <%= payRun.payslips.filter(p => p.paymentMethod ===
                  'Cash').length %>
                </td>
                <td>
                  <%= payRun.payslips.filter(p => p.paymentMethod ===
                  'Cheque').length %>
                </td>
                <td><i class="icon-pdf"></i> <i class="icon-excel"></i></td>
                <td><i class="icon-pdf"></i> <i class="icon-excel"></i></td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Payslips in this Pay Run Table -->
        <div class="payrun-card" style="margin-top: 20px;">
          <h3>Payslips in this Pay Run</h3>
          <table class="payrun-table">
            <thead>
              <tr>
                <th>Employee</th>
                <th>Basic Salary</th>
                <th>Custom Income</th>
                <th>Gross Pay</th>
                <th>PAYE</th>
                <th>UIF</th>
                <th>Total Deductions</th>
                <th>Net Pay</th>
              </tr>
            </thead>
            <tbody>
              <% if (payRun.payrollPeriods && payRun.payrollPeriods.length > 0) { %>
                <% payRun.payrollPeriods.forEach(function(period) { %>
                  <%
                    // 🔧 DYNAMIC CUSTOM INCOME CALCULATION (like employeeProfile.ejs)
                    const periodBasicSalary = period.basicSalary || 0;

                    // Calculate custom income dynamically from customIncomeItems
                    let periodCustomIncomeForUIF = 0;
                    if (period.customIncomeItems && Array.isArray(period.customIncomeItems)) {
                      periodCustomIncomeForUIF = period.customIncomeItems.reduce((total, item) => {
                        let amount = 0;
                        if (item.calculatedAmount && item.calculatedAmount > 0) {
                          amount = Number(item.calculatedAmount);
                        } else if (typeof item.amount === 'number') {
                          amount = Number(item.amount);
                        } else if (typeof item.monthlyAmount === 'number') {
                          amount = Number(item.monthlyAmount);
                        }
                        return total + amount;
                      }, 0);
                    }

                    const periodComprehensiveGrossIncome = periodBasicSalary + periodCustomIncomeForUIF;
                    const periodComprehensiveUIF = Math.min(periodComprehensiveGrossIncome * 0.01, 177.12);
                    const periodComprehensiveTotalDeductions = (period.PAYE || 0) + periodComprehensiveUIF;
                    const periodComprehensiveNetPay = periodComprehensiveGrossIncome - periodComprehensiveTotalDeductions;
                  %>
                  <tr>
                    <td><%= period.employee ? `${period.employee.firstName} ${period.employee.lastName}` : 'Unknown Employee' %></td>
                    <td>R <%= Number(periodBasicSalary).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></td>
                    <td>R <%= Number(periodCustomIncomeForUIF).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></td>
                    <td>R <%= Number(periodComprehensiveGrossIncome).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></td>
                    <td>R <%= Number(period.PAYE || 0).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></td>
                    <td>R <%= Number(periodComprehensiveUIF).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></td>
                    <td>R <%= Number(periodComprehensiveTotalDeductions).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></td>
                    <td>R <%= Number(periodComprehensiveNetPay).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></td>
                  </tr>
                <% }); %>
              <% } else if (payRun.payslips && payRun.payslips.length > 0) { %>
                <% payRun.payslips.forEach(function(payslip) { %>
                  <%
                    // 🔧 DYNAMIC CUSTOM INCOME CALCULATION for payslips
                    const payslipBasicSalary = payslip.basicSalary || 0;

                    // Calculate custom income dynamically from customIncomeItems
                    let payslipCustomIncomeForUIF = 0;
                    if (payslip.customIncomeItems && Array.isArray(payslip.customIncomeItems)) {
                      payslipCustomIncomeForUIF = payslip.customIncomeItems.reduce((total, item) => {
                        let amount = 0;
                        if (item.calculatedAmount && item.calculatedAmount > 0) {
                          amount = Number(item.calculatedAmount);
                        } else if (typeof item.amount === 'number') {
                          amount = Number(item.amount);
                        } else if (typeof item.monthlyAmount === 'number') {
                          amount = Number(item.monthlyAmount);
                        }
                        return total + amount;
                      }, 0);
                    }

                    const payslipComprehensiveGrossIncome = payslipBasicSalary + payslipCustomIncomeForUIF;
                    const payslipComprehensiveUIF = Math.min(payslipComprehensiveGrossIncome * 0.01, 177.12);
                    const payslipComprehensiveTotalDeductions = (payslip.PAYE || 0) + payslipComprehensiveUIF;
                    const payslipComprehensiveNetPay = payslipComprehensiveGrossIncome - payslipComprehensiveTotalDeductions;
                  %>
                  <tr>
                    <td><%= payslip.employee ? `${payslip.employee.firstName} ${payslip.employee.lastName}` : 'Unknown Employee' %></td>
                    <td>R <%= Number(payslipBasicSalary).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></td>
                    <td>R <%= Number(payslipCustomIncomeForUIF).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></td>
                    <td>R <%= Number(payslipComprehensiveGrossIncome).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></td>
                    <td>R <%= Number(payslip.PAYE || 0).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></td>
                    <td>R <%= Number(payslipComprehensiveUIF).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></td>
                    <td>R <%= Number(payslipComprehensiveTotalDeductions).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></td>
                    <td>R <%= Number(payslipComprehensiveNetPay).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></td>
                  </tr>
                <% }); %>
              <% } else { %>
                <tr>
                  <td colspan="8" style="text-align: center; padding: 20px;">No payslips found in this pay run</td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>

        <!-- Payroll Summary Cards -->
        <div class="payroll-summary">
          <h3>Payroll Summary</h3>
          <div class="card-container">
            <div class="card">
              <h4>Total Gross Pay</h4>
              <p>R <%= Number(totalGrossPay).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></p>
            </div>
            <div class="card">
              <h4>Total Net Pay</h4>
              <p>R <%= Number(totalNetPay).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></p>
            </div>
            <div class="card">
              <h4>Total UIF</h4>
              <p>R <%= Number(totalUIF).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></p>
            </div>
            <div class="card">
              <h4>Total SDL</h4>
              <p>R <%= Number(totalSDL).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></p>
            </div>
            <div class="card">
              <h4>Total PAYE</h4>
              <p>R <%= Number(totalPAYE).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></p>
            </div>
          </div>
        </div>

        <!-- Back to all pay runs link -->
        <div class="back-to-payruns">
          <a href="/payruns">Back to All Pay Runs</a>
        </div>
        <div class="action-dropdown">
          <!-- Dropdown for actions -->
          <button class="dropdown-btn">Actions</button>
          <div class="dropdown-content">
            <a href="#">View All Payslips</a>
            <a href="#">Download Payslips (PDF)</a>
            <a href="#">Download Payslips (Excel)</a>
          </div>
        </div>
      </div>
    </main>
    <!-- Include your script files here -->
    <script src="/path/to/your/js/file.js"></script>
  </body>
</html>
