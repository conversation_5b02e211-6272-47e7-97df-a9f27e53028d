<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="csrf-token" content="<%= csrfToken %>">
    <!-- CRITICAL FIX: Prevent browser caching for dynamic payroll data -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <title>Employee Profile</title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="/images/Favicon Panda Logo.png" />
    <link rel="apple-touch-icon" href="/images/Favicon Panda Logo.png" />

    <!-- Modern Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" />

    <!-- Phosphor Icons -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>

    <!-- External Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.4.0/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/date-fns/2.29.3/date-fns.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <!-- Stylesheets -->
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/employeeManagement.css" />
    <link rel="stylesheet" href="/css/employeeProfile.css" />
    <link rel="stylesheet" href="/css/employee-navigation.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />
    <link rel="stylesheet" href="/css/mobile-header.css" />
    <link rel="stylesheet" href="/css/mobile-employee-profile.css" />
    <link rel="stylesheet" href="/css/mobile-nav.css" />

    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" />

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Nunito+Sans:opsz,wght@6..12,200&display=swap" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=Familjen+Grotesk&display=swap" rel="stylesheet" />

    <!-- Phosphor Icons -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
      /* Professional Employee Profile Styles - PandaPayroll Design System */

      /* Enterprise Tag System - Conservative Business Styling */
      .tag {
        font-size: 0.6875rem;
        padding: 0.125rem 0.375rem;
        border-radius: 2px;
        font-weight: 500;
        white-space: nowrap;
        margin-left: 0.5rem;
        border: 1px solid #d1d5db;
        text-transform: uppercase;
        letter-spacing: 0.025em;
        background-color: #f9fafb;
        color: #374151;
      }

      .tag.non-taxable {
        background-color: #f9fafb;
        color: #374151;
        border-color: #d1d5db;
      }

      .tag.taxable {
        background-color: #f9fafb;
        color: #374151;
        border-color: #d1d5db;
      }

      .tag.age-tag {
        background-color: #f9fafb;
        color: #6b7280;
        border-color: #d1d5db;
        font-size: 0.625rem;
        text-transform: none;
        letter-spacing: normal;
      }

      .tag.prorated {
        background-color: #f9fafb;
        color: #374151;
        border-color: #d1d5db;
      }

      .tag.tax-deductible {
        background-color: #f9fafb;
        color: #374151;
        border-color: #d1d5db;
      }

      .tag.employer {
        background-color: #f9fafb;
        color: #374151;
        border-color: #d1d5db;
      }

      .tag.info {
        background-color: #f9fafb;
        color: #374151;
        border-color: #d1d5db;
      }

      .tag.tax-exempt {
        background-color: #f9fafb;
        color: #374151;
        border-color: #d1d5db;
      }

      .tag.tax-benefit {
        background-color: #f9fafb;
        color: #374151;
        border-color: #d1d5db;
      }

      .tag.tax-directive {
        background-color: #f9fafb;
        color: #374151;
        border-color: #d1d5db;
      }

      .tag.members {
        background-color: #f9fafb;
        color: #374151;
        border-color: #d1d5db;
      }

      /* Calculator Section Improvements */
      .calc-page {
        display: none;
      }

      .calc-page[data-page="1"] {
        display: block;
      }

      .calc-group {
        margin-bottom: 1.5rem;
        padding: 1rem;
        background: #f8fafc;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
      }

      .calc-group h3 {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
        color: #1e293b;
        font-size: 1rem;
        font-weight: 600;
      }

      .calc-group h3 i {
        color: #6366f1;
        font-size: 1.125rem;
      }

      .calc-group.allowances {
        margin-top: 1rem;
        background: #ffffff;
      }

      .allowance-info {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
      }

      .allowance-breakdown {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
        margin-top: 0.25rem;
      }

      .row {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 0.75rem 0;
        border-bottom: 1px solid #e2e8f0;
      }

      .row:last-child {
        border-bottom: none;
      }

      .row.total {
        margin-top: 0.5rem;
        padding-top: 1rem;
        border-top: 2px solid #e2e8f0;
        border-bottom: none;
        font-weight: 600;
        background: #f8fafc;
        margin-left: -1rem;
        margin-right: -1rem;
        padding-left: 1rem;
        padding-right: 1rem;
      }

      .row.sub-item {
        padding-left: 1rem;
        font-size: 0.875rem;
        color: #64748b;
        background: #ffffff;
        margin-left: -1rem;
        margin-right: -1rem;
        padding-right: 1rem;
      }

      .row.sub-item .allowance-info {
        display: flex;
        align-items: center;
        flex-direction: row;
        gap: 0.5rem;
      }

      .calc-group.summary .row {
        padding: 1rem 0;
        border-bottom: 1px solid #e2e8f0;
      }

      .calc-group.summary .row.total {
        border-bottom: none;
        border-top: 2px solid #6366f1;
        font-size: 1.1em;
        color: #6366f1;
      }

      .salary-info {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
        align-items: flex-end;
      }

      .proration-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: #64748b;
      }

      /* Enterprise Action Buttons */
      .downloadPayslip {
        margin: 0;
      }

      .downloadPayslip button,
      #finaliseButton {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        padding: 0.75rem 1rem;
        background-color: #6366f1;
        color: white;
        border: 1px solid #6366f1;
        border-radius: 2px;
        font-family: 'Inter', sans-serif;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.1s ease;
        box-shadow: none;
        width: 100%;
        text-align: center;
        white-space: nowrap;
      }

      .downloadPayslip button:hover,
      #finaliseButton:hover:not([disabled]) {
        background-color: #4f46e5;
        border-color: #4f46e5;
      }

      .downloadPayslip button:active,
      #finaliseButton:active:not([disabled]) {
        background-color: #4338ca;
        border-color: #4338ca;
      }

      #finaliseButton[disabled] {
        background-color: #9ca3af;
        border-color: #9ca3af;
        cursor: not-allowed;
        opacity: 0.7;
      }

      /* Unfinalize button styling */
      #finaliseButton.unfinalize-button {
        background-color: #f59e0b;
        border-color: #f59e0b;
        color: white;
      }

      #finaliseButton.unfinalize-button:hover:not([disabled]) {
        background-color: #d97706;
        border-color: #d97706;
      }

      #finaliseButton.unfinalize-button:active:not([disabled]) {
        background-color: #b45309;
        border-color: #b45309;
      }

      /* Finalized period state management */
      .period-finalized .input-link,
      .period-finalized .placeholder-link,
      .period-finalized .add-button {
        pointer-events: none;
        opacity: 0.6;
        cursor: not-allowed;
        position: relative;
      }

      .period-finalized .input-link::after,
      .period-finalized .placeholder-link::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(156, 163, 175, 0.3);
        border-radius: inherit;
        z-index: 1;
      }

      .period-finalized .add-button {
        background-color: #e5e7eb;
        color: #9ca3af;
        border-color: #d1d5db;
      }

      .period-finalized .add-button:hover {
        background-color: #e5e7eb;
        color: #9ca3af;
        border-color: #d1d5db;
      }

      /* Period status indicator */
      .period-status-indicator {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
        margin-left: 0.5rem;
      }

      .period-status-indicator.finalized {
        background-color: #fef3c7;
        color: #92400e;
        border: 1px solid #fbbf24;
      }

      .period-status-indicator.unfinalized {
        background-color: #d1fae5;
        color: #065f46;
        border: 1px solid #10b981;
      }



      /* Toast Notification System */
      .toast-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        max-width: 400px;
      }

      .toast {
        background: white;
        border-radius: 8px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        margin-bottom: 10px;
        padding: 1rem;
        border-left: 4px solid;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
      }

      .toast.show {
        opacity: 1;
        transform: translateX(0);
      }

      .toast.success {
        border-left-color: #10b981;
      }

      .toast.error {
        border-left-color: #ef4444;
      }

      .toast.warning {
        border-left-color: #f59e0b;
      }

      .toast.info {
        border-left-color: #3b82f6;
      }

      .toast-icon {
        font-size: 1.25rem;
        margin-top: 0.125rem;
      }

      .toast.success .toast-icon {
        color: #10b981;
      }

      .toast.error .toast-icon {
        color: #ef4444;
      }

      .toast.warning .toast-icon {
        color: #f59e0b;
      }

      .toast.info .toast-icon {
        color: #3b82f6;
      }

      .toast-content {
        flex: 1;
      }

      .toast-title {
        font-weight: 600;
        font-size: 0.875rem;
        margin-bottom: 0.25rem;
        color: #1f2937;
      }

      .toast-message {
        font-size: 0.8125rem;
        color: #6b7280;
        line-height: 1.4;
      }

      .toast-close {
        background: none;
        border: none;
        color: #9ca3af;
        cursor: pointer;
        font-size: 1rem;
        padding: 0;
        margin-left: 0.5rem;
      }

      .toast-close:hover {
        color: #6b7280;
      }

      /* Confirmation Dialog System */
      .confirmation-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 10001;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
      }

      .confirmation-overlay.show {
        opacity: 1;
        visibility: visible;
      }

      .confirmation-dialog {
        background: white;
        border-radius: 12px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        max-width: 500px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        transform: scale(0.9);
        transition: transform 0.3s ease;
      }

      .confirmation-overlay.show .confirmation-dialog {
        transform: scale(1);
      }

      .confirmation-header {
        padding: 1.5rem 1.5rem 1rem;
        border-bottom: 1px solid #e5e7eb;
      }

      .confirmation-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.75rem;
      }

      .confirmation-body {
        padding: 1.5rem;
      }

      .confirmation-message {
        color: #6b7280;
        line-height: 1.6;
        margin-bottom: 1rem;
      }

      .confirmation-details {
        background: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 6px;
        padding: 1rem;
        margin: 1rem 0;
      }

      .confirmation-details h4 {
        margin: 0 0 0.5rem 0;
        font-size: 0.875rem;
        font-weight: 600;
        color: #374151;
      }

      .confirmation-details ul {
        margin: 0;
        padding-left: 1.25rem;
        color: #6b7280;
        font-size: 0.8125rem;
      }

      .confirmation-actions {
        padding: 1rem 1.5rem 1.5rem;
        border-top: 1px solid #e5e7eb;
        display: flex;
        gap: 0.75rem;
        justify-content: flex-end;
      }

      .confirmation-btn {
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        border: 1px solid;
        transition: all 0.2s ease;
      }

      .confirmation-btn.cancel {
        background: white;
        color: #6b7280;
        border-color: #d1d5db;
      }

      .confirmation-btn.cancel:hover {
        background: #f9fafb;
        color: #374151;
      }

      .confirmation-btn.confirm {
        background: #ef4444;
        color: white;
        border-color: #ef4444;
      }

      .confirmation-btn.confirm:hover {
        background: #dc2626;
        border-color: #dc2626;
      }

      .confirmation-btn.warning {
        background: #f59e0b;
        color: white;
        border-color: #f59e0b;
      }

      .confirmation-btn.warning:hover {
        background: #d97706;
        border-color: #d97706;
      }

      /* Payroll Actions Buttons - Clean Design */
      .payroll-actions-buttons {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
        margin-bottom: 1.5rem;
      }

      .payroll-actions-buttons form {
        margin: 0;
        width: 100%;
      }

      .payroll-actions-buttons .downloadPayslip {
        width: 100%;
      }

      /* Calculator Payroll Actions - Always Visible */
      .calculator-payroll-actions {
        display: flex;
        gap: 0.75rem;
        margin-top: 1.5rem;
        padding: 1rem;
        border-top: 1px solid #e5e7eb;
        background: rgba(248, 250, 252, 0.8);
        backdrop-filter: blur(10px);
        border-radius: 0 0 0.75rem 0.75rem;
        justify-content: flex-end;
        align-items: center;
        position: sticky;
        bottom: 0;
        z-index: 10;
        box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
      }

      .calc-action-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        padding: 0.75rem 1.25rem;
        background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
        color: white;
        border: none;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        box-shadow:
          0 3px 6px rgba(99, 102, 241, 0.25),
          0 1px 3px rgba(0, 0, 0, 0.12);
        min-width: 150px;
        position: relative;
        overflow: hidden;
      }

      .calc-action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
      }

      .calc-action-btn:hover:not([disabled]) {
        background: linear-gradient(135deg, #4f46e5 0%, #4338ca 100%);
        transform: translateY(-2px) scale(1.02);
        box-shadow:
          0 6px 12px rgba(99, 102, 241, 0.35),
          0 3px 6px rgba(0, 0, 0, 0.15);
      }

      .calc-action-btn:hover:not([disabled])::before {
        left: 100%;
      }

      .calc-action-btn:active:not([disabled]) {
        transform: translateY(0);
        box-shadow:
          0 2px 4px rgba(99, 102, 241, 0.2),
          0 1px 2px rgba(0, 0, 0, 0.1);
      }

      .calc-action-btn[disabled] {
        background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
        cursor: not-allowed;
        opacity: 0.7;
        transform: none;
      }

      .calc-action-btn.unfinalize-button {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        box-shadow:
          0 2px 4px rgba(245, 158, 11, 0.2),
          0 1px 2px rgba(0, 0, 0, 0.1);
      }

      .calc-action-btn.unfinalize-button:hover:not([disabled]) {
        background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
        box-shadow:
          0 4px 8px rgba(245, 158, 11, 0.3),
          0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .calc-action-btn.download-btn {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        box-shadow:
          0 2px 4px rgba(16, 185, 129, 0.2),
          0 1px 2px rgba(0, 0, 0, 0.1);
      }

      .calc-action-btn.download-btn:hover {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        box-shadow:
          0 4px 8px rgba(16, 185, 129, 0.3),
          0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .calculator-payroll-actions form {
        margin: 0;
      }

      .calculator-payroll-actions .downloadPayslip {
        margin: 0;
      }

      /* Responsive Calculator Actions */
      @media (max-width: 768px) {
        .calculator-payroll-actions {
          flex-direction: column;
          gap: 0.75rem;
          align-items: stretch;
          padding: 1rem;
          position: sticky;
          bottom: 0;
          margin-top: 1rem;
        }

        .calc-action-btn {
          font-size: 0.875rem;
          padding: 0.875rem 1rem;
          min-width: auto;
          width: 100%;
          justify-content: center;
        }
      }

      @media (max-width: 480px) {
        .calc-action-btn {
          font-size: 0.8125rem;
          padding: 0.75rem;
        }
      }

      /* Legacy Payroll Actions (if any remain) */
      @media (max-width: 768px) {
        .payroll-actions-buttons {
          gap: 0.5rem;
          margin-bottom: 1rem;
        }

        .downloadPayslip button,
        #finaliseButton {
          font-size: 0.875rem;
          padding: 0.75rem 1rem;
        }
      }

      @media (max-width: 480px) {
        .downloadPayslip button,
        #finaliseButton {
          font-size: 0.8125rem;
          padding: 0.75rem;
        }
      }

      /* Base Modal Styles */
      .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(2px);
      }

      .modal-content {
        background-color: #fefefe;
        margin: 5% auto;
        padding: 0;
        border: none;
        border-radius: 12px;
        width: 90%;
        max-width: 600px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        animation: modalSlideIn 0.3s ease-out;
      }

      .modal-header {
        padding: 20px 24px;
        border-bottom: 1px solid #e5e7eb;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #f9fafb;
        border-radius: 12px 12px 0 0;
      }

      .modal-header h3 {
        margin: 0;
        color: #1f2937;
        font-size: 18px;
        font-weight: 600;
      }

      .modal-body {
        padding: 24px;
        max-height: 70vh;
        overflow-y: auto;
      }

      .modal-footer {
        padding: 16px 24px;
        border-top: 1px solid #e5e7eb;
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        background-color: #f9fafb;
        border-radius: 0 0 12px 12px;
      }

      .close {
        background: none;
        border: none;
        font-size: 24px;
        font-weight: bold;
        color: #6b7280;
        cursor: pointer;
        padding: 0;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 6px;
        transition: all 0.2s ease;
      }

      .close:hover {
        background-color: #e5e7eb;
        color: #374151;
      }

      @keyframes modalSlideIn {
        from {
          opacity: 0;
          transform: translateY(-50px) scale(0.95);
        }
        to {
          opacity: 1;
          transform: translateY(0) scale(1);
        }
      }

      /* PAYE Modal Styles */
      .paye-clickable:hover {
        background-color: #f3f4f6;
        border-radius: 4px;
        transition: background-color 0.2s ease;
      }

      .paye-calculation-breakdown {
        padding: 20px 0;
      }

      .calculation-section {
        margin-bottom: 30px;
        padding: 20px;
        background-color: #f9fafb;
        border-radius: 8px;
        border-left: 4px solid #3b82f6;
      }

      .calculation-section h4 {
        margin: 0 0 15px 0;
        color: #1f2937;
        font-weight: 600;
        font-size: 16px;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .calc-row {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 14px;
        line-height: 1.5;
      }

      .calc-row.indent {
        margin-left: 40px;
      }

      .calc-row.total-row {
        border-top: 1px solid #d1d5db;
        padding-top: 8px;
        margin-top: 8px;
        font-weight: 600;
      }

      .calc-label {
        min-width: 250px;
        color: #374151;
        font-weight: 500;
      }

      .calc-equals {
        margin: 0 15px;
        color: #6b7280;
        font-weight: bold;
      }

      .calc-formula {
        color: #7c3aed;
        font-style: italic;
        flex: 1;
      }

      .calc-value {
        color: #059669;
        font-weight: 600;
        min-width: 100px;
        text-align: right;
      }

      .final-section {
        border-left-color: #059669;
        background-color: #ecfdf5;
      }

      .final-result {
        background-color: #d1fae5;
        padding: 10px;
        border-radius: 6px;
        margin-top: 10px;
        font-weight: 700;
        font-size: 16px;
      }

      .final-result .calc-value {
        color: #047857;
        font-size: 18px;
      }

      /* Modal responsive adjustments */
      @media (max-width: 768px) {
        .calc-row {
          flex-direction: column;
          align-items: flex-start;
          gap: 5px;
        }

        .calc-label {
          min-width: auto;
        }

        .calc-equals {
          margin: 0;
        }

        .calc-row.indent {
          margin-left: 20px;
        }
      }
    </style>
</head>
<body data-company-code="<%= companyCode %>" data-termination-date="<%= employee.lastDayOfService ? moment.utc(employee.lastDayOfService).format('YYYY-MM-DD') : '' %>">
    <!-- Include Payment Restriction Banner -->
    <%- include('partials/payment-restriction') %>

    <%- include('partials/header') %>
    <nav><%- include('partials/sidebar') %></nav>

    <main class="main-container">
        <!-- Professional Employee Profile Header -->
        <section class="title-section" style="margin-top: 6.5rem;">
          <div class="profile-header">
            <div class="profile-avatar" title="Employee initials">
              <% if (employee.profileImage) { %>
                <img src="<%= employee.profileImage %>" alt="<%= employee.firstName %>'s profile"
                     onerror="this.onerror=null; this.src=null; this.classList.add('fallback'); this.innerHTML='<%= employee.firstName[0] %><%= employee.lastName[0] %>'">
              <% } else { %>
                <div class="avatar-initials">
                  <%= employee.firstName[0] %><%= employee.lastName[0] %>
                </div>
              <% } %>
            </div>

            <div class="profile-info">
              <div class="profile-name-section">
                <h1 class="profile-name">
                  <%= employee.firstName %> <%= employee.lastName %>
                  <div class="employee-status"
                       data-status="<%= employee.status || 'Active' %>"
                       title="Employee current status">
                    <i class="ph ph-circle-fill"></i>
                    <span><%= employee.status || 'Active' %></span>
                  </div>
                </h1>
                <div class="profile-title" title="Employee job title">
                  <i class="ph ph-briefcase"></i>
                  <%= employee.jobTitle || 'Employee' %>
                </div>
              </div>
            </div>

            <!-- Employee Navigation -->
            <div class="employee-navigation">
              <% if (typeof navigation !== 'undefined' && navigation && navigation.previous) { %>
                <button class="nav-arrow nav-previous"
                        data-employee-url="<%= navigation.previous.url %>"
                        data-employee-name="<%= navigation.previous.name %>"
                        title="Previous: <%= navigation.previous.name %> (#<%= navigation.previous.employeeNumber %>)">
                  <i class="ph ph-caret-left"></i>
                </button>
              <% } else { %>
                <button class="nav-arrow nav-previous disabled" disabled title="No previous employee">
                  <i class="ph ph-caret-left"></i>
                </button>
              <% } %>

              <% if (typeof navigation !== 'undefined' && navigation && navigation.totalEmployees > 1) { %>
                <span class="nav-position" title="Employee <%= navigation.currentPosition %> of <%= navigation.totalEmployees %>">
                  <%= navigation.currentPosition %> / <%= navigation.totalEmployees %>
                </span>
              <% } %>

              <% if (typeof navigation !== 'undefined' && navigation && navigation.next) { %>
                <button class="nav-arrow nav-next"
                        data-employee-url="<%= navigation.next.url %>"
                        data-employee-name="<%= navigation.next.name %>"
                        title="Next: <%= navigation.next.name %> (#<%= navigation.next.employeeNumber %>)">
                  <i class="ph ph-caret-right"></i>
                </button>
              <% } else { %>
                <button class="nav-arrow nav-next disabled" disabled title="No next employee">
                  <i class="ph ph-caret-right"></i>
                </button>
              <% } %>
            </div>
          </div>

          <div class="quick-info">
            <div class="info-item" title="Employee ID">
              <div class="info-icon">
                <i class="ph ph-identification-card"></i>
              </div>
              <div class="info-text">
                <span class="info-label">Employee ID</span>
                <span class="info-value"><%= employee.companyEmployeeNumber || 'Not Assigned' %></span>
              </div>
            </div>

            <div class="info-item" title="Pay Frequency">
              <div class="info-icon">
                <i class="ph ph-calendar"></i>
              </div>
              <div class="info-text">
                <span class="info-label">Pay Frequency</span>
                <span class="info-value"><%= payFrequency || 'Not Set' %></span>
              </div>
            </div>

            <div class="info-item" title="Department">
              <div class="info-icon">
                <i class="ph ph-buildings"></i>
              </div>
              <div class="info-text">
                <span class="info-label">Department</span>
                <span class="info-value"><%= employee.department || 'Not Assigned' %></span>
              </div>
            </div>
            <%
              // Calculate employee age
              const calculateAge = (dob) => {
                const birthDate = new Date(dob);
                const today = new Date();
                let age = today.getFullYear() - birthDate.getFullYear();
                const monthDiff = today.getMonth() - birthDate.getMonth();
                if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                  age--;
                }
                return age;
              };

              const employeeAge = employee.dob ? calculateAge(employee.dob) : 'N/A';
            %>
            <% if (dobFormatted) { %>
                <div class="info-item" title="Date of Birth">
                  <div class="info-icon">
                    <i class="ph ph-cake"></i>
                  </div>
                  <div class="info-text">
                    <span class="info-label">Date of Birth</span>
                    <span class="info-value">
                        <%= dobFormatted %>
                        <% if (employeeAge && employeeAge > 0) { %>
                            <span class="tag age-tag"><%= employeeAge %> years</span>
                        <% } %>
                    </span>
                  </div>
                </div>
            <% } %>
          </div>
        </section>

  <!-- Professional Action Navigation -->
  <div class="action-tabs">
    <!-- Payroll Tab -->
    <button class="tab-button active" onclick="showTab('payroll')">
      <i class="ph ph-calculator"></i>
      Payroll
    </button>

    <!-- Edit Info Dropdown -->
    <div class="dropdown">
      <button class="tab-button" onclick="toggleDropdown(event)">
        <i class="ph ph-pencil-simple"></i>
        Edit Info
        <i class="ph ph-caret-down"></i>
      </button>
      <div id="dropdownContent" class="dropdown-content">
        <a href="/clients/<%= companyCode %>/employeeProfile/<%= employee._id %>/edit">
          <i class="ph ph-user"></i>
          Basic Info
        </a>
        <a href="/clients/<%= companyCode %>/employeeProfile/<%= employee._id %>/classifications">
          <i class="ph ph-tag"></i>
          Classifications
        </a>
        <a href="/clients/<%= companyCode %>/employeeProfile/<%= employee._id %>/defineRFI">
          <i class="ph ph-file-text"></i>
          Define RFI
        </a>
        <a href="/clients/<%= companyCode %>/employeeProfile/<%= employee._id %>/regularHours">
          <i class="ph ph-clock"></i>
          Regular Hours
        </a>
        <a href="/clients/<%= companyCode %>/employeeProfile/<%= employee._id %>/eti">
          <i class="ph ph-chart-line"></i>
          ETI
        </a>
        <a href="/clients/<%= companyCode %>/employeeProfile/<%= employee._id %>/skillsEquity">
          <i class="ph ph-star"></i>
          Skills Equity
        </a>
      </div>
    </div>

    <!-- Leave Tab -->
    <button class="tab-button" onclick="window.location.href='/clients/<%= company.companyCode %>/employeeManagement/employee/<%= employee._id %>/leave'">
      <i class="ph ph-calendar-blank"></i>
      Leave
    </button>

    <!-- End Service Tab -->
    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/end-service"
       class="tab-button warning"
       role="button">
      <i class="ph ph-sign-out"></i>
      <%= (employee.status === 'Inactive' || employee.status === 'Serving Notice') ? 'Manage End of Service' : 'End Service' %>
    </a>

    <!-- Delete Employee Tab -->
    <button class="tab-button danger" onclick="handleDeleteEmployee('<%= employee._id %>')">
      <i class="ph ph-trash"></i>
      Delete Employee
    </button>
  </div>
       <!-- Main Content Grid -->
<section id="content-section" class="<%= currentPeriod && currentPeriod.isFinalized ? 'period-finalized' : '' %>">

  <!-- Regular Inputs Card -->
  <div class="input-card <%= currentPeriod?.isFinalized ? 'finalized-period' : '' %>">
    <div class="header">
        <h2>Regular Inputs</h2>
        <% if (!currentPeriod?.isFinalized) { %>
            <button class="add-button">
                <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/regularInputs">
                    <i class="ph ph-plus"></i> Add
                </a>
            </button>
        <% } %>
    </div>
    <div class="regular-items">
        <div class="item">
            <% if ((currentPeriod?.basicSalary === undefined || currentPeriod?.basicSalary === null) && !payroll?.basicSalary) { %>
                <% if (currentPeriod?.isFinalized) { %>
                    <div class="input-link finalized-input">
                        <div class="item-header">
                            <i class="ph ph-currency-circle-dollar"></i>
                            Basic Salary
                        </div>
                        <div class="item-value empty">Not configured</div>
                    </div>
                <% } else { %>
                    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/addBasicSalary<%= currentPeriod && currentPeriod.endDate ? '?selectedMonth=' + encodeURIComponent(currentPeriod.endDateBusiness || moment.utc(currentPeriod.endDate).format('YYYY-MM-DD')) : '' %>" class="input-link">
                        <div class="item-header">
                            <i class="ph ph-currency-circle-dollar"></i>
                            Basic Salary
                        </div>
                        <div class="item-value empty">Click to add</div>
                    </a>
                <% } %>
            <% } else { %>
                <div class="item-header">
                    <i class="ph ph-currency-circle-dollar"></i>
                    <% if (currentPeriod?.isFinalized) { %>
                        <span class="finalized-link">Basic Salary</span>
                    <% } else { %>
                        <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/addBasicSalary<%= currentPeriod && currentPeriod.endDate ? '?selectedMonth=' + encodeURIComponent(currentPeriod.endDateBusiness || moment.utc(currentPeriod.endDate).format('YYYY-MM-DD')) : '' %>">Basic Salary</a>
                    <% } %>
                </div>
                <div class="item-value">
                    <!-- CRITICAL FIX: Use only the historical basicSalary value, no fallbacks to current salary -->
                    R <%= Number(basicSalary || 0).toFixed(2) %>
                </div>
            <% } %>
        </div>
        <%
        const hasTravelAllowance = payroll?.travelAllowance?.fixedAllowanceAmount > 0 ||
                                 payroll?.travelAllowance?.businessKilometers > 0 ||
                                 payroll?.travelAllowance?.petrolCardValue > 0 ||
                                 payroll?.travelAllowance?.fixedAllowance ||
                                 payroll?.travelAllowance?.reimbursedExpenses ||
                                 payroll?.travelAllowance?.companyPetrolCard ||
                                 payroll?.travelAllowance?.reimbursedPerKmTravelled;

        if (hasTravelAllowance) {
          const totalAllowance = payroll.travelAllowance.calculationDetails?.totalAllowance || 0;
          const taxableAmount = payroll.travelAllowance.calculationDetails?.taxableAmount || 0;
          const nonTaxableAmount = payroll.travelAllowance.calculationDetails?.nonTaxableAmount || 0;
      %>
        <div class="item">

                <div class="item-header">
                    <i class="ph ph-car"></i>
                    <% if (currentPeriod?.isFinalized) { %>
                        <span class="finalized-link">Travel Allowance</span>
                    <% } else { %>
                        <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/travel-allowance">Travel Allowance</a>
                    <% } %>
                </div>
                <div class="item-value">
                    R <%= Number(totalAllowance).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                    <% if (taxableAmount > 0) { %>
                        <span class="tag taxable">Taxable: R <%= Number(taxableAmount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
                    <% } %>
                    <% if (nonTaxableAmount > 0) { %>
                        <span class="tag non-taxable">Non-Taxable: R <%= Number(nonTaxableAmount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
                    <% } %>
                </div>
        </div>
        <% } %>

        <% if (payroll?.accommodationBenefit > 0) { %>
        <div class="item">
                <div class="item-header">
                    <i class="ph ph-house"></i>
                    <% if (currentPeriod?.isFinalized) { %>
                        <span class="finalized-link">Accommodation Benefit</span>
                    <% } else { %>
                        <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/accommodation-benefit">Accommodation Benefit</a>
                    <% } %>
                </div>
                <div class="item-value">
                    R <%= Number(payroll.accommodationBenefit).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                    <span class="tag taxable">Taxable</span>
                </div>
        </div>
        <% } %>


        <% if (payroll?.companyCarUnderOperatingLease?.amount > 0) { %>
          <div class="item">
              <div class="item-header">
                  <i class="ph ph-car-profile"></i>
                  <% if (currentPeriod?.isFinalized) { %>
                      <span class="finalized-link">Company Car Under Operating Lease</span>
                  <% } else { %>
                      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/company-car-lease">Company Car Under Operating Lease</a>
                  <% } %>
              </div>
              <div class="item-value">
                  R <%= Number(payroll.companyCarUnderOperatingLease.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                  <span class="tag taxable">Taxable</span>
              </div>
          </div>
      <% } %>

        <% if (payroll?.commissionEnabled) { %>
        <div class="item">
            <% if (currentPeriod?.isFinalized) { %>
                <div class="input-link finalized-input">
                    <div class="item-header">
                        <i class="ph ph-trending-up"></i>
                        Commission
                    </div>
                    <div class="item-value empty">Configured</div>
                </div>
            <% } else { %>
                <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/commission" class="input-link">
                    <div class="item-header">
                        <i class="ph ph-trending-up"></i>
                        Commission
                    </div>
                    <div class="item-value empty">Click to configure</div>
                </a>
            <% } %>
        </div>
        <% } %>

        <% if (payroll?.companyCar?.deemedValue > 0) { %>
          <div class="item">
              <div class="item-header">
                  <i class="ph ph-car"></i>
                  <% if (currentPeriod?.isFinalized) { %>
                      <span class="finalized-link">Company Car</span>
                  <% } else { %>
                      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/company-car">Company Car</a>
                  <% } %>
              </div>
              <div class="item-value">
                  R <%= Number(payroll.companyCar.deemedValue).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                  <span class="tag taxable">Taxable</span>
                  <% if (payroll.companyCar.includesMaintenancePlan) { %>
                      <span class="tag info">Includes Maintenance Plan</span>
                  <% } %>
              </div>
          </div>
      <% } %>

      <% if (payroll?.lossOfIncome > 0) { %>
        <div class="item">
                <% if (currentPeriod?.isFinalized) { %>
                    <div class="input-link finalized-input">
                        <div class="item-header">
                            <i class="ph ph-heart-break"></i>
                            Loss of Income
                            <% if (payroll?.data?.get('lossOfIncomeDate')) { %>
                                <span class="date-tag">
                                    <%= new Date(payroll.data.get('lossOfIncomeDate')).toLocaleDateString('en-ZA', { day: 'numeric', month: 'short', year: 'numeric' }) %>
                                </span>
                            <% } %>
                        </div>
                        <div class="item-value">
                            R <%= Number(payroll.lossOfIncome).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                            <span class="tag non-taxable">Non-Taxable</span>
                        </div>
                    </div>
                <% } else { %>
                    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/loss-of-income" class="input-link">
                        <div class="item-header">
                            <i class="ph ph-heart-break"></i>
                            Loss of Income
                            <% if (payroll?.data?.get('lossOfIncomeDate')) { %>
                                <span class="date-tag">
                                    <%= new Date(payroll.data.get('lossOfIncomeDate')).toLocaleDateString('en-ZA', { day: 'numeric', month: 'short', year: 'numeric' }) %>
                                </span>
                            <% } %>
                        </div>
                        <div class="item-value">
                            R <%= Number(payroll.lossOfIncome).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                            <span class="tag non-taxable">Non-Taxable</span>
                        </div>
                    </a>
                <% } %>
        </div>
        <% } %>

        <% if (payroll?.retirementAnnuityFund?.employeeContribution > 0 || payroll?.retirementAnnuityFund?.employerContribution > 0) { %>
        <div class="item">
                <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/retirement-annuity-fund" class="input-link">
                    <div class="item-header">
                        <i class="ph ph-piggy-bank"></i>
                        Retirement Annuity Fund
                        <% if (payroll?.data?.get('retirementAnnuityFundDate')) { %>
                            <span class="date-tag">
                                <%= new Date(payroll.data.get('retirementAnnuityFundDate')).toLocaleDateString('en-ZA', { day: 'numeric', month: 'short', year: 'numeric' }) %>
                            </span>
                        <% } %>
                    </div>
                    <div class="item-value">
                        R <%= Number(payroll.retirementAnnuityFund.employeeContribution).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                        <span class="tag tax-deductible">Tax Deductible</span>
                        <% if (payroll.retirementAnnuityFund.employerContribution > 0) { %>
                            <span class="tag employer">Employer: R <%= Number(payroll.retirementAnnuityFund.employerContribution).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
                        <% } %>
                    </div>
                </a>
        </div>
        <% } %>

        <% if (payroll?.maintenanceOrder > 0) { %>
        <div class="item">
                <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/maintenance-order" class="input-link">
                    <div class="item-header">
                        <i class="ph ph-gavel"></i>
                        Maintenance Order
                        <% if (payroll?.data?.get('maintenanceOrderDate')) { %>
                            <span class="date-tag">
                                <%= new Date(payroll.data.get('maintenanceOrderDate')).toLocaleDateString('en-ZA', { day: 'numeric', month: 'short', year: 'numeric' }) %>
                            </span>
                        <% } %>
                    </div>
                    <div class="item-value">
                        R <%= Number(payroll.maintenanceOrder).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                        <span class="tag non-taxable">Non-Taxable</span>
                    </div>
                </a>
        </div>
        <% } %>

        <!-- Deductions-->

        <% if (payroll?.pensionFund?.fixedContributionEmployee > 0 || payroll?.pensionFund?.rfiEmployee > 0) { %>
          <div class="item">
              <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/pension-fund" class="input-link">
                  <div class="item-header">
                      <i class="ph ph-bank"></i>
                      Pension Fund
                  </div>
                  <div class="item-value">
                      <% if (payroll.pensionFund.contributionCalculation === 'fixedAmount') { %>
                          R <%= Number(payroll.pensionFund.fixedContributionEmployee).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                      <% } else { %>
                          <%= payroll.pensionFund.rfiEmployee %>% of RFI
                      <% } %>
                      <span class="tag tax-deductible">Tax Deductible</span>
                  </div>
              </a>
          </div>
          <% } %>

          <% if (payroll?.medical?.medicalAid > 0) { %>
            <div class="item">
                <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/medical-aid" class="input-link">
                    <div class="item-header">
                        <i class="ph ph-first-aid"></i>
                        Medical Aid
                        <% if (payroll?.data?.get('medicalAidDate')) { %>
                            <span class="date-tag">
                                <%= new Date(payroll.data.get('medicalAidDate')).toLocaleDateString('en-ZA', { day: 'numeric', month: 'short', year: 'numeric' }) %>
                            </span>
                        <% } %>
                    </div>
                    <div class="item-value">
                        R <%= Number(payroll.medical.medicalAid).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                        <% if (payroll.medical.employerContribution > 0) { %>
                            <span class="tag employer">Employer: R <%= Number(payroll.medical.employerContribution).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
                        <% } %>
                        <span class="tag members"><%= payroll.medical.members %> <%= payroll.medical.members === 1 ? 'Member' : 'Members' %></span>
                        <span class="tag tax-deductible">Tax Deductible</span>
                    </div>
                </a>
            </div>
          <% } %>

          <% if (payroll?.retirementAnnuityFund?.employeeContribution > 0) { %>
              <div class="item">
                  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/retirement-annuity" class="input-link">
                      <div class="item-header">
                          <i class="ph ph-piggy-bank"></i>
                          Retirement Annuity
                      </div>
                      <div class="item-value">
                          R <%= Number(payroll.retirementAnnuityFund.employeeContribution).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                          <span class="tag tax-deductible">Tax Deductible</span>
                          <% if (payroll.retirementAnnuityFund.employerContribution > 0) { %>
                              <span class="tag employer">+ Employer</span>
                          <% } %>
                      </div>
                  </a>
              </div>
              <% } %>

              <% if (payroll?.maintenanceOrder?.amount > 0) { %>
                <div class="input-placeholder maintenance-order">
                    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/maintenance-order" class="placeholder-link">
                        <div class="placeholder-content">
                            <div class="placeholder-header">
                                <i class="ph ph-gavel"></i>
                                <span class="component-name">Maintenance Order</span>
                            </div>
                            <div class="placeholder-details">
                                <span class="amount">R <%= Number(payroll.maintenanceOrder.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
                                <span class="tag non-taxable">Non-Taxable</span>
                            </div>
                        </div>
                    </a>
                </div>
                <% } %>

                <% if (payroll?.providentFund?.fixedContributionEmployee > 0 || payroll?.providentFund?.rfiEmployee > 0) { %>
                  <div class="item">
                      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/provident-fund" class="input-link">
                          <div class="item-header">
                              <i class="ph ph-bank"></i>
                              Provident Fund
                          </div>
                          <div class="item-value">
                              <% if (payroll.providentFund.contributionCalculation === 'fixedAmount') { %>
                                  R <%= Number(payroll.providentFund.fixedContributionEmployee).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                              <% } else { %>
                                  <%= payroll.providentFund.rfiEmployee %>% of RFI
                              <% } %>
                              <span class="tag tax-deductible">Tax Deductible</span>
                              <% if (payroll.providentFund.employerContribution > 0) { %>
                                  <span class="tag employer">+ Employer</span>
                              <% } %>
                          </div>
                      </a>
                  </div>
                  <% } %>

                  <% if (payroll?.unionMembershipFee > 0) { %>
                    <div class="item">
                        <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/union-membership-fee" class="input-link">
                            <div class="item-header">
                                <i class="ph ph-handshake"></i>
                                Union Membership Fee
                            </div>
                            <div class="item-value">
                                R <%= Number(payroll.unionMembershipFee).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                                <span class="tag tax-deductible">Tax Deductible</span>
                            </div>
                        </a>
                    </div>
                    <% } %>

                    <% if (payroll?.incomeProtection?.amountDeductedFromEmployee > 0 || payroll?.incomeProtection?.amountPaidByEmployer > 0) { %>
                      <div class="item">
                          <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/income-protection" class="input-link">
                              <div class="item-header">
                                  <i class="ph ph-shield-plus"></i>
                                  Income Protection
                              </div>
                              <div class="item-value">
                                  R <%= Number(payroll.incomeProtection.amountDeductedFromEmployee + payroll.incomeProtection.amountPaidByEmployee).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                                  <span class="tag tax-deductible">Tax Deductible</span>
                                  <% if (payroll.incomeProtection.amountPaidByEmployer > 0) { %>
                                      <span class="tag employer">+ Employer</span>
                                  <% } %>
                                  <% if (payroll.incomeProtection.employerOwnsPolicy) { %>
                                      <span class="tag info">Employer Policy</span>
                                  <% } %>
                              </div>
                          </a>
                      </div>
                      <% } %>


              <!-- Other-->

              <% if (payroll?.savings?.regularDeduction > 0) { %>
                <div class="item">
                    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/savings" class="input-link">
                        <div class="item-header">
                            <i class="ph ph-piggy-bank"></i>
                            Savings
                        </div>
                        <div class="item-value">
                            R <%= Number(payroll.savings.regularDeduction).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                            <span class="tag non-taxable">Non-Taxable</span>
                        </div>
                    </a>
                </div>
                <% } %>

                <% if (payroll?.foreignServiceIncome?.foreignServiceTaxExemption) { %>
                  <div class="item">
                      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/foreign-service-income" class="input-link">
                          <div class="item-header">
                              <i class="ph ph-globe"></i>
                              Foreign Service Income
                          </div>
                          <div class="item-value">
                              <span class="tag tax-exempt">Tax Exempt</span>
                              <% if (payroll.foreignServiceIncome.foreignServiceTaxExemption) { %>
                                  <span class="tag info">Section 10(1)(o)(ii)</span>
                              <% } %>
                          </div>
                      </a>
                  </div>
                  <% } %>

                  <% if (payroll?.employerLoan?.regularRepayment > 0) { %>
                    <div class="item">
                        <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/employer-loan" class="input-link">
                            <div class="item-header">
                                <i class="ph ph-bank"></i>
                                Employer Loan
                            </div>
                            <div class="item-value">
                                R <%= Number(payroll.employerLoan.regularRepayment).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                                <% if (payroll.employerLoan.calculateInterestBenefit) { %>
                                    <span class="tag tax-benefit">Fringe Benefit</span>
                                <% } %>
                                <span class="tag info"><%= payroll.employerLoan.interestRate %>% Interest</span>
                            </div>
                        </a>
                    </div>
                    <% } %>

                    <% if (payroll?.taxDirective?.directiveNumber) { %>
                      <div class="item">
                          <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/tax-directive" class="input-link">
                              <div class="item-header">
                                  <i class="ph ph-file-text"></i>
                                  Tax Directive
                              </div>
                              <div class="item-value">
                                  <%= payroll.taxDirective.directiveNumber %>
                                  <% if (payroll.taxDirective.directiveType === 'Fixed Percentage - IRP3(b) and IRP3(pa)') { %>
                                      <span class="tag info"><%= payroll.taxDirective.percentage %>%</span>
                                  <% } else if (payroll.taxDirective.directiveType === 'Fixed Amount - IRP3(c)') { %>
                                      <span class="tag info">Fixed Amount</span>
                                  <% } %>
                                  <span class="tag tax-directive">SARS Directive</span>
                              </div>
                          </a>
                      </div>
                      <% } %>

                    <% if (payroll?.voluntaryTaxOverDeduction > 0) { %>
                      <div class="item">
                          <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/voluntary-tax-over-deduction" class="input-link">
                              <div class="item-header">
                                  <i class="ph ph-currency-circle-dollar"></i>
                                  Voluntary Tax Over Deduction
                              </div>
                              <div class="item-value">
                                  R <%= Number(payroll.voluntaryTaxOverDeduction).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                                  <span class="tag tax-deductible">Tax Deductible</span>
                              </div>
                          </a>
                      </div>
                      <% } %>

        <% if (payroll?.bursariesAndScholarships?.taxablePortion > 0) { %>
        <div class="item">
                <div class="item-header">
                    <i class="ph ph-graduation-cap"></i>
                    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/bursaries-and-scholarships">Bursaries and Scholarships</a>
                </div>
                <div class="item-value">
                    R <%= Number(payroll.bursariesAndScholarships.taxablePortion).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                    <span class="tag taxable">Taxable</span>
                </div>
        </div>
        <% } %>


        <!-- Deductions Section -->
<!-- Garnishee Order -->
<% if (payroll?.garnishee?.amount > 0) { %>
<div class="input-placeholder garnishee-order">
  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/garnishee" class="placeholder-link">
      <div class="placeholder-content">
          <div class="placeholder-header">
              <i class="ph ph-scales"></i>
              <span class="component-name">Garnishee Order</span>
          </div>
          <div class="placeholder-details">
              <span class="amount">R <%= parseFloat(payroll.garnishee.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
          </div>
      </div>
  </a>
</div>
<% } %>

<!-- Income Protection -->
<% if (currentPeriod?.data?.has('incomeProtection') && parseFloat(currentPeriod.data.get('incomeProtection').amount) > 0) { %>
<div class="input-placeholder income-protection">
  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/income-protection" class="placeholder-link">
      <div class="placeholder-content">
          <div class="placeholder-header">
              <i class="ph ph-shield-check"></i>
              <span class="component-name">Income Protection</span>
          </div>
          <div class="placeholder-details">
              <% const incomeProtection = currentPeriod.data.get('incomeProtection'); %>
              <span class="amount">R <%= parseFloat(incomeProtection.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
          </div>
      </div>
  </a>
</div>
<% } %>

<!-- Maintenance Order -->
<% if (currentPeriod?.data?.has('maintenanceOrder') && parseFloat(currentPeriod.data.get('maintenanceOrder').amount) > 0) { %>
<div class="input-placeholder maintenance-order">
  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/maintenance-order" class="placeholder-link">
      <div class="placeholder-content">
          <div class="placeholder-header">
              <i class="ph ph-gavel"></i>
              <span class="component-name">Maintenance Order</span>
          </div>
          <div class="placeholder-details">
              <% const maintenanceOrder = currentPeriod.data.get('maintenanceOrder'); %>
              <span class="amount">R <%= parseFloat(maintenanceOrder.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
              <span class="tag non-taxable">Non-Taxable</span>
          </div>
      </div>
  </a>
</div>
<% } %>

<!-- Medical Aid -->
<% if (currentPeriod?.data?.has('medicalAid') && parseFloat(currentPeriod.data.get('medicalAid').amount) > 0) { %>
<div class="input-placeholder medical-aid">
  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/medical-aid" class="placeholder-link">
      <div class="placeholder-content">
          <div class="placeholder-header">
              <i class="ph ph-first-aid"></i>
              <span class="component-name">Medical Aid</span>
          </div>
          <div class="placeholder-details">
              <% const medicalAid = currentPeriod.data.get('medicalAid'); %>
              <span class="amount">R <%= parseFloat(medicalAid.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
          </div>
      </div>
  </a>
</div>
<% } %>

<!-- Pension Fund -->
<% if (currentPeriod?.data?.has('pensionFund') && parseFloat(currentPeriod.data.get('pensionFund').amount) > 0) { %>
<div class="input-placeholder pension-fund">
  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/pension-fund" class="placeholder-link">
      <div class="placeholder-content">
          <div class="placeholder-header">
              <i class="ph ph-bank"></i>
              <span class="component-name">Pension Fund</span>
          </div>
          <div class="placeholder-details">
              <% const pensionFund = currentPeriod.data.get('pensionFund'); %>
              <span class="amount">R <%= parseFloat(pensionFund.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
          </div>
      </div>
  </a>
</div>
<% } %>

<!-- Provident Fund -->
<% if (currentPeriod?.data?.has('providentFund') && parseFloat(currentPeriod.data.get('providentFund').amount) > 0) { %>
<div class="input-placeholder provident-fund">
  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/provident-fund" class="placeholder-link">
      <div class="placeholder-content">
          <div class="placeholder-header">
              <i class="ph ph-bank"></i>
              <span class="component-name">Provident Fund</span>
          </div>
          <div class="placeholder-details">
              <% const providentFund = currentPeriod.data.get('providentFund'); %>
              <span class="amount">R <%= parseFloat(providentFund.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
          </div>
      </div>
  </a>
</div>
<% } %>

<!-- Retirement Annuity Fund -->
<% if (currentPeriod?.data?.has('retirementAnnuityFund') && parseFloat(currentPeriod.data.get('retirementAnnuityFund').amount) > 0) { %>
<div class="input-placeholder retirement-annuity-fund">
  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/retirement-annuity-fund" class="placeholder-link">
      <div class="placeholder-content">
          <div class="placeholder-header">
              <i class="ph ph-bank"></i>
              <span class="component-name">Retirement Annuity Fund</span>
          </div>
          <div class="placeholder-details">
              <% const retirementAnnuityFund = currentPeriod.data.get('retirementAnnuityFund'); %>
              <span class="amount">R <%= parseFloat(retirementAnnuityFund.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
          </div>
      </div>
  </a>
</div>
<% } %>

<!-- Union Membership Fee -->
<% if (currentPeriod?.data?.has('unionMembershipFee') && parseFloat(currentPeriod.data.get('unionMembershipFee').amount) > 0) { %>
<div class="input-placeholder union-membership-fee">
  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/union-membership-fee" class="placeholder-link">
      <div class="placeholder-content">
          <div class="placeholder-header">
              <i class="ph ph-users-three"></i>
              <span class="component-name">Union Membership Fee</span>
          </div>
          <div class="placeholder-details">
              <% const unionMembershipFee = currentPeriod.data.get('unionMembershipFee'); %>
              <span class="amount">R <%= parseFloat(unionMembershipFee.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
          </div>
      </div>
  </a>
</div>
<% } %>

<!-- Voluntary Tax Over Deduction -->
<% if (currentPeriod?.data?.has('voluntaryTaxOverDeduction') && parseFloat(currentPeriod.data.get('voluntaryTaxOverDeduction').amount) > 0) { %>
<div class="input-placeholder voluntary-tax-over-deduction">
  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/voluntary-tax-over-deduction" class="placeholder-link">
      <div class="placeholder-content">
          <div class="placeholder-header">
              <i class="ph ph-currency-circle-dollar"></i>
              <span class="component-name">Voluntary Tax Over Deduction</span>
          </div>
          <div class="placeholder-details" style="flex-direction: column; align-items: flex-end; gap: 4px;">
              <span class="amount">R <%= parseFloat(currentPeriod.data.get('voluntaryTaxOverDeduction').amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
              <div style="display: flex; gap: 4px; flex-wrap: wrap; justify-content: flex-end;">
                  <span class="tag tax-deductible">Tax Deductible</span>
              </div>
          </div>
      </div>
  </a>
</div>
<% } %>

<!-- Benefits Section -->
<!-- Accommodation Benefit -->
<% if (currentPeriod?.data?.has('accommodationBenefit') && parseFloat(currentPeriod.data.get('accommodationBenefit').amount) > 0) { %>
  <div class="input-placeholder accommodation-benefit">
    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/accommodation-benefit" class="placeholder-link">
        <div class="placeholder-content">
            <div class="placeholder-header">
                <i class="ph ph-house"></i>
                <span class="component-name">Accommodation Benefit</span>
            </div>
            <div class="placeholder-details">
                <% const accommodationBenefit = currentPeriod.data.get('accommodationBenefit'); %>
                <span class="amount">R <%= parseFloat(accommodationBenefit.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
            </div>
        </div>
    </a>
  </div>
  <% } %>

  <!-- Bursaries and Scholarships -->
  <% if (currentPeriod?.data?.has('bursariesAndScholarships') && parseFloat(currentPeriod.data.get('bursariesAndScholarships').amount) > 0) { %>
  <div class="input-placeholder bursaries-and-scholarships">
    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/bursaries-and-scholarships" class="placeholder-link">
        <div class="placeholder-content">
            <div class="placeholder-header">
                <i class="ph ph-student"></i>
                <span class="component-name">Bursaries and Scholarships</span>
            </div>
            <div class="placeholder-details">
                <% const bursariesAndScholarships = currentPeriod.data.get('bursariesAndScholarships'); %>
                <span class="amount">R <%= parseFloat(bursariesAndScholarships.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
            </div>
        </div>
    </a>
  </div>
  <% } %>

  <!-- Company Car -->
  <% if (currentPeriod?.data?.has('companyCar') && parseFloat(currentPeriod.data.get('companyCar').amount) > 0) { %>
  <div class="input-placeholder company-car">
    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/company-car" class="placeholder-link">
        <div class="placeholder-content">
            <div class="placeholder-header">
                <i class="ph ph-car"></i>
                <span class="component-name">Company Car</span>
            </div>
            <div class="placeholder-details">
                <% const companyCar = currentPeriod.data.get('companyCar'); %>
                <span class="amount">R <%= parseFloat(companyCar.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
            </div>
        </div>
    </a>
  </div>
  <% } %>

  <!-- Company Car Under Operating Lease -->
  <% if (currentPeriod?.data?.has('companyCarUnderOperatingLease') && parseFloat(currentPeriod.data.get('companyCarUnderOperatingLease').amount) > 0) { %>
  <div class="input-placeholder company-car-under-operating-lease">
    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/company-car-lease" class="placeholder-link">
        <div class="placeholder-content">
            <div class="placeholder-header">
                <i class="ph ph-car-profile"></i>
                <span class="component-name">Company Car (Operating Lease)</span>
            </div>
            <div class="placeholder-details">
                <% const companyCarLease = currentPeriod.data.get('companyCarUnderOperatingLease'); %>
                <span class="amount">R <%= parseFloat(companyCarLease.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
            </div>
        </div>
    </a>
  </div>
  <% } %>

  <!-- Other Section -->
  <!-- Employer Loan -->
  <% if (payroll?.employerLoan?.regularRepayment > 0) { %>
  <div class="input-placeholder employer-loan">
    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/employer-loan" class="placeholder-link">
        <div class="placeholder-content">
            <div class="placeholder-header">
                <i class="ph ph-bank"></i>
                <span class="component-name">Employer Loan</span>
            </div>
            <div class="placeholder-details" style="flex-direction: column; align-items: flex-end; gap: 4px;">
                <span class="amount">R <%= parseFloat(payroll.employerLoan.regularRepayment).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
                <div style="display: flex; gap: 4px; flex-wrap: wrap; justify-content: flex-end;">
                    <% if (payroll.employerLoan.calculateInterestBenefit) { %>
                        <span class="tag tax-benefit">Fringe Benefit</span>
                    <% } %>
                    <span class="tag info"><%= payroll.employerLoan.interestRate %>% Interest</span>
                </div>
            </div>
        </div>
    </a>
  </div>
  <% } %>

  <!-- Foreign Service Income -->
  <% if (currentPeriod?.data?.has('foreignServiceIncome') && parseFloat(currentPeriod.data.get('foreignServiceIncome').amount) > 0) { %>
  <div class="input-placeholder foreign-service-income">
    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/foreign-service-income" class="placeholder-link">
        <div class="placeholder-content">
            <div class="placeholder-header">
                <i class="ph ph-globe"></i>
                <span class="component-name">Foreign Service Income</span>
            </div>
            <div class="placeholder-details">
                <% const foreignServiceIncome = currentPeriod.data.get('foreignServiceIncome'); %>
                <span class="amount">R <%= parseFloat(foreignServiceIncome.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
            </div>
        </div>
    </a>
  </div>
  <% } %>

  <!-- Savings -->
  <% if (currentPeriod?.data?.has('savings') && parseFloat(currentPeriod.data.get('savings').amount) > 0) { %>
  <div class="input-placeholder savings">
    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/savings" class="placeholder-link">
        <div class="placeholder-content">
            <div class="placeholder-header">
                <i class="ph ph-piggy-bank"></i>
                <span class="component-name">Savings</span>
            </div>
            <div class="placeholder-details">
                <% const savings = currentPeriod.data.get('savings'); %>
                <span class="amount">R <%= parseFloat(savings.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
            </div>
        </div>
    </a>
  </div>
  <% } %>

  <!-- Tax Directive -->
  <% if (currentPeriod?.data?.has('taxDirective') && parseFloat(currentPeriod.data.get('taxDirective').amount) > 0) { %>
  <div class="input-placeholder tax-directive">
    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/tax-directive" class="placeholder-link">
        <div class="placeholder-content">
            <div class="placeholder-header">
                <i class="ph ph-file-text"></i>
                <span class="component-name">Tax Directive</span>
            </div>
            <div class="placeholder-details">
                <% const taxDirective = currentPeriod.data.get('taxDirective'); %>
                <span class="amount">R <%= parseFloat(taxDirective.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
            </div>
        </div>
    </a>
  </div>
  <% } %>
  <!-- Income Section -->
     <!-- Commission -->
<% if (currentPeriod?.data?.has('commission') && parseFloat(currentPeriod.data.get('commission').amount) > 0) { %>
<div class="input-placeholder commission">
  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/commission" class="placeholder-link">
      <div class="placeholder-content">
          <div class="placeholder-header">
              <i class="ph ph-trending-up"></i>
              <span class="component-name">Commission</span>
          </div>
          <div class="placeholder-details">
              <% const commission = currentPeriod.data.get('commission'); %>
              <span class="amount">R <%= parseFloat(commission.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
          </div>
      </div>
  </a>
</div>
<% } %>

<% if (currentPeriod?.data?.has('lossOfIncome') && parseFloat(currentPeriod.data.get('lossOfIncome').amount) > 0) { %>
<div class="input-placeholder loss-of-income">
  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/loss-of-income" class="placeholder-link">
      <div class="placeholder-content">
          <div class="placeholder-header">
              <i class="ph ph-trending-down"></i>
              <span class="component-name">Loss of Income</span>
          </div>
          <div class="placeholder-details">
              <% const lossOfIncome = currentPeriod.data.get('lossOfIncome'); %>
              <span class="amount">R <%= parseFloat(lossOfIncome.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
          </div>
      </div>
  </a>
</div>
<% } %>

<% if (currentPeriod?.voluntaryTaxOverDeduction > 0) { %>
<div class="input-placeholder voluntary-tax">
  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/voluntary-tax-over-deduction" class="placeholder-link">
      <div class="placeholder-content">
          <div class="placeholder-header">
              <i class="ph ph-money"></i>
              <span class="component-name">Voluntary Tax Over Deduction</span>
          </div>
          <div class="placeholder-details">
              <span class="amount">R <%= parseFloat(currentPeriod.voluntaryTaxOverDeduction).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
          </div>
      </div>
  </a>
</div>
<% } %>

<% if (payroll?.garnishee > 0) { %>
  <div class="item">
    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/garnishee" class="input-link">
      <div class="item-header">
        <i class="ph ph-scales"></i>
        Garnishee
        <% if (payroll?.data?.get('garnisheeDate')) { %>
          <span class="date-tag">
            <%= new Date(payroll.data.get('garnisheeDate')).toLocaleDateString('en-ZA', { day: 'numeric', month: 'short', year: 'numeric' }) %>
          </span>
        <% } %>
      </div>
      <div class="item-value">
        R <%= Number(payroll.garnishee).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
        <span class="tag non-taxable">Non-Taxable</span>
      </div>
    </a>
  </div>
<% } %>








        <!-- Hours Input (if hourly paid) -->


        <!-- Custom Income Items (general, non-quantity types) -->
        <%
        // CRITICAL FIX: Use same period-specific logic as Calculator card
        // For finalized periods: only show items that were actually configured for that period
        // For unfinalized periods: use displayCustomIncomeItems (which includes fallback logic from route)
        const isFinalized = currentPeriod?.isFinalized;
        let customIncomeSource = [];

        if (isFinalized) {
          // FINALIZED: Only use period-specific data (no fallback)
          customIncomeSource = Array.isArray(displayCustomIncomeItems) ? displayCustomIncomeItems : [];
        } else {
          // UNFINALIZED: Use displayCustomIncomeItems (includes fallback from route)
          customIncomeSource = Array.isArray(displayCustomIncomeItems) ? displayCustomIncomeItems : [];
        }
        %>
        <% if (customIncomeSource && customIncomeSource.length > 0) { %>
          <%
            const generalCustomIncomeItems = customIncomeSource.filter(function(item) {
              return item.customIncomeId && item.name && item.inputType !== 'customRateQuantity';
            });
          %>
          <% generalCustomIncomeItems.forEach(function(customIncomeItem) { %>
            <div class="item">
              <div class="item-header">
                <i class="ph ph-plus-circle"></i>
                <% if (currentPeriod?.isFinalized) { %>
                  <span class="finalized-link"><%= customIncomeItem.name %> (Custom Income)</span>
                <% } else { %>
                  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/custom-income/<%= customIncomeItem.customIncomeId %>?mode=edit"><%= customIncomeItem.name %> (Custom Income)</a>
                <% } %>
              </div>
              <div class="item-value">
                <%
                  const displayAmount = (customIncomeItem.calculatedAmount && customIncomeItem.calculatedAmount > 0)
                    ? customIncomeItem.calculatedAmount
                    : (typeof customIncomeItem.amount === 'number' ? customIncomeItem.amount
                      : (typeof customIncomeItem.monthlyAmount === 'number' ? customIncomeItem.monthlyAmount : 0));
                %>
                <% if (displayAmount && Number(displayAmount) > 0) { %>
                  R <%= Number(displayAmount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                  <span class="tag taxable">Taxable</span>
                <% } else { %>
                  <span class="item-value empty">Configured<% if (customIncomeItem.inputType === 'amountPerEmployee' || customIncomeItem.inputType === 'differentEveryPayslip' || customIncomeItem.inputType === 'onceOffSpecificPayslips') { %> - Set amount for this period<% } %></span>
                <% } %>
              </div>
            </div>
          <% }); %>
        <% } %>


        <!-- Custom Income Items (configured quantity-based) -->
        <% if (displayCustomIncomeItems && displayCustomIncomeItems.length > 0) { %>
            <%
            // Filter for configured custom income items (period-specific)
            const configuredCustomIncomeItems = displayCustomIncomeItems.filter(function(item) {
                return item.inputType === 'customRateQuantity' &&
                       item.customIncomeId &&
                       item.name;
            });
            %>
            <% configuredCustomIncomeItems.forEach(function(customIncomeItem) { %>
                <div class="item">
                    <div class="item-header">
                        <i class="ph ph-plus-circle"></i>
                        <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/custom-income/<%= customIncomeItem.customIncomeId %>?mode=edit"><%= customIncomeItem.name %> (Custom Income)</a>
                    </div>
                    <div class="item-value">
                        <% if (customIncomeItem.calculatedAmount && customIncomeItem.calculatedAmount > 0) { %>
                            R <%= Number(customIncomeItem.calculatedAmount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                            <span class="tag taxable">Taxable</span>
                            <% if (customIncomeItem.quantity) { %>
                                <span class="tag info">Qty: <%= customIncomeItem.quantity %></span>
                            <% } %>
                        <% } else { %>
                            <span class="item-value empty">Configured - Set quantity on payslip</span>
                        <% } %>
                    </div>
                </div>
            <% }); %>
        <% } %>

    </div>
</div>


  <!-- Payslip Inputs Card -->
  <div class="input-card <%= currentPeriod?.isFinalized ? 'finalized-period' : '' %>">
    <div class="header">
        <h2>Payslip Inputs</h2>
        <% if (!currentPeriod?.isFinalized) { %>
            <button class="add-button">
                <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff">
                    <i class="ph ph-plus"></i> Add
                </a>
            </button>
        <% } %>
    </div>



    <div class="payslip-items">
        <% if (payroll?.travelAllowance?.reimbursedExpenses || payroll?.travelAllowance?.companyPetrolCard || payroll?.travelAllowance?.reimbursedPerKmTravelled) { %>
            <div class="input-placeholder travel-expenses">
                <% if (currentPeriod?.isFinalized) { %>
                    <div class="placeholder-link finalized-input">
                        <div class="placeholder-content">
                            <div class="placeholder-header">
                                <i class="ph ph-car"></i>
                                <span class="component-name">Travel Expenses</span>
                            </div>
                        </div>
                    </div>
                <% } else { %>
                    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/travel-expenses" class="placeholder-link">
                        <div class="placeholder-content">
                            <div class="placeholder-header">
                                <i class="ph ph-car"></i>
                                <span class="component-name">Travel Expenses</span>
                            </div>
                        </div>
                    </a>
                <% } %>
            </div>
        <% } %>
        <!-- Once-off Items -->
  <% if (payroll?.hourlyPaid) { %>
    <div class="input-placeholder hourly-salary">
        <% if (currentPeriod?.isFinalized) { %>
            <div class="placeholder-link finalized-input">
                <div class="placeholder-content">
                    <div class="placeholder-header">
                        <i class="ph ph-clock-countdown"></i>
                        <span class="component-name">Hourly Salary Input</span>
                    </div>
                    <% if (hourlyRates) { %>
                        <div class="placeholder-details">
                            <p>Normal Hours: <%= hourlyRates.normalHours || 0 %></p>
                            <p>Overtime Hours: <%= hourlyRates.overtimeHours || 0 %></p>
                            <% if (hourlyRates.weeks && hourlyRates.weeks.length > 0) { %>
                                <p>Sunday Hours: <%= hourlyRates.weeks.reduce((total, week) => total + (Number(week.sundayHours) || 0), 0) %></p>
                            <% } %>
                        </div>
                    <% } %>
                </div>
            </div>
        <% } else { %>
            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/basicSalaryHourly" class="placeholder-link">
                <div class="placeholder-content">
                    <div class="placeholder-header">
                        <i class="ph ph-clock-countdown"></i>
                        <span class="component-name">Hourly Salary Input</span>
                    </div>
                    <% if (hourlyRates) { %>
                        <div class="placeholder-details">
                            <p>Normal Hours: <%= hourlyRates.normalHours || 0 %></p>
                            <p>Overtime Hours: <%= hourlyRates.overtimeHours || 0 %></p>
                            <% if (hourlyRates.weeks && hourlyRates.weeks.length > 0) { %>
                                <p>Sunday Hours: <%= hourlyRates.weeks.reduce((total, week) => total + (Number(week.sundayHours) || 0), 0) %></p>
                            <% } %>
                        </div>
                    <% } %>
                </div>
            </a>
        <% } %>
    </div>
<% } %>

        <!-- Custom Income Quantity Input Placeholders -->
        <% if (displayCustomIncomeItems && displayCustomIncomeItems.length > 0) { %>
            <%
            // Filter for configured custom income items (period-specific)
            const configuredCustomIncomeItems = displayCustomIncomeItems.filter(function(item) {
                return item.inputType === 'customRateQuantity' &&
                       item.customIncomeId &&
                       item.name;
            });
            %>
            <% configuredCustomIncomeItems.forEach(function(customIncomeItem) { %>
                <div class="input-placeholder custom-income-quantity">
                    <% if (currentPeriod?.isFinalized) { %>
                        <div class="placeholder-link finalized-input">
                            <div class="placeholder-content">
                                <div class="placeholder-header">
                                    <i class="ph ph-plus-circle"></i>
                                    <span class="component-name"><%= customIncomeItem.name %> - Quantity</span>
                                </div>
                                <div class="placeholder-details">
                                    <% if (customIncomeItem.quantity) { %>
                                        <p>Current Quantity: <%= customIncomeItem.quantity %></p>
                                        <% if (customIncomeItem.customRate) { %>
                                            <p>Rate: R <%= Number(customIncomeItem.customRate).toFixed(2) %></p>
                                            <p>Amount: R <%= Number(customIncomeItem.calculatedAmount || 0).toFixed(2) %></p>
                                        <% } %>
                                    <% } else { %>
                                        <p>No quantity set for this period</p>
                                        <% if (customIncomeItem.customRate) { %>
                                            <p>Rate: R <%= Number(customIncomeItem.customRate).toFixed(2) %></p>
                                        <% } %>
                                    <% } %>
                                </div>
                            </div>
                        </div>
                    <% } else { %>
                        <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/custom-income-quantity/<%= customIncomeItem.customIncomeId %>" class="placeholder-link">
                            <div class="placeholder-content">
                                <div class="placeholder-header">
                                    <i class="ph ph-plus-circle"></i>
                                    <span class="component-name"><%= customIncomeItem.name %> - Quantity</span>
                                </div>
                                <div class="placeholder-details">
                                    <% if (customIncomeItem.quantity) { %>
                                        <p>Current Quantity: <%= customIncomeItem.quantity %></p>
                                        <% if (customIncomeItem.customRate) { %>
                                            <p>Rate: R <%= Number(customIncomeItem.customRate).toFixed(2) %></p>
                                            <p>Amount: R <%= Number(customIncomeItem.calculatedAmount || 0).toFixed(2) %></p>
                                        <% } %>
                                    <% } else { %>
                                        <p>Click to set quantity for this pay period</p>
                                        <% if (customIncomeItem.customRate) { %>
                                            <p>Rate: R <%= Number(customIncomeItem.customRate).toFixed(2) %></p>
                                        <% } %>
                                    <% } %>
                                </div>
                            </div>
                        </a>
                    <% } %>
                </div>
            <% }); %>
        <% } %>

<style>
.placeholder-details {
    padding: 10px;
    font-size: 0.9em;
    color: #666;
}

.placeholder-details p {
    margin: 5px 0;
}

/* Period Modal Styles - Enhanced for Finalized/Unfinalized Periods */
.period-item.unfinalized {
    border-left: 4px solid #10b981; /* Green border for unfinalized periods */
    background-color: #f0fdf4; /* Light green background */
}

.period-item.finalized {
    border-left: 4px solid #6b7280; /* Gray border for finalized periods */
    background-color: #f9fafb; /* Light gray background */
}

.period-status.pending {
    background-color: #10b981;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.period-status.finalized {
    background-color: #6b7280;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status.pending {
    color: #10b981;
    font-weight: 500;
}

.status.completed {
    color: #6b7280;
    font-weight: 500;
}

.period-item.unfinalized:hover {
    background-color: #dcfce7; /* Darker green on hover */
    transform: translateX(5px);
    transition: all 0.2s ease;
}

.period-item.finalized:hover {
    background-color: #f3f4f6; /* Darker gray on hover */
    transform: translateX(5px);
    transition: all 0.2s ease;
}

/* SIMPLE FIX: Removed business rule engine generated periods styles */

.period-item.generated:hover {
    background-color: #dbeafe; /* Darker blue on hover */
    transform: translateX(5px);
    transition: all 0.2s ease;
}

.period-item.generated .period-status {
    background-color: #3b82f6;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
}
</style>
        <!-- Once-off Items Section -->
        <%
        const hasOnceOffItems =
            (payroll?.annualBonus?.amount > 0) ||
            (payroll?.arbitrationAward?.amount > 0) ||
            (payroll?.dividendsSubject?.amount > 0) ||
            (payroll?.extraPay?.amount > 0) ||
            (payroll?.leavePaidOut?.amount > 0) ||
            (payroll?.restraintOfTrade?.amount > 0) ||
            (currentPeriod?.data?.get('travelExpenses')?.expenses > 0) ||
            (currentPeriod?.data?.get('travelExpenses')?.petrolCardSpend > 0) ||
            (currentPeriod?.data?.get('travelExpenses')?.kmsTravelled > 0) ||
            (payroll?.travelAllowance?.reimbursedExpenses || payroll?.travelAllowance?.companyPetrolCard || payroll?.travelAllowance?.reimbursedPerKmTravelled) || // Show when any travel expense feature is enabled
            (payroll?.commissionEnabled); // Show when commission is enabled

        const getOnceOffItems = (payroll) => {
            const items = [];

            if (payroll?.annualBonus?.amount > 0) {
                items.push({
                    type: 'earning',
                    description: payroll.annualBonus.description || 'Annual Bonus',
                    amount: payroll.annualBonus.amount,
                    date: payroll.annualBonus.date
                });
            }

            if (payroll?.arbitrationAward?.amount > 0) {
                items.push({
                    type: 'earning',
                    description: payroll.arbitrationAward.description || 'Arbitration Award',
                    amount: payroll.arbitrationAward.amount,
                    date: payroll.arbitrationAward.date
                });
            }

            // Add travel expenses items
            const travelExpenses = currentPeriod?.data?.get('travelExpenses');
            if (travelExpenses?.expenses > 0) {
                items.push({
                    type: 'earning',
                    description: 'Travel Expenses',
                    amount: travelExpenses.expenses,
                    date: travelExpenses.lastModified
                });
            }

            if (travelExpenses?.petrolCardSpend > 0) {
                items.push({
                    type: 'earning',
                    description: 'Petrol Card Spend',
                    amount: travelExpenses.petrolCardSpend,
                    date: travelExpenses.lastModified
                });
            }

            if (travelExpenses?.kmsTravelled > 0 && travelExpenses?.kmReimbursement > 0) {
                items.push({
                    type: 'earning',
                    description: `Km Reimbursement (${travelExpenses.kmsTravelled} km)`,
                    amount: travelExpenses.kmReimbursement,
                    date: travelExpenses.lastModified
                });
            }

            // Add other once-off items similarly
            return items;
        }
        %>

        <% if (hasOnceOffItems) { %>
            <% getOnceOffItems(payroll).forEach(item => { %>
                <div class="item">
                    <div class="item-header">
                        <i class="ph <%= item.type === 'deduction' ? 'ph-minus-circle' : 'ph-plus-circle' %>"></i>
                        <%= item.description %>
                    </div>
                    <div class="item-value <%= item.type === 'deduction' ? 'deduction' : '' %>">
                        <%= item.type === 'deduction' ? '-' : '+' %> R <%= Number(item.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                    </div>
                </div>
            <% }); %>
        <% } else { %>
            <div class="empty-state">
                <i class="ph ph-receipt"></i>
                <p>No once-off items for this period</p>
                <% if (!currentPeriod?.isFinalized) { %>
                    <!-- Add any additional content for non-finalized periods -->
                <% } %>
            </div>
        <% } %>

        <!-- Annual Bonus Display -->
        <% if (payroll?.annualBonus?.amount > 0) { %>
            <div class="item">
                <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/annualBonus" class="input-link">
                    <div class="item-header">
                        <i class="ph ph-confetti"></i>
                        Annual Bonus
                    </div>
                    <div class="item-value">
                        R <%= Number(payroll.annualBonus.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                        <span class="tag taxable">Taxable</span>
                        <% if (payroll.annualBonus.date) { %>
                            <span class="tag info"><%= new Date(payroll.annualBonus.date).toLocaleDateString('en-ZA') %></span>
                        <% } %>
                    </div>
                </a>
            </div>
        <% } %>
        <!-- Annual Payment -->
<% if (payroll?.annualPayment?.amount > 0) { %>
  <div class="item">
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/annual-payment" class="input-link">
          <div class="item-header">
              <i class="ph ph-currency-circle-dollar"></i>
              Annual Payment
          </div>
          <div class="item-value">
              R <%= Number(payroll.annualPayment.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
              <span class="tag taxable">Taxable</span>
              <% if (payroll.annualPayment.date) { %>
                  <span class="tag info"><%= new Date(payroll.annualPayment.date).toLocaleDateString('en-ZA') %></span>
              <% } %>
          </div>
      </a>
  </div>
<% } %>

<!-- Dividends Subject -->
<% if (payroll?.dividendsSubject?.amount > 0) { %>
  <div class="item">
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/dividends-subject" class="input-link">
          <div class="item-header">
              <i class="ph ph-chart-pie"></i>
              Dividends Subject
          </div>
          <div class="item-value">
              R <%= Number(payroll.dividendsSubject.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
              <span class="tag taxable">Taxable</span>
              <% if (payroll.dividendsSubject.directiveIssueDate) { %>
                  <span class="tag info"><%= new Date(payroll.dividendsSubject.directiveIssueDate).toLocaleDateString('en-ZA') %></span>
              <% } %>
          </div>
      </a>
  </div>
<% } %>

<!-- Extra Pay -->
<% if (payroll?.extraPay > 0) { %>
  <div class="item">
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/extra-pay" class="input-link">
          <div class="item-header">
              <i class="ph ph-plus-circle"></i>
              Extra Pay
          </div>
          <div class="item-value">
              R <%= Number(payroll.extraPay).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
              <span class="tag taxable">Taxable</span>
          </div>
      </a>
  </div>
<% } %>

<!-- Restraint of Trade -->
<% if (payroll?.restraintOfTrade > 0) { %>
  <div class="item">
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/restraint-of-trade" class="input-link">
          <div class="item-header">
              <i class="ph ph-handshake"></i>
              Restraint of Trade
          </div>
          <div class="item-value">
              R <%= Number(payroll.restraintOfTrade).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
              <span class="tag taxable">Taxable</span>
          </div>
      </a>
  </div>
<% } %>
<!-- Broad Based Share Plan -->
<% if (payroll?.broadBasedEmployeeSharePlan > 0) { %>
  <div class="item">
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/broad-based-share-plan" class="input-link">
          <div class="item-header">
              <i class="ph ph-chart-line-up"></i>
              Broad Based Share Plan
          </div>
          <div class="item-value">
              R <%= Number(payroll.broadBasedEmployeeSharePlan).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
              <span class="tag taxable">Taxable</span>
          </div>
      </a>
  </div>
<% } %>

<!-- Computer Allowance -->
<% if (payroll?.computerAllowance > 0) { %>
  <div class="item">
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/computer-allowance" class="input-link">
          <div class="item-header">
              <i class="ph ph-desktop"></i>
              Computer Allowance
          </div>
          <div class="item-value">
              R <%= Number(payroll.computerAllowance).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
              <span class="tag taxable">Taxable</span>
          </div>
      </a>
  </div>
<% } %>

<!-- Expense Claim -->
<% if (payroll?.expenseClaim > 0) { %>
  <div class="item">
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/expense-claim" class="input-link">
          <div class="item-header">
              <i class="ph ph-receipt"></i>
              Expense Claim
          </div>
          <div class="item-value">
              R <%= Number(payroll.expenseClaim).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
              <span class="tag non-taxable">Non-Taxable</span>
          </div>
      </a>
  </div>
<% } %>

<!-- Gain Vesting -->
<% if (payroll?.gainVesting?.directiveIncomeAmount > 0) { %>
  <div class="item">
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/gain-vesting" class="input-link">
          <div class="item-header">
              <i class="ph ph-trend-up"></i>
              Gain Vesting
          </div>
          <div class="item-value">
              R <%= Number(payroll.gainVesting.directiveIncomeAmount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
              <span class="tag taxable">Taxable</span>
              <% if (payroll.gainVesting.date) { %>
                  <span class="tag info"><%= new Date(payroll.gainVesting.date).toLocaleDateString('en-ZA') %></span>
              <% } %>
          </div>
      </a>
  </div>
<% } %>

<!-- Phone Allowance -->
<% if (payroll?.phoneAllowance > 0) { %>
  <div class="item">
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/phone-allowance" class="input-link">
          <div class="item-header">
              <i class="ph ph-phone"></i>
              Phone Allowance
          </div>
          <div class="item-value">
              R <%= Number(payroll.phoneAllowance).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
              <span class="tag taxable">Taxable</span>
          </div>
      </a>
  </div>
<% } %>

<!-- Relocation Allowance -->
<% if (payroll?.relocationAllowance?.taxableAmount > 0 || payroll?.relocationAllowance?.nonTaxableAmount > 0) { %>
  <div class="item">
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/relocation-allowance" class="input-link">
          <div class="item-header">
              <i class="ph ph-house-simple"></i>
              Relocation Allowance
          </div>
          <div class="item-value">
              R <%= Number(payroll.relocationAllowance.taxableAmount + payroll.relocationAllowance.nonTaxableAmount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
              <% if (payroll.relocationAllowance.taxableAmount > 0) { %>
                  <span class="tag taxable">Partially Taxable</span>
              <% } else { %>
                  <span class="tag non-taxable">Non-Taxable</span>
              <% } %>
          </div>
      </a>
  </div>
<% } %>

<!-- Subsistence International -->
<% if (payroll?.subsistenceAllowanceInternational?.totalPaidToEmployee > 0) { %>
  <div class="item">
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/subsistence-international" class="input-link">
          <div class="item-header">
              <i class="ph ph-airplane"></i>
              International Subsistence
          </div>
          <div class="item-value">
              R <%= Number(payroll.subsistenceAllowanceInternational.totalPaidToEmployee).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
              <span class="tag non-taxable">Non-Taxable</span>
              <span class="tag info"><%= payroll.subsistenceAllowanceInternational.numberOfDays %> days</span>
          </div>
      </a>
  </div>
<% } %>

<!-- Subsistence Local -->
<% if (payroll?.subsistenceAllowanceLocal?.fullAmountPaidToEmployee > 0) { %>
  <div class="item">
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/subsistence-local" class="input-link">
          <div class="item-header">
              <i class="ph ph-map-pin"></i>
              Local Subsistence
          </div>
          <div class="item-value">
              R <%= Number(payroll.subsistenceAllowanceLocal.fullAmountPaidToEmployee).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
              <span class="tag non-taxable">Non-Taxable</span>
              <span class="tag info"><%= payroll.subsistenceAllowanceLocal.numberOfDays %> days</span>
          </div>
      </a>
  </div>
<% } %>

<!-- Tool Allowance -->
<% if (payroll?.toolAllowance > 0) { %>
  <div class="item">
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/tool-allowance" class="input-link">
          <div class="item-header">
              <i class="ph ph-wrench"></i>
              Tool Allowance
          </div>
          <div class="item-value">
              R <%= Number(payroll.toolAllowance).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
              <span class="tag taxable">Taxable</span>
          </div>
      </a>
  </div>
<% } %>

<!-- Uniform Allowance -->
<% if (payroll?.uniformAllowance > 0) { %>
  <div class="item">
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/uniform-allowance" class="input-link">
          <div class="item-header">
              <i class="ph ph-t-shirt"></i>
              Uniform Allowance
          </div>
          <div class="item-value">
              R <%= Number(payroll.uniformAllowance).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
              <span class="tag taxable">Taxable</span>
          </div>
      </a>
  </div>
<% } %>
<% if (payroll?.commissionEnabled) { %>
  <div class="item">
    <% if (payroll?.commission > 0) { %>
      <div class="item-header">
        <i class="ph ph-trending-up"></i>
        <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/commission">Commission</a>
        <% if (payroll?.data?.get('commissionDate')) { %>
          <span class="date-tag">
            <%= new Date(payroll.data.get('commissionDate')).toLocaleDateString('en-ZA', { day: 'numeric', month: 'short', year: 'numeric' }) %>
          </span>
        <% } %>
      </div>
      <div class="item-value">
        R <%= Number(payroll.commission).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
        <span class="tag taxable">Taxable</span>
      </div>
    <% } else { %>
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/commission" class="input-link">
        <div class="item-header">
          <i class="ph ph-trending-up"></i>
          Commission
          <% if (payroll?.data?.get('commissionDate')) { %>
            <span class="date-tag">
              <%= new Date(payroll.data.get('commissionDate')).toLocaleDateString('en-ZA', { day: 'numeric', month: 'short', year: 'numeric' }) %>
            </span>
          <% } %>
        </div>
        <div class="item-value empty">Set on payslip</div>
      </a>
    <% } %>
  </div>
<% } %>
    </div>
    </div>


  <!-- Calculator Card -->
  <div class="section" id="calculator">
      <div class="header">
          <h2>Calculator</h2>
          <button id="selectMonthButton" class="selectButton" data-employee-id="<%= employee._id %>">
            <div class="period-display">
                <% if (currentPeriod) { %>
                    <%
                    // Use BusinessDate field for timezone-independent display
                    const currentPeriodEndDateBusiness = currentPeriod.endDateBusiness || moment.utc(currentPeriod.endDate).format('YYYY-MM-DD');
                    %>
                    <%= moment(currentPeriodEndDateBusiness).format('DD/MM/YYYY') %>
                    <span class="status-indicator <%= currentPeriod.isFinalized ? 'completed' : 'pending' %>">
                        <%= currentPeriod.isFinalized ? 'Finalized' : 'Pending' %>
                    </span>
                <% } %>
            </div>
            <i class="ph ph-caret-down"></i>
        </button>
      </div>
      <div class="pagination-controls">
        <button class="nav-button prev" onclick="prevPage()">
          <i class="ph ph-caret-left"></i>
        </button>
        <span class="page-indicator">1 of 4</span>
        <button class="nav-button next" onclick="nextPage()">
          <i class="ph ph-caret-right"></i>
        </button>
      </div>

      <div class="calculator-container">
        <% if (payrollDetails) { %>
        <!-- Page 1: Income Section -->
        <div class="calc-page" data-page="1">
          <div class="calc-group basic-salary">
            <h3><i class="ph ph-money"></i> Basic Salary</h3>
            <div class="row">
              <%
              // Determine label based on whether pro-rata is applied
              let salaryLabel = "Basic Salary";
              if (typeof proratedSalaryResult !== 'undefined' && proratedSalaryResult && proratedSalaryResult.isFirstPeriodWithDOA && Number(proratedSalaryResult.proratedPercentage) < 100) {
                salaryLabel = "Prorated Amount";
              }
              %>
              <label><%= salaryLabel %> (<%= employee.payFrequency.frequency %>):</label>
              <%
              // Pro-rata calculation with error handling
              let displaySalary = basicSalary || 0;
              let showProRataDetails = false;
              let proRataInfo = null;

              try {
                // Check if we have BusinessDate pro-rata results
                if (typeof proratedSalaryResult !== 'undefined' && proratedSalaryResult && proratedSalaryResult.isFirstPeriodWithDOA) {
                  const percentage = Number(proratedSalaryResult.proratedPercentage);

                  // Only show pro-rata details if percentage is not 100%
                  if (percentage < 100) {
                    displaySalary = Number(proratedSalaryResult.proratedSalary);
                    showProRataDetails = true;
                    proRataInfo = {
                      workedDays: proratedSalaryResult.workedDays,
                      totalDays: proratedSalaryResult.totalDaysInPeriod,
                      percentage: percentage,
                      fullSalary: Number(proratedSalaryResult.fullPeriodSalary),
                      proratedAmount: Number(proratedSalaryResult.proratedSalary)
                    };
                  } else {
                    // If 100%, just show basic salary without pro-rata details
                    displaySalary = basicSalary || Number(proratedSalaryResult.fullPeriodSalary);
                    showProRataDetails = false;
                  }
                }
              } catch (error) {
                console.error("Pro-rata calculation error:", error);
                // Fallback to basic salary
                displaySalary = basicSalary || 0;
                showProRataDetails = false;
              }
              %>
              <span>R <%= Number(displaySalary).toFixed(2) %></span>
            </div>

            <% if (showProRataDetails && proRataInfo) { %>
              <!-- Pro-rated Details -->
              <div class="row">
                <label>Pro-rata Details:</label>
                <span><%= proRataInfo.workedDays %> of <%= proRataInfo.totalDays %> days (<%= proRataInfo.percentage.toFixed(2) %>%)</span>
              </div>
              <div class="row prorated-amount">
                <label>Full Period Salary:</label>
                <span>R <%= proRataInfo.fullSalary.toFixed(2) %></span>
              </div>
              <div class="row prorated-amount">
                <label>Prorated Amount:</label>
                <span>R <%= proRataInfo.proratedAmount.toFixed(2) %></span>
              </div>
            <% } %>

            <%
            // Check for unpaid leave deductions
            const unpaidLeaveInfo = currentPeriod?.calculations?.unpaidLeave;
            const hasUnpaidLeave = unpaidLeaveInfo && unpaidLeaveInfo.days > 0;
            %>
            <% if (hasUnpaidLeave) { %>
              <!-- Unpaid Leave Deduction -->
              <div class="row unpaid-leave-deduction" style="border-top: 1px solid #e5e7eb; margin-top: 10px; padding-top: 10px;">
                <label style="color: #dc2626; font-weight: 600;">Unpaid Leave Deduction:</label>
                <span style="color: #dc2626; font-weight: 600;">-R <%= unpaidLeaveInfo.deduction.toFixed(2) %></span>
              </div>
              <div class="row unpaid-leave-details">
                <label>Unpaid Leave Days:</label>
                <span><%= unpaidLeaveInfo.days %> day<%= unpaidLeaveInfo.days === 1 ? '' : 's' %></span>
              </div>
              <% if (unpaidLeaveInfo.details && unpaidLeaveInfo.details.length > 0) { %>
                <div class="row unpaid-leave-breakdown" style="font-size: 0.9em; color: #6b7280;">
                  <label>Details:</label>
                  <span>
                    <% unpaidLeaveInfo.details.forEach((detail, index) => { %>
                      <%= detail.overlapDays %> day<%= detail.overlapDays === 1 ? '' : 's' %> (<%= new Date(detail.startDate).toLocaleDateString() %> - <%= new Date(detail.endDate).toLocaleDateString() %>)<%= index < unpaidLeaveInfo.details.length - 1 ? ', ' : '' %>
                    <% }); %>
                  </span>
                </div>
              <% } %>
            <% } %>

            <!-- Accommodation Benefit Section -->
            <% if (payroll && payroll.accommodationBenefit && payroll.accommodationBenefit > 0) { %>
            <div class="row">
              <label>Accommodation Benefit:</label>
              <span>R <%= Number(payroll.accommodationBenefit).toFixed(2) %></span>
            </div>
            <div class="row sub-item">
              <label>Taxable Portion (100%):</label>
              <div class="allowance-info">
                <span>R <%= Number(payroll.accommodationBenefit).toFixed(2) %></span>
                <span class="tag taxable">Taxable</span>
              </div>
            </div>
            <% } %>

            <!-- Loss of Income Section -->
            <% if (payroll && payroll.lossOfIncome && payroll.lossOfIncome > 0) { %>
            <div class="row">
              <label>Loss of Income:</label>
              <span>R <%= Number(payroll.lossOfIncome).toFixed(2) %></span>
            </div>
            <% } %>

            <!-- Commission Section -->
            <% if (payroll && payroll.commission && payroll.commission > 0) { %>
            <div class="row">
              <label>Commission:</label>
              <div class="allowance-info">
                <span>R <%= Number(payroll.commission).toFixed(2) %></span>
                <span class="tag taxable">Taxable</span>
              </div>
            </div>
            <% } %>

            <!-- Custom Income Section -->
            <%
              // Debug: Check what custom income data is available
              const hasCustomIncomeFromCalculations = currentPeriodCalculations?.customIncome?.total && Number(currentPeriodCalculations.customIncome.total) > 0;
              const hasCustomIncomeFromPayroll = displayCustomIncomeItems && Array.isArray(displayCustomIncomeItems) && displayCustomIncomeItems.length > 0;

              let customIncomeTotal = 0;
              let customIncomeItems = [];

              if (hasCustomIncomeFromCalculations) {
                customIncomeTotal = Number(currentPeriodCalculations.customIncome.total);
                customIncomeItems = currentPeriodCalculations.customIncome.items || [];
              } else if (hasCustomIncomeFromPayroll) {
                // Fallback: calculate from displayCustomIncomeItems
                customIncomeItems = displayCustomIncomeItems.map(function(item) {
                  let amount = 0;
                  if (item.calculatedAmount && item.calculatedAmount > 0) {
                    amount = Number(item.calculatedAmount);
                  } else if (typeof item.amount === 'number') {
                    amount = Number(item.amount);
                  } else if (typeof item.monthlyAmount === 'number') {
                    amount = Number(item.monthlyAmount);
                  }
                  customIncomeTotal += amount;
                  return { name: item.name, amount: amount };
                }).filter(item => item.amount > 0);
              }
            %>

            <% if (customIncomeTotal > 0) { %>
            <div class="row" style="margin-top: 15px;">
              <label>Custom Income:</label>
              <div class="allowance-info">
                <span>R <%= customIncomeTotal.toFixed(2) %></span>
                <span class="tag taxable">Taxable</span>
              </div>
            </div>
            <% if (customIncomeItems && customIncomeItems.length > 0) { %>
              <div class="row sub-item">
                <label>Breakdown:</label>
                <div class="allowance-info">
                  <ul style="margin: 0; padding-left: 18px;">
                    <% customIncomeItems.forEach(function(ci) { %>
                      <li><%= ci.name || 'Custom Item' %>: R <%= Number(ci.amount || 0).toFixed(2) %></li>
                    <% }); %>
                  </ul>
                </div>
              </div>
            <% } %>
            <% } %>

            <!-- Total Income -->
            <div class="row total">
              <label>Total Income:</label>
              <%
                // Calculate Total Income for display - build it up from components to ensure accuracy
                // Start with basic salary components
                let calculatedTotalIncome = 0;

                // 1. Basic Salary (prorated if applicable)
                const basicSalaryAmount = Number(basicSalary || 0);
                calculatedTotalIncome += basicSalaryAmount;

                // 2. Accommodation Benefit
                const accommodationBenefitForTotal = Number(payroll?.accommodationBenefit || 0);
                calculatedTotalIncome += accommodationBenefitForTotal;

                // 3. Loss of Income
                const lossOfIncomeAmount = Number(payroll?.lossOfIncome || 0);
                calculatedTotalIncome += lossOfIncomeAmount;

                // 4. Commission
                const commissionAmount = Number(payroll?.commission || 0);
                calculatedTotalIncome += commissionAmount;

                // 5. Custom Income (from displayed items)
                const customIncomeDisplayTotal = customIncomeTotal || 0;
                calculatedTotalIncome += customIncomeDisplayTotal;

                // Use backend calculation if available, otherwise use our calculated total
                const backendTotalIncome = Number(payrollDetails?.totalIncome || totalIncome || 0);
                const travelAllowanceForDisplay = payroll?.travelAllowance?.fixedAllowanceAmount || 0;
                const travelExpensesForDisplay = currentPeriodCalculations?.travelExpenses?.total || 0;
                const kmReimbursementForDisplay = currentPeriodCalculations?.kmReimbursement?.total || 0;

                // Backend total income includes allowances, so subtract them for Income section display
                const backendIncomeExcludingAllowances = backendTotalIncome - travelAllowanceForDisplay - travelExpensesForDisplay - kmReimbursementForDisplay;

                // Use the higher of calculated vs backend (to ensure custom income is included)
                const totalIncomeForDisplay = Math.max(calculatedTotalIncome, backendIncomeExcludingAllowances);

                // Debug logging for total income calculation
                console.log('=== CALCULATOR CARD TOTAL INCOME DEBUG ===');
                console.log('Basic salary:', basicSalaryAmount);
                console.log('Accommodation benefit:', accommodationBenefitForTotal);
                console.log('Loss of income:', lossOfIncomeAmount);
                console.log('Commission:', commissionAmount);
                console.log('Custom income total:', customIncomeDisplayTotal);
                console.log('Calculated total income:', calculatedTotalIncome);
                console.log('Backend total income:', backendTotalIncome);
                console.log('Backend excluding allowances:', backendIncomeExcludingAllowances);
                console.log('Final total income for display:', totalIncomeForDisplay);
              %>
              <span>R <%= totalIncomeForDisplay.toFixed(2) %></span>
            </div>
          </div>
        </div>

        <!-- Page 2: Allowances Section -->
        <div class="calc-page" data-page="2">
          <div class="calc-group allowances">
            <h3><i class="ph ph-car"></i> Allowances</h3>
            <%
              const travelAllowanceAmount = Number(payroll?.travelAllowance?.fixedAllowanceAmount || 0);
              const medicalAidAllowanceAmount = Number(medicalAidPaidOutAllowance || 0);
              const showMedicalAidAllowance = medicalAidAllowanceAmount > 0 && payroll?.medical?.employeeHandlesPayment;
              const hasAnyAllowances = travelAllowanceAmount > 0 || showMedicalAidAllowance;

              if (hasAnyAllowances) {
            %>
              <!-- Travel Allowance -->
              <% if (travelAllowanceAmount > 0) { %>

              <div class="row" <%- travelAllowanceAmount === 0 ? 'style="display: none;"' : '' %>>
                <label>Travel Allowance:</label>
                <div class="allowance-info">
                  <span>R <%= travelAllowanceAmount.toFixed(2) %></span>
                </div>
              </div>
              <!-- Taxable Portion -->
              <div class="row sub-item">
                <label>Taxable Portion (<%= payroll?.travelAllowance?.only20PercentTax ? '20%' : '80%' %>):</label>
                <div class="allowance-info">
                  <span>R <%= (travelAllowanceAmount * (payroll?.travelAllowance?.only20PercentTax ? 0.2 : 0.8)).toFixed(2) %></span>
                  <span class="tag taxable">Taxable</span>
                </div>
              </div>
              <!-- Non-taxable Portion -->
              <div class="row sub-item">
                <label>Non-taxable Portion (<%= payroll?.travelAllowance?.only20PercentTax ? '80%' : '20%' %>):</label>
                <div class="allowance-info">
                  <span>R <%= (travelAllowanceAmount * (payroll?.travelAllowance?.only20PercentTax ? 0.8 : 0.2)).toFixed(2) %></span>
                  <span class="tag non-taxable">Non-taxable</span>
                </div>
            <% } %>

            <!-- Travel Expenses Section -->
            <%
            const travelExpensesTotal = currentPeriodCalculations?.travelExpenses?.total || 0;
            if (travelExpensesTotal > 0) {
            %>
              <div class="row" style="margin-top: 15px;">
                <label>Travel Expenses:</label>
                <div class="allowance-info">
                  <span>R <%= Number(travelExpensesTotal).toFixed(2) %></span>
                </div>
              </div>
              <!-- Taxable Portion -->
              <div class="row sub-item">
                <label>Taxable Portion (<%= currentPeriodCalculations?.travelExpenses?.taxablePercentage || 80 %>%):</label>
                <div class="allowance-info">
                  <span>R <%= Number(currentPeriodCalculations?.travelExpenses?.taxableAmount || 0).toFixed(2) %></span>
                  <span class="tag taxable">Taxable</span>
                </div>
              </div>
              <!-- Non-taxable Portion -->
              <div class="row sub-item">
                <label>Non-taxable Portion (<%= (100 - (currentPeriodCalculations?.travelExpenses?.taxablePercentage || 80)) %>%):</label>
                <div class="allowance-info">
                  <span>R <%= Number(currentPeriodCalculations?.travelExpenses?.nonTaxableAmount || 0).toFixed(2) %></span>
                  <span class="tag non-taxable">Non-taxable</span>
                </div>
              </div>
            <% } %>

            <!-- Petrol Card Spend Section -->
            <%
            const petrolCardTotal = currentPeriodCalculations?.petrolCard?.total || 0;
            if (petrolCardTotal > 0) {
            %>
              <div class="row" style="margin-top: 15px;">
                <label>Petrol Card Spend:</label>
                <div class="allowance-info">
                  <span>R <%= Number(petrolCardTotal).toFixed(2) %></span>
                </div>
              </div>
              <!-- Taxable Portion -->
              <div class="row sub-item">
                <label>Taxable Portion (<%= currentPeriodCalculations?.petrolCard?.taxablePercentage || 80 %>%):</label>
                <div class="allowance-info">
                  <span>R <%= Number(currentPeriodCalculations?.petrolCard?.taxableAmount || 0).toFixed(2) %></span>
                  <span class="tag taxable">Taxable</span>
                </div>
              </div>
              <!-- Non-taxable Portion -->
              <div class="row sub-item">
                <label>Non-taxable Portion (<%= (100 - (currentPeriodCalculations?.petrolCard?.taxablePercentage || 80)) %>%):</label>
                <div class="allowance-info">
                  <span>R <%= Number(currentPeriodCalculations?.petrolCard?.nonTaxableAmount || 0).toFixed(2) %></span>
                  <span class="tag non-taxable">Non-taxable</span>
                </div>
              </div>
            <% } %>

            <!-- Km Reimbursement Section -->
            <%
            const kmReimbursementTotal = currentPeriodCalculations?.kmReimbursement?.total || 0;
            if (kmReimbursementTotal > 0) {
            %>
              <div class="row" style="margin-top: 15px;">
                <label>Km Reimbursement:</label>
                <div class="allowance-info">
                  <span>R <%= Number(kmReimbursementTotal).toFixed(2) %></span>
                </div>
              </div>
              <!-- SARS Allowable Portion -->
              <div class="row sub-item">
                <label>SARS Allowable (R<%= Number(currentPeriodCalculations?.kmReimbursement?.sarsRatePerKm || 4.76).toFixed(2) %>/km):</label>
                <div class="allowance-info">
                  <span>R <%= Number(currentPeriodCalculations?.kmReimbursement?.nonTaxableAmount || 0).toFixed(2) %></span>
                  <span class="tag non-taxable">Non-taxable</span>
                </div>
              </div>
              <!-- Taxable Portion -->
              <div class="row sub-item">
                <label>Excess over SARS rate:</label>
                <div class="allowance-info">
                  <span>R <%= Number(currentPeriodCalculations?.kmReimbursement?.taxableAmount || 0).toFixed(2) %></span>
                  <span class="tag taxable">Taxable</span>
                </div>
              </div>
            <% } %>

            <!-- Medical Aid Benefit Paid Out (only show when employee handles payment) -->
            <% if (showMedicalAidAllowance) { %>
              <div class="row" style="margin-top: 15px;">
                <label>Medical Aid Benefit Paid Out:</label>
                <div class="allowance-info">
                  <span>R <%= medicalAidAllowanceAmount.toFixed(2) %></span>
                </div>
              </div>
              <!-- Taxable Portion -->
              <div class="row sub-item">
                <label>Taxable Portion (100%):</label>
                <div class="allowance-info">
                  <span>R <%= medicalAidAllowanceAmount.toFixed(2) %></span>
                  <span class="tag taxable">Taxable</span>
                </div>
              </div>
            <% } %>

            <% } else { %>
              <div class="empty-state">
                <p>No allowances for this period</p>
              </div>
            <% } %>
          </div>

          <!-- Benefits Section -->
          <div class="calc-group benefits">
            <h3><i class="ph ph-gift"></i> Benefits</h3>
            <%
              const accommodationBenefitAmount = Number(payroll?.accommodationBenefit || 0);
              const medicalAidBenefitAmount = Number(medicalAidTaxableBenefit || 0);
              const showMedicalAidBenefit = medicalAidBenefitAmount > 0 && !payroll?.medical?.employeeHandlesPayment;

              // Calculate pension fund employer contribution based on contribution method
              let pensionFundEmployerContribution = 0;
              if (payroll?.pensionFund) {
                if (payroll.pensionFund.contributionCalculation === 'fixedAmount') {
                  pensionFundEmployerContribution = Number(payroll.pensionFund.fixedContributionEmployer || 0);
                } else if (payroll.pensionFund.contributionCalculation === 'percentageRFI' && employee.rfiConfig?.lastCalculation?.amount) {
                  // Calculate based on RFI percentage
                  const rfiAmount = Number(employee.rfiConfig.lastCalculation.amount || 0);
                  const rfiPercentage = Number(payroll.pensionFund.rfiEmployer || 0);
                  pensionFundEmployerContribution = (rfiAmount * rfiPercentage) / 100;
                }
              }

              const hasAnyBenefits = accommodationBenefitAmount > 0 || showMedicalAidBenefit || pensionFundEmployerContribution > 0;

              if (hasAnyBenefits) {
            %>
              <% if (accommodationBenefitAmount > 0) { %>
                <div class="row">
                  <label>Accommodation Benefit:</label>
                  <div class="allowance-info">
                    <span>R <%= accommodationBenefitAmount.toFixed(2) %></span>
                  </div>
                </div>
                <!-- Taxable Portion -->
                <div class="row sub-item">
                  <label>Taxable Portion (100%):</label>
                  <div class="allowance-info">
                    <span>R <%= accommodationBenefitAmount.toFixed(2) %></span>
                    <span class="tag taxable">Taxable</span>
                  </div>
                </div>
              <% } %>

              <!-- Medical Aid Employer Contribution Benefit (only show when company handles payment) -->
              <% if (medicalAidBenefitAmount > 0 && !payroll?.medical?.employeeHandlesPayment) { %>
                <div class="row">
                  <label>Medical Aid Employer Contribution:</label>
                  <div class="allowance-info">
                    <span>R <%= medicalAidBenefitAmount.toFixed(2) %></span>
                  </div>
                </div>
                <!-- Taxable Portion -->
                <div class="row sub-item">
                  <label>Taxable Portion (100%):</label>
                  <div class="allowance-info">
                    <span>R <%= medicalAidBenefitAmount.toFixed(2) %></span>
                    <span class="tag taxable">Taxable</span>
                  </div>
                </div>
              <% } %>

              <!-- Pension Fund Employer Contribution Benefit -->
              <% if (pensionFundEmployerContribution > 0) { %>
                <div class="row">
                  <label>Pension Fund Employer Contribution:</label>
                  <div class="allowance-info">
                    <span>R <%= pensionFundEmployerContribution.toFixed(2) %></span>
                  </div>
                </div>
                <!-- Non-Taxable Benefit -->
                <div class="row sub-item">
                  <label>Taxable Portion (0%):</label>
                  <div class="allowance-info">
                    <span>R 0.00</span>
                    <span class="tag non-taxable">Non-Taxable</span>
                  </div>
                </div>
              <% } %>
            <% } else { %>
              <div class="empty-state">
                <p>No benefits for this period</p>
              </div>
            <% } %>
          </div>
        </div>



        <!-- Page 3: Deductions Section -->
        <div class="calc-page" data-page="3">
          <div class="calc-group deductions">
            <h3><i class="ph ph-minus-circle"></i> Deductions</h3>
            <!-- PAYE -->
            <div class="row paye-clickable" onclick="openPAYEModal()" style="cursor: pointer;" title="Click to view PAYE calculation breakdown">
              <label>PAYE Tax: <i class="ph ph-info" style="margin-left: 5px; color: #6b7280; font-size: 14px;"></i></label>
              <span>R <%= Number(grossPAYE || totalPAYE || 0).toFixed(2) %></span>
            </div>
            <!-- Medical Aid Tax Credits (only if enabled and applicable) -->
            <% if (applyTaxCredits && medicalAidTaxCredit && medicalAidTaxCredit > 0) { %>
            <div class="row" style="color: #10b981;">
              <label>Medical Aid Tax Credits:</label>
              <span>-R <%= Number(medicalAidTaxCredit || 0).toFixed(2) %></span>
            </div>
            <div class="row" style="font-weight: 600; border-top: 1px solid #e5e7eb; padding-top: 8px; margin-top: 4px;">
              <label>Net PAYE (after credits):</label>
              <span>R <%= Number(netPAYE || totalPAYE || 0).toFixed(2) %></span>
            </div>
            <% } %>
            <!-- UIF -->
            <div class="row">
              <label>UIF:</label>
              <span>R <%= Number(totalUIF || 0).toFixed(2) %></span>
            </div>
            <% if (currentPeriodCalculations?.customIncome?.total && Number(currentPeriodCalculations.customIncome.total) > 0) { %>
            <div class="row sub-item">
              <label>UIF Base (Basic Salary + Custom Income):</label>
              <span>R <%= (Number(basicSalary || 0) + Number(currentPeriodCalculations.customIncome.total)).toFixed(2) %></span>
            </div>
            <% } %>
            <!-- Medical Aid Deduction (only show when company handles payment) -->
            <% if (medicalAidEmployeeDeduction && medicalAidEmployeeDeduction > 0 && !payroll?.medical?.employeeHandlesPayment) { %>
            <div class="row">
              <label>Medical Aid:</label>
              <span>R <%= Number(medicalAidEmployeeDeduction || 0).toFixed(2) %></span>
            </div>
            <% } %>
            <!-- Pension Fund Employee Contribution -->
            <%
              // Calculate pension fund employee contribution based on contribution method
              let pensionFundEmployeeContribution = 0;
              if (payroll?.pensionFund) {
                if (payroll.pensionFund.contributionCalculation === 'fixedAmount') {
                  pensionFundEmployeeContribution = Number(payroll.pensionFund.fixedContributionEmployee || 0);
                } else if (payroll.pensionFund.contributionCalculation === 'percentageRFI' && employee.rfiConfig?.lastCalculation?.amount) {
                  // Calculate based on RFI percentage
                  const rfiAmount = Number(employee.rfiConfig.lastCalculation.amount || 0);
                  const rfiPercentage = Number(payroll.pensionFund.rfiEmployee || 0);
                  pensionFundEmployeeContribution = (rfiAmount * rfiPercentage) / 100;
                }
              }
            %>
            <% if (pensionFundEmployeeContribution > 0) { %>
            <div class="row">
              <label>Pension Fund:</label>
              <span>R <%= pensionFundEmployeeContribution.toFixed(2) %></span>
            </div>
            <% } %>
            <!-- Total Deductions -->
            <div class="row total">
              <label>Total Deductions:</label>
              <%
                // Calculate total deductions using conditional tax credits logic (same as summary section) - FIXED
                const payeAmount = (applyTaxCredits && medicalAidTaxCredit && medicalAidTaxCredit > 0) ? (netPAYE || 0) : (grossPAYE || 0);
                const calculatedTotalDeductions = payeAmount + Number(totalUIF || 0) + Number(medicalAidEmployeeDeduction || 0) + Number(pensionFundEmployeeContribution || 0);
              %>
              <span>R <%= calculatedTotalDeductions.toFixed(2) %></span>
            </div>
          </div>
        </div>

        <!-- Page 4: Summary Section -->
        <div class="calc-page" data-page="4">
          <div class="calc-group summary">
            <h3><i class="ph ph-calculator"></i> Summary</h3>
            <%
              // Calculate Total Income - ensure custom income is included
              const summaryTravelAllowance = payroll?.travelAllowance?.fixedAllowanceAmount || 0;
              const summaryTravelExpenses = currentPeriodCalculations?.travelExpenses?.total || 0;
              const summaryKmReimbursement = currentPeriodCalculations?.kmReimbursement?.total || 0;
              const summaryMedicalAidAllowance = Number(medicalAidPaidOutAllowance || 0);

              // Use the same total income calculation as the Income section to ensure consistency
              let summaryCalculatedTotalIncome = 0;
              summaryCalculatedTotalIncome += Number(basicSalary || 0); // Basic Salary
              summaryCalculatedTotalIncome += Number(payroll?.accommodationBenefit || 0); // Accommodation Benefit
              summaryCalculatedTotalIncome += Number(payroll?.lossOfIncome || 0); // Loss of Income
              summaryCalculatedTotalIncome += Number(payroll?.commission || 0); // Commission
              summaryCalculatedTotalIncome += (customIncomeTotal || 0); // Custom Income

              // Use backend calculation if available, otherwise use calculated total
              const summaryBaseIncome = Number(payrollDetails?.totalIncome || totalIncome || 0);
              const summaryBackendIncomeExcludingAllowances = summaryBaseIncome - summaryTravelAllowance - summaryTravelExpenses - summaryKmReimbursement;

              // Use the higher of calculated vs backend to ensure custom income is included
              const summaryTotalIncome = Math.max(summaryCalculatedTotalIncome, summaryBackendIncomeExcludingAllowances);

              // Calculate Total Allowances (including medical aid paid-out allowance when employee handles payment)
              const summaryTotalAllowances = summaryTravelAllowance + summaryTravelExpenses + summaryKmReimbursement + summaryMedicalAidAllowance;

              console.log('=== SUMMARY SECTION TOTAL INCOME DEBUG ===');
              console.log('Summary calculated total income:', summaryCalculatedTotalIncome);
              console.log('Summary backend income excluding allowances:', summaryBackendIncomeExcludingAllowances);
              console.log('Summary final total income:', summaryTotalIncome);
            %>
            <div class="row">
              <label>Total Income (<%= employee.payFrequency.frequency %>):</label>
              <span>R <%= summaryTotalIncome.toFixed(2) %></span>
            </div>
            <% if (summaryTotalAllowances > 0) { %>
            <div class="row">
              <label>Total Allowances:</label>
              <span>R <%= summaryTotalAllowances.toFixed(2) %></span>
            </div>
            <% } %>
            <div class="row">
              <label>Total Deductions:</label>
              <%
                // Use the same calculation as in the deductions section for consistency (conditional tax credits) - FIXED
                const summaryPayeAmount = (applyTaxCredits && medicalAidTaxCredit && medicalAidTaxCredit > 0) ? (netPAYE || 0) : (grossPAYE || 0);
                const summaryCalculatedTotalDeductions = summaryPayeAmount + Number(totalUIF || 0) + Number(medicalAidEmployeeDeduction || 0) + Number(pensionFundEmployeeContribution || 0);
              %>
              <span>R <%= summaryCalculatedTotalDeductions.toFixed(2) %></span>
            </div>
            <div class="row total">
              <label>Nett Pay:</label>
              <%
                // Calculate Nett Pay: (Total Income + Total Allowances) - Total Deductions (using net PAYE)
                const summaryGrossIncome = summaryTotalIncome + summaryTotalAllowances;
                const summaryNettPay = summaryGrossIncome - summaryCalculatedTotalDeductions;
              %>
              <span>R <%= summaryNettPay.toFixed(2) %></span>
            </div>
          </div>
        </div>
        <% } else { %>
          <p>No payroll details available for this period.</p>
        <% } %>
      </div>

      <!-- Payroll Actions - Always Visible -->
      <div class="calculator-payroll-actions">
        <!-- Dynamic form based on period finalization status -->
        <% if (currentPeriod && currentPeriod.isFinalized) { %>
          <!-- Unfinalize form -->
          <form id="finaliseForm" method="post" action="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/unfinalize">
              <input type="hidden" name="employeeId" value="<%= employee._id %>" />
              <input type="hidden" name="currentPeriodEndDate" value="<%= currentPeriod.endDateBusiness || moment.utc(currentPeriod.endDate).format('YYYY-MM-DD') %>" />
              <input type="hidden" name="payFrequency" value="<%= JSON.stringify(employee.payFrequency) %>">
              <input type="hidden" name="_csrf" value="<%= csrfToken %>" />

              <button id="finaliseButton" type="submit" class="unfinalize-button calc-action-btn">
                  <i class="ph ph-arrow-counter-clockwise"></i>
                  Unfinalize Period
              </button>
          </form>
        <% } else { %>
          <!-- Finalize form (existing logic) -->
          <form id="finaliseForm" method="post" action="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/finalize">
              <input type="hidden" name="employeeId" value="<%= employee._id %>" />

              <% if (currentPeriod && currentPeriod.endDate) { %>
                  <input type="hidden" name="currentPeriodEndDate" value="<%= currentPeriod.endDateBusiness || moment.utc(currentPeriod.endDate).format('YYYY-MM-DD') %>" />
              <% } else { %>
                  <input type="hidden" name="currentPeriodEndDate" value="<%= moment.utc(thisMonth).format('YYYY-MM-DD') %>" />
              <% } %>

              <input type="hidden" name="payFrequency" value="<%= JSON.stringify(employee.payFrequency) %>">
              <input type="hidden" name="_csrf" value="<%= csrfToken %>" />

              <!-- Disable the button if no period exists -->
              <button id="finaliseButton" type="button" class="calc-action-btn" <%= !currentPeriod ? 'disabled' : '' %> onclick="showFinalizeConfirmation()">
                  <i class="ph ph-check-circle"></i>
                  <%= !currentPeriod ? 'No Period to Finalise' : 'Finalise Period' %>
              </button>
          </form>
        <% } %>

        <div class="downloadPayslip">
          <button onclick="confirmDownloadPayslip('<%= employee._id %>', '<%= currentPeriod ? (currentPeriod.endDateBusiness || moment.utc(currentPeriod.endDate).format('YYYY-MM-DD')) : moment.utc(thisMonth).format('YYYY-MM-DD') %>', '<%= currentPeriod ? currentPeriod.isFinalized : false %>')" class="calc-action-btn download-btn">
            <i class="ph ph-download-simple"></i>
            Download Payslip
          </button>
        </div>
      </div>
  </div>

  <!-- Enhanced Travel Allowance Calculator (BETA) - Temporarily disabled due to missing partial
  <%# include('partials/enhancedTravelAllowanceCalculator', {
    payroll: payroll,
    employee: employee,
    currentPeriod: currentPeriod
  }) %> -->


</section>

  <!-- Toast Notification Container -->
  <div id="toast-container" class="toast-container"></div>

  <!-- Confirmation Dialog -->
  <div id="confirmation-overlay" class="confirmation-overlay">
    <div class="confirmation-dialog">
      <div class="confirmation-header">
        <h3 id="confirmation-title" class="confirmation-title"></h3>
      </div>
      <div class="confirmation-body">
        <div id="confirmation-message" class="confirmation-message"></div>
        <div id="confirmation-details" class="confirmation-details" style="display: none;"></div>
      </div>
      <div class="confirmation-actions">
        <button id="confirmation-cancel" class="confirmation-btn cancel">Cancel</button>
        <button id="confirmation-confirm" class="confirmation-btn confirm">Confirm</button>
      </div>
    </div>
  </div>

  <!-- Month Selection Modal -->
  <div id="monthModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Select a Pay Period</h3>
        <button class="close">&times;</button>
      </div>
      <ul id="periodsList">
        <% if (currentPeriod) { %>
            <%
            // CRITICAL FIX: Create properly scoped variables for current period
            const currentPeriodId = currentPeriod._id;
            const currentPeriodSalary = Number(currentPeriod.basicSalary || 0);
            const currentPeriodStartDate = currentPeriod.startDate;
            const currentPeriodEndDate = currentPeriod.endDate;
            const currentPeriodFinalized = currentPeriod.isFinalized;
            %>
            <%
            // Use BusinessDate fields for current period display
            const currentPeriodStartDateBusiness = currentPeriod.startDateBusiness || moment.utc(currentPeriodStartDate).format('YYYY-MM-DD');
            const currentPeriodEndDateBusiness = currentPeriod.endDateBusiness || moment.utc(currentPeriodEndDate).format('YYYY-MM-DD');
            %>
            <li class="period-item current" data-period="<%= currentPeriodEndDateBusiness %>">
                <div class="period-info">
                    <i class="fa fa-calendar-check-o" aria-hidden="true"></i>
                    <span class="period-dates">
                        <%= moment(currentPeriodStartDateBusiness).format('DD/MM/YYYY') %> - <%= moment(currentPeriodEndDateBusiness).format('DD/MM/YYYY') %>
                    </span>
                    <span class="period-status current">Current Period</span>
                </div>
                <div class="period-details">
                    <span class="salary" data-period-id="<%= currentPeriodId %>" data-salary="<%= currentPeriodSalary %>">R <%= currentPeriodSalary.toFixed(2) %></span>
                    <% if (!currentPeriodFinalized) { %>
                        <span class="status pending">Pending</span>
                    <% } %>
                </div>
            </li>
        <% } %>

        <%
        // SIMPLE FIX: Use only database periods - no business rule engine
        let allPeriods = [];

        // Add database periods only (payPeriods)
        if (payPeriods && payPeriods.length > 0) {
            allPeriods = [...payPeriods];
        }

        if (allPeriods && allPeriods.length > 0) { %>
            <%
            // BusinessDate Implementation: Show only database periods in the modal
            // Filter out the current period to avoid duplication, then sort by BusinessDate descending
            const otherPeriods = allPeriods
                .filter(period => {
                    const periodId = period._id ? period._id.toString() : '';
                    const currentId = currentPeriod ? currentPeriod._id.toString() : '';
                    return periodId !== currentId;
                })
                .sort((a, b) => {
                    // Use BusinessDate fields for sorting, fallback to legacy Date fields
                    const aEndDate = a.endDateBusiness || moment.utc(a.endDate).format('YYYY-MM-DD');
                    const bEndDate = b.endDateBusiness || moment.utc(b.endDate).format('YYYY-MM-DD');
                    return bEndDate.localeCompare(aEndDate); // String comparison for YYYY-MM-DD
                });

            // Use traditional for loop with proper variable scoping
            for (let i = 0; i < otherPeriods.length; i++) {
                const period = otherPeriods[i];
                // Create local variables to ensure proper scoping
                const periodId = period._id;
                const periodSalary = Number(period.basicSalary || 0);
                // Use BusinessDate fields for timezone-independent display
                const periodStartDateBusiness = period.startDateBusiness || moment.utc(period.startDate).format('YYYY-MM-DD');
                const periodEndDateBusiness = period.endDateBusiness || moment.utc(period.endDate).format('YYYY-MM-DD');
                const periodFinalized = period.isFinalized;
            %>
                <li class="period-item <%= periodFinalized ? 'finalized' : 'unfinalized' %>"
                    data-period="<%= periodEndDateBusiness %>"
                    title="Database Period (BusinessDate)">
                    <div class="period-info">
                        <i class="fa <%= periodFinalized ? 'fa-lock' : 'fa-calendar-o' %>" aria-hidden="true"></i>
                        <span class="period-dates">
                            <%= moment(periodStartDateBusiness).format('DD/MM/YYYY') %> - <%= moment(periodEndDateBusiness).format('DD/MM/YYYY') %>
                        </span>
                        <span class="period-status <%= periodFinalized ? 'finalized' : 'pending' %>">
                            <%= periodFinalized ? 'Finalized' : 'Pending' %>
                        </span>
                    </div>
                    <div class="period-details">
                        <span class="salary" data-period-id="<%= periodId %>" data-salary="<%= periodSalary %>">
                            R <%= periodSalary.toFixed(2) %>
                        </span>
                        <span class="status <%= periodFinalized ? 'completed' : 'pending' %>">
                            <%= periodFinalized ? 'Completed' : 'Pending' %>
                        </span>
                    </div>
                </li>
            <% } %>
        <% } else { %>
            <li class="no-periods">No periods available</li>
        <% } %>
      </ul>
      <div class="modal-footer">
        <a href="/clients/<%= companyCode %>/employeeProfile/<%= employee._id %>/addPayslip" class="btn btn-primary">
            <i class="fa fa-plus"></i> Manually Add Payslip
        </a>
      </div>
    </div>
  </div>

  <!-- PAYE Calculation Breakdown Modal -->
  <div id="payeModal" class="modal">
    <div class="modal-content" style="max-width: 800px;">
      <div class="modal-header">
        <h3><i class="ph ph-calculator"></i> PAYE Tax Calculation Breakdown</h3>
        <button class="close" onclick="closePAYEModal()">&times;</button>
      </div>
      <div class="modal-body">
        <div class="paye-calculation-breakdown">
          <div class="calculation-section">
            <h4>Income Summary</h4>
            <div class="calc-row">
              <span class="calc-label">YTD Regular Income</span>
              <span class="calc-equals">=</span>
              <span class="calc-value" id="ytdRegularIncome">R 0.00</span>
            </div>
            <div class="calc-row">
              <span class="calc-label">YTD Irregular Income</span>
              <span class="calc-equals">=</span>
              <span class="calc-value" id="ytdIrregularIncome">R 0.00</span>
            </div>
            <div class="calc-row total-row">
              <span class="calc-label">YTD Total Income</span>
              <span class="calc-equals">=</span>
              <span class="calc-value" id="ytdTotalIncome">R 0.00</span>
            </div>
          </div>

          <div class="calculation-section">
            <h4>Annual Tax Calculation</h4>
            <div class="calc-row">
              <span class="calc-label">Tax on Annualised Total Income</span>
              <span class="calc-equals">=</span>
              <span class="calc-formula">YearlyTax(Annualised Total Income)</span>
            </div>
            <div class="calc-row indent">
              <span class="calc-equals">=</span>
              <span class="calc-formula" id="annualisedTotalFormula">YearlyTax(R 0.00)</span>
            </div>
            <div class="calc-row indent">
              <span class="calc-equals">=</span>
              <span class="calc-value" id="taxOnAnnualisedTotal">R 0.00</span>
            </div>

            <div class="calc-row">
              <span class="calc-label">Tax on Annualised Regular Income</span>
              <span class="calc-equals">=</span>
              <span class="calc-formula">YearlyTax(Annualised Regular Income)</span>
            </div>
            <div class="calc-row indent">
              <span class="calc-equals">=</span>
              <span class="calc-formula" id="annualisedRegularFormula">YearlyTax(R 0.00)</span>
            </div>
            <div class="calc-row indent">
              <span class="calc-equals">=</span>
              <span class="calc-value" id="taxOnAnnualisedRegular">R 0.00</span>
            </div>

            <div class="calc-row">
              <span class="calc-label">Tax on Irregular Income</span>
              <span class="calc-equals">=</span>
              <span class="calc-formula">Tax on Annualised Total Income - Tax on Annualised Regular Income</span>
            </div>
            <div class="calc-row indent">
              <span class="calc-equals">=</span>
              <span class="calc-formula" id="irregularTaxFormula">R 0.00 - R 0.00</span>
            </div>
            <div class="calc-row indent">
              <span class="calc-equals">=</span>
              <span class="calc-value" id="taxOnIrregular">R 0.00</span>
            </div>
          </div>

          <div class="calculation-section">
            <h4>YTD Tax Calculation</h4>
            <div class="calc-row">
              <span class="calc-label">YTD Tax</span>
              <span class="calc-equals">=</span>
              <span class="calc-formula">Tax on Annualised Regular Income × YTD Periods ÷ Total Periods + Tax on Irregular Income</span>
            </div>
            <div class="calc-row indent">
              <span class="calc-equals">=</span>
              <span class="calc-formula" id="ytdTaxFormula">R 0.00 × 0.00 ÷ 12.00 + R 0.00</span>
            </div>
            <div class="calc-row indent">
              <span class="calc-equals">=</span>
              <span class="calc-value" id="ytdTax">R 0.00</span>
            </div>
          </div>

          <div class="calculation-section final-section">
            <h4>Current Period Tax</h4>
            <div class="calc-row">
              <span class="calc-label">Tax for Current Payslip</span>
              <span class="calc-equals">=</span>
              <span class="calc-formula">YTD Tax - Tax Already Paid - YTD Medical Aid Tax Credits</span>
            </div>
            <div class="calc-row indent">
              <span class="calc-equals">=</span>
              <span class="calc-formula" id="currentTaxFormula">R 0.00 - R 0.00 - R 0.00</span>
            </div>
            <div class="calc-row indent final-result">
              <span class="calc-equals">=</span>
              <span class="calc-value" id="currentPAYE">R 0.00</span>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" onclick="closePAYEModal()">Close</button>
      </div>
    </div>
  </div>


  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Period Modal Salary Debug - BusinessDate Implementation
      console.log('🔍 Period Modal Salary Debug (BusinessDate):', {
        currentPeriodSalary: '<%= currentPeriod ? currentPeriod.basicSalary : "N/A" %>',
        payPeriodsCount: '<%= payPeriods ? payPeriods.length : 0 %>',
        payPeriodsData: [
          <% if (payPeriods && payPeriods.length > 0) { %>
            <%
            // Use proper scoping to prevent closure issues in debugging too
            for (let debugIndex = 0; debugIndex < payPeriods.length; debugIndex++) {
              const debugPeriod = payPeriods[debugIndex];
              const debugStartDateBusiness = debugPeriod.startDateBusiness || moment.utc(debugPeriod.startDate).format('YYYY-MM-DD');
              const debugEndDateBusiness = debugPeriod.endDateBusiness || moment.utc(debugPeriod.endDate).format('YYYY-MM-DD');
            %>
              {
                id: '<%= debugPeriod._id %>',
                startDateBusiness: '<%= debugStartDateBusiness %>',
                endDateBusiness: '<%= debugEndDateBusiness %>',
                legacyStartDate: '<%= debugPeriod.startDate %>',
                legacyEndDate: '<%= debugPeriod.endDate %>',
                basicSalary: <%= debugPeriod.basicSalary || 0 %>,
                isFinalized: <%= debugPeriod.isFinalized %>
              }<%= debugIndex < payPeriods.length - 1 ? ',' : '' %>
            <% } %>
          <% } %>
        ]
      });

      // Verify salary display integrity
      function verifySalaryDisplay() {
        const salaryElements = document.querySelectorAll('.salary[data-period-id]');
        console.log('🔍 Verifying salary display for', salaryElements.length, 'periods:');

        salaryElements.forEach((element, index) => {
          const periodId = element.getAttribute('data-period-id');
          const dataSalary = element.getAttribute('data-salary');
          const displayedText = element.textContent.trim();
          const displayedAmount = displayedText.replace('R ', '').replace(/,/g, '');

          console.log(`Period ${index + 1}:`, {
            periodId,
            dataSalary,
            displayedText,
            displayedAmount,
            isCorrect: parseFloat(dataSalary).toFixed(2) === parseFloat(displayedAmount).toFixed(2)
          });
        });
      }

      // Run verification after DOM is loaded
      setTimeout(verifySalaryDisplay, 100);

      // Payroll Actions Button Debugging
      console.log('🔧 Payroll Actions Debug:', {
        finalizeButton: document.getElementById('finaliseButton'),
        downloadButton: document.querySelector('.downloadPayslip button'),
        companyCode: document.body.dataset.companyCode,
        actionButtons: document.querySelector('.payroll-actions-buttons')
      });

      // Enhanced finalization state management
      const finalizeBtn = document.getElementById('finaliseButton');
      const downloadBtn = document.querySelector('.downloadPayslip button');
      const contentSection = document.getElementById('content-section');
      const isFinalized = contentSection && contentSection.classList.contains('period-finalized');

      if (finalizeBtn) {
        console.log('✅ Finalize button found and ready');

        // Check if this is a finalize button (not unfinalize)
        if (!finalizeBtn.classList.contains('unfinalize-button')) {
          console.log('🔍 Adding finalize validation feedback');

          // Remove the old event listener since we're now using onclick="showFinalizeConfirmation()"
          // The confirmation modal will handle validation and submission
          console.log('🔍 Finalize button configured to use confirmation modal');
        } else {
          // Just log for unfinalize buttons (handled separately)
          finalizeBtn.addEventListener('click', function(e) {
            console.log('🎯 Unfinalize button clicked', {
              disabled: this.disabled,
              form: this.form,
              action: this.form?.action
            });
          });
        }
      } else {
        console.error('❌ Finalize button not found');
      }

      if (downloadBtn) {
        console.log('✅ Download button found and ready');
        downloadBtn.addEventListener('click', function(e) {
          console.log('🎯 Download button clicked', {
            onclick: this.getAttribute('onclick')
          });
        });
      } else {
        console.error('❌ Download button not found');
      }

      // Add user feedback for finalized period interactions
      if (isFinalized) {
        console.log('📋 Period is finalized - adding interaction feedback');

        // Add click handlers for disabled elements to show feedback
        const disabledLinks = document.querySelectorAll('.period-finalized .input-link, .period-finalized .placeholder-link');
        const disabledButtons = document.querySelectorAll('.period-finalized .add-button');

        disabledLinks.forEach(link => {
          link.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            showFinalizationFeedback('This form is disabled because the period is finalized. Use "Unfinalize Period" to make changes.');
          });
        });

        disabledButtons.forEach(button => {
          button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            showFinalizationFeedback('Cannot add new items to a finalized period. Use "Unfinalize Period" to make changes.');
          });
        });
      }

      // Handle travel expenses form visibility based on travel allowance changes
      function checkTravelExpensesVisibility() {
        // Check if we need to refresh the page to update travel expenses visibility
        const urlParams = new URLSearchParams(window.location.search);
        const travelAllowanceUpdated = urlParams.get('travelAllowanceUpdated');

        if (travelAllowanceUpdated === 'true') {
          // Remove the parameter from URL and refresh to update the payslip items
          urlParams.delete('travelAllowanceUpdated');
          const newUrl = window.location.pathname + (urlParams.toString() ? '?' + urlParams.toString() : '');
          window.location.replace(newUrl);
        }
      }

      // Check on page load
      checkTravelExpensesVisibility();

      // Function to show finalization feedback
      function showFinalizationFeedback(message) {
        // Create or update feedback tooltip
        let tooltip = document.getElementById('finalization-tooltip');
        if (!tooltip) {
          tooltip = document.createElement('div');
          tooltip.id = 'finalization-tooltip';
          tooltip.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #1f2937;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
            z-index: 10000;
            max-width: 400px;
            text-align: center;
            font-size: 0.875rem;
            line-height: 1.5;
            opacity: 0;
            transition: opacity 0.3s ease;
          `;
          document.body.appendChild(tooltip);
        }

        tooltip.textContent = message;
        tooltip.style.opacity = '1';

        // Auto-hide after 3 seconds
        setTimeout(() => {
          tooltip.style.opacity = '0';
        }, 3000);
      }

      // Enhanced unfinalize button validation feedback
      if (finalizeBtn && finalizeBtn.classList.contains('unfinalize-button')) {
        console.log('🔄 Adding unfinalize validation feedback');

        finalizeBtn.addEventListener('click', async function(e) {
          e.preventDefault();

          const employeeId = '<%= employee._id %>';
          const periodEndDate = '<%= currentPeriod ? (currentPeriod.endDateBusiness || moment.utc(currentPeriod.endDate).format("YYYY-MM-DD")) : "" %>';
          const companyCode = '<%= company.companyCode %>';

          // Show loading state
          this.disabled = true;
          this.innerHTML = '<i class="ph ph-spinner ph-spin"></i> Checking...';

          try {
            // Check unfinalization eligibility
            const response = await fetch(`/clients/${companyCode}/employeeProfile/${employeeId}/period/${periodEndDate}/can-unfinalize`);
            const data = await response.json();

            if (data.success && data.canUnfinalize) {
              // Show confirmation dialog
              const confirmed = await showConfirmation({
                title: 'Unfinalize Period',
                icon: 'ph-arrow-counter-clockwise',
                message: `Are you sure you want to unfinalize the period ending ${periodEndDate}? This will allow modifications to the payroll data.`,
                details: data.warnings && data.warnings.length > 0 ?
                  `<h4>Please note:</h4><ul>${data.warnings.map(w => `<li>${w}</li>`).join('')}</ul>` : null,
                confirmText: 'Unfinalize Period',
                confirmType: 'warning',
                cancelText: 'Cancel'
              });

              if (confirmed) {
                // Proceed with form submission
                this.innerHTML = '<i class="ph ph-arrow-counter-clockwise"></i> Unfinalizing...';
                this.form.submit();
              } else {
                // Reset button
                this.disabled = false;
                this.innerHTML = '<i class="ph ph-arrow-counter-clockwise"></i> Unfinalize Period';
              }
            } else {
              // Show validation errors
              this.disabled = false;
              this.innerHTML = '<i class="ph ph-arrow-counter-clockwise"></i> Unfinalize Period';

              const errors = data.errors || ['Cannot unfinalize period'];
              showToast('error', 'Cannot Unfinalize', errors.join('; '));
            }
          } catch (error) {
            console.error('Error checking unfinalization:', error);
            this.disabled = false;
            this.innerHTML = '<i class="ph ph-arrow-counter-clockwise"></i> Unfinalize Period';
            showToast('error', 'Error', 'Error checking unfinalization eligibility. Please try again.');
          }
        });
      }

      // Toast Notification System
      window.showToast = function(type, title, message, duration = 5000) {
        const container = document.getElementById('toast-container');
        if (!container) return;

        // Safety checks for undefined values
        if (!type || !title) {
          console.warn('showToast: type and title are required');
          return;
        }

        if (!message || message === 'undefined' || message.trim() === '') {
          console.warn('showToast: skipping toast with empty/undefined message');
          return;
        }

        const toast = document.createElement('div');
        toast.className = `toast ${type}`;

        const iconMap = {
          success: 'ph-check-circle',
          error: 'ph-x-circle',
          warning: 'ph-warning-circle',
          info: 'ph-info'
        };

        // Escape HTML to prevent XSS and handle special characters
        const safeTitle = String(title).replace(/[&<>"']/g, function(match) {
          const escapeMap = { '&': '&amp;', '<': '&lt;', '>': '&gt;', '"': '&quot;', "'": '&#39;' };
          return escapeMap[match];
        });

        const safeMessage = String(message).replace(/[&<>"']/g, function(match) {
          const escapeMap = { '&': '&amp;', '<': '&lt;', '>': '&gt;', '"': '&quot;', "'": '&#39;' };
          return escapeMap[match];
        });

        toast.innerHTML = `
          <i class="ph ${iconMap[type] || 'ph-info'} toast-icon"></i>
          <div class="toast-content">
            <div class="toast-title">${safeTitle}</div>
            <div class="toast-message">${safeMessage}</div>
          </div>
          <button class="toast-close" onclick="this.parentElement.remove()">
            <i class="ph ph-x"></i>
          </button>
        `;

        container.appendChild(toast);

        // Trigger animation
        setTimeout(() => toast.classList.add('show'), 100);

        // Auto-remove after duration
        setTimeout(() => {
          toast.classList.remove('show');
          setTimeout(() => toast.remove(), 300);
        }, duration);
      };

      // Enhanced finalization feedback using toast
      function showFinalizationFeedback(message) {
        showToast('warning', 'Period Finalized', message);
      }

      // Confirmation Dialog System
      window.showConfirmation = function(options) {
        return new Promise((resolve) => {
          const overlay = document.getElementById('confirmation-overlay');
          const title = document.getElementById('confirmation-title');
          const message = document.getElementById('confirmation-message');
          const details = document.getElementById('confirmation-details');
          const cancelBtn = document.getElementById('confirmation-cancel');
          const confirmBtn = document.getElementById('confirmation-confirm');

          // Set content
          title.innerHTML = `<i class="ph ${options.icon || 'ph-warning-circle'}"></i> ${options.title}`;
          message.textContent = options.message;

          if (options.details) {
            details.innerHTML = options.details;
            details.style.display = 'block';
          } else {
            details.style.display = 'none';
          }

          // Set button styles and text
          confirmBtn.textContent = options.confirmText || 'Confirm';
          confirmBtn.className = `confirmation-btn ${options.confirmType || 'confirm'}`;
          cancelBtn.textContent = options.cancelText || 'Cancel';

          // Show dialog
          overlay.classList.add('show');

          // Handle events
          const handleCancel = () => {
            overlay.classList.remove('show');
            cleanup();
            resolve(false);
          };

          const handleConfirm = () => {
            overlay.classList.remove('show');
            cleanup();
            resolve(true);
          };

          const cleanup = () => {
            cancelBtn.removeEventListener('click', handleCancel);
            confirmBtn.removeEventListener('click', handleConfirm);
            overlay.removeEventListener('click', handleOverlayClick);
          };

          const handleOverlayClick = (e) => {
            if (e.target === overlay) {
              handleCancel();
            }
          };

          cancelBtn.addEventListener('click', handleCancel);
          confirmBtn.addEventListener('click', handleConfirm);
          overlay.addEventListener('click', handleOverlayClick);
        });
      };

      // Show flash messages as toasts
      <% if (typeof messages !== 'undefined') { %>
        <% if (messages.success && messages.success.length > 0) { %>
          <% messages.success.forEach(function(msg) { %>
            <% if (msg && msg.trim()) { %>
              showToast('success', 'Success', '<%= msg %>');
            <% } %>
          <% }); %>
        <% } %>
        <% if (messages.error && messages.error.length > 0) { %>
          <% messages.error.forEach(function(msg) { %>
            <% if (msg && msg.trim()) { %>
              showToast('error', 'Error', '<%= msg %>');
            <% } %>
          <% }); %>
        <% } %>
        <% if (messages.warning && messages.warning.length > 0) { %>
          <% messages.warning.forEach(function(msg) { %>
            <% if (msg && msg.trim()) { %>
              showToast('warning', 'Warning', '<%= msg %>');
            <% } %>
          <% }); %>
        <% } %>
        <% if (messages.info && messages.info.length > 0) { %>
          <% messages.info.forEach(function(msg) { %>
            <% if (msg && msg.trim()) { %>
              showToast('info', 'Info', '<%= msg %>');
            <% } %>
          <% }); %>
        <% } %>
      <% } %>

      // Logging for Deductions Section
      const deductionRows = document.querySelectorAll('.deductions .row');
      console.log('Deductions Section Debug:', Array.from(deductionRows).map(row => {
        const label = row.querySelector('label');
        const value = row.querySelector('span');
        return {
          label: label ? label.textContent.trim() : 'Unknown Label',
          value: value ? value.textContent.trim() : 'No Value'
        };
      }));

      // Logging for Summary Section
      const summaryRows = document.querySelectorAll('.calc-group.summary .row');
      console.log('Summary Section Debug:', Array.from(summaryRows).map(row => {
        const label = row.querySelector('label');
        const value = row.querySelector('span');
        return {
          label: label ? label.textContent.trim() : 'Unknown Label',
          value: value ? value.textContent.trim() : 'No Value'
        };
      }));

      // Logging for Payroll Totals
      const payrollTotalsSection = document.querySelector('.calc-group.summary');
      if (payrollTotalsSection) {
        console.log('Payroll Totals Section Full HTML:', payrollTotalsSection.innerHTML);
      }

      // Log PAYE and UIF values using safer selectors - find by label text content
      const findRowByLabelText = (selector, text) => {
        const rows = document.querySelectorAll(selector);
        for (const row of rows) {
          const label = row.querySelector('label');
          if (label && label.textContent.trim().includes(text)) {
            return row;
          }
        }
        return null;
      };

      const payeRow = findRowByLabelText('.deductions .row', 'PAYE Tax');
      const uifRow = findRowByLabelText('.deductions .row', 'UIF');

      console.log('PAYE and UIF Specific Debug:', {
        payeValue: payeRow ? payeRow.querySelector('span')?.textContent.trim() : 'Not Found',
        uifValue: uifRow ? uifRow.querySelector('span')?.textContent.trim() : 'Not Found'
      });
    });
  </script>
  <script>
      document.addEventListener('DOMContentLoaded', function() {
          const modal = document.getElementById('monthModal');
          const btn = document.getElementById('selectMonthButton');
          const span = document.getElementsByClassName('close')[0];
          const periodsList = document.getElementById('periodsList');

          // Show modal with animation
          if (btn) {
              btn.onclick = function() {
                  modal.style.display = 'block';
                  document.body.style.overflow = 'hidden';
              }
          }

          // Close modal with animation
          function closeModal() {
              modal.style.opacity = '0';
              setTimeout(() => {
                  modal.style.display = 'none';
                  modal.style.opacity = '1';
                  document.body.style.overflow = 'auto';
              }, 300);
          }

          if (span) {
              span.onclick = closeModal;
          }

          // Close when clicking outside
          window.onclick = function(event) {
              if (event.target == modal) {
                  closeModal();
              }
              if (event.target == document.getElementById('payeModal')) {
                  closePAYEModal();
              }
          }

          // Handle period selection
          if (periodsList) {
              periodsList.addEventListener('click', function(e) {
                  const periodItem = e.target.closest('.period-item');
                  if (!periodItem) return;

                  const periodDate = periodItem.getAttribute('data-period');
                  const employeeId = btn.getAttribute('data-employee-id');
                  const isFinalized = periodItem.classList.contains('finalized');

                  // Update button text
                  const periodDates = periodItem.querySelector('.period-dates').textContent;
                  const status = isFinalized ? 'Finalized' : 'Pending';

                  btn.innerHTML = `
                      <div class="period-display">
                          ${periodDates}
                          <span class="status-indicator ${isFinalized ? 'completed' : 'pending'}">${status}</span>
                      </div>
                      <ion-icon name="chevron-down-outline"></ion-icon>
                  `;

                  // Navigate to selected period
                  window.location.href = `/clients/<%= companyCode %>/employeeProfile/${employeeId}?selectedMonth=${periodDate}&viewFinalized=${isFinalized}`;
              });
          }

          // Add hover effect
          const periodItems = document.querySelectorAll('.period-item');
          periodItems.forEach(item => {
              item.addEventListener('mouseenter', function() {
                  this.style.transform = 'translateX(5px)';
              });
              item.addEventListener('mouseleave', function() {
                  this.style.transform = 'translateX(0)';
              });
          });
      });

      // Remove duplicate function - using the one from employeeProfile.js

      // Wrapper function to safely handle delete employee action
      function handleDeleteEmployee(employeeId) {
        if (typeof window.confirmDeleteEmployee === 'function') {
          window.confirmDeleteEmployee(employeeId);
        } else {
          console.error('confirmDeleteEmployee function not available. Please refresh the page.');
          alert('Delete function not available. Please refresh the page and try again.');
        }
      }

      // Handle finalized period input links
      document.addEventListener('DOMContentLoaded', function() {
          const isFinalized = <%= currentPeriod?.isFinalized ? 'true' : 'false' %>;

          if (isFinalized) {
              // Find all input links in Regular Inputs and Payslip Inputs cards that haven't been manually updated
              const inputCards = document.querySelectorAll('.input-card');

              inputCards.forEach(card => {
                  // Skip if already has finalized-period class (manually updated)
                  if (card.classList.contains('finalized-period')) return;

                  // Find all anchor tags that are input links
                  const inputLinks = card.querySelectorAll('a[href*="/employeeProfile/"]');

                  inputLinks.forEach(link => {
                      // Skip if already processed
                      if (link.classList.contains('finalized-input') || link.parentElement.classList.contains('finalized-input')) return;

                      // Convert link to disabled state
                      const linkText = link.textContent.trim();
                      const linkHTML = link.innerHTML;

                      // Create disabled span
                      const disabledSpan = document.createElement('span');
                      disabledSpan.className = 'finalized-link';
                      disabledSpan.innerHTML = linkHTML;

                      // Replace the link
                      link.parentNode.replaceChild(disabledSpan, link);
                  });

                  // Handle input-link class elements (full clickable items)
                  const fullInputLinks = card.querySelectorAll('.input-link:not(.finalized-input)');

                  fullInputLinks.forEach(link => {
                      link.classList.add('finalized-input');
                      link.style.cursor = 'not-allowed';
                      link.style.pointerEvents = 'none';
                      link.style.opacity = '0.7';

                      // Update "Click to add" text
                      const emptyValues = link.querySelectorAll('.item-value.empty');
                      emptyValues.forEach(value => {
                          if (value.textContent.includes('Click to add')) {
                              value.textContent = 'Not configured';
                          }
                      });
                  });
              });
          }
      });

      // Make function globally available
      window.handleDeleteEmployee = handleDeleteEmployee;
  </script>
  <script src="/js/employeeProfile.js"></script>
  <script src="/js/employeeActions.js"></script>
  <script src="/js/employee-navigation.js"></script>

  <!-- CRITICAL FIX: Frontend debugging for data synchronization -->
  <script>
  document.addEventListener('DOMContentLoaded', function() {
      console.log('=== FRONTEND DATA DEBUG ===');
      console.log('Template Data Received:', {
          basicSalary: '<%= basicSalary || "undefined" %>',
          currentPeriodBasicSalary: '<%= currentPeriod?.basicSalary || "undefined" %>',
          payrollBasicSalary: '<%= payroll?.basicSalary || "undefined" %>',
          totalIncome: '<%= totalIncome || "undefined" %>',
          selectedMonth: '<%= typeof selectedMonth !== "undefined" && selectedMonth ? moment.utc(selectedMonth).format('YYYY-MM-DD') : "undefined" %>',
          timestamp: new Date().toISOString()
      });

      // Log actual DOM values
      const basicSalaryElement = document.querySelector('.item-value');
      if (basicSalaryElement) {
          console.log('DOM Basic Salary Value:', basicSalaryElement.textContent.trim());
      }
  });
  </script>

  <%- include('partials/mobile-bottom-nav', { req: { path: `/clients/${company.companyCode}/employeeManagement` }, company: company }) %>
</body>
</html>

<script>
  console.log("🎂 Employee Age Debug:", {
    "Date of Birth": "<%= employee.dob ? moment(employee.dob).format('YYYY-MM-DD') : 'N/A' %>",
    "Calculated Age": "<%= employeeAge %>",
    "Employee ID": "<%= employee._id %>"
  });

  // PAYE Modal Functions
  function openPAYEModal() {
      console.log('🔧 Opening PAYE Modal...');
      const modal = document.getElementById('payeModal');
      console.log('🔧 Modal element:', modal);

      if (modal) {
          modal.style.display = 'block';
          document.body.style.overflow = 'hidden';
          console.log('🔧 Modal should be visible now');

          // Calculate and populate PAYE breakdown
          calculatePAYEBreakdown();
      } else {
          console.error('❌ PAYE Modal element not found!');
      }
  }

  function closePAYEModal() {
      const modal = document.getElementById('payeModal');
      modal.style.opacity = '0';
      setTimeout(() => {
          modal.style.display = 'none';
          modal.style.opacity = '1';
          document.body.style.overflow = 'auto';
      }, 300);
  }

  function calculatePAYEBreakdown() {
      // Use data passed from the server instead of trying to read from non-existent input fields
      const basicSalary = <%= basicSalary || 0 %>;
      const totalIncome = <%= totalIncome || 0 %>;
      const totalPAYE = <%= totalPAYE || 0 %>;
      const isProrated = <%= isProrated ? 'true' : 'false' %>;
      const proratedPercentage = <%= proRataDetails?.percentage || 100 %>;
      const frequency = '<%= employee.payFrequency?.frequency || "monthly" %>';
      const commission = <%= payroll?.commission || 0 %>;

      // Calculate periods per year based on frequency
      const periodsPerYear = frequency === 'weekly' ? 52 :
                            frequency === 'biweekly' ? 26 : 12;

      // Get current period number (simplified - using current month for monthly)
      const currentDate = new Date();
      const currentPeriodNumber = frequency === 'monthly' ? currentDate.getMonth() + 1 :
                                 frequency === 'weekly' ? Math.ceil(currentDate.getDate() / 7) : 1;

      // Calculate YTD values (simplified approach)
      const ytdRegularIncome = basicSalary * currentPeriodNumber;
      const ytdIrregularIncome = commission; // Include commission as irregular income
      const ytdTotalIncome = ytdRegularIncome + ytdIrregularIncome;

      // Calculate taxable income for current period (use totalIncome which includes allowances and commission)
      const currentTaxableIncome = totalIncome;

      // Annualise the income
      const annualisedTotalIncome = currentTaxableIncome * periodsPerYear;
      const annualisedRegularIncome = annualisedTotalIncome; // Same as total for regular employees

      // Calculate annual tax using the same logic as the backend
      const taxOnAnnualisedTotal = calculateAnnualPAYE(annualisedTotalIncome);
      const taxOnAnnualisedRegular = calculateAnnualPAYE(annualisedRegularIncome);
      const taxOnIrregular = taxOnAnnualisedTotal - taxOnAnnualisedRegular;

      // Calculate YTD tax
      const ytdTax = (taxOnAnnualisedRegular * currentPeriodNumber / periodsPerYear) + taxOnIrregular;

      // Calculate current period tax
      const taxAlreadyPaid = 0; // Would need YTD data from previous periods
      const medicalAidTaxCredits = 0; // Would need medical aid data
      const currentPAYE = totalPAYE; // Use the actual calculated PAYE from the backend

      // Update modal values with proper currency formatting
      document.getElementById('ytdRegularIncome').textContent = formatCurrency(ytdRegularIncome);
      document.getElementById('ytdIrregularIncome').textContent = formatCurrency(ytdIrregularIncome);
      document.getElementById('ytdTotalIncome').textContent = formatCurrency(ytdTotalIncome);

      document.getElementById('annualisedTotalFormula').textContent = `YearlyTax(${formatCurrency(annualisedTotalIncome)})`;
      document.getElementById('taxOnAnnualisedTotal').textContent = formatCurrency(taxOnAnnualisedTotal);

      document.getElementById('annualisedRegularFormula').textContent = `YearlyTax(${formatCurrency(annualisedRegularIncome)})`;
      document.getElementById('taxOnAnnualisedRegular').textContent = formatCurrency(taxOnAnnualisedRegular);

      document.getElementById('irregularTaxFormula').textContent = `${formatCurrency(taxOnAnnualisedTotal)} - ${formatCurrency(taxOnAnnualisedRegular)}`;
      document.getElementById('taxOnIrregular').textContent = formatCurrency(taxOnIrregular);

      document.getElementById('ytdTaxFormula').textContent = `${formatCurrency(taxOnAnnualisedRegular)} × ${currentPeriodNumber.toFixed(2)} ÷ ${periodsPerYear.toFixed(2)} + ${formatCurrency(taxOnIrregular)}`;
      document.getElementById('ytdTax').textContent = formatCurrency(ytdTax);

      document.getElementById('currentTaxFormula').textContent = `${formatCurrency(ytdTax)} - ${formatCurrency(taxAlreadyPaid)} - ${formatCurrency(medicalAidTaxCredits)}`;
      document.getElementById('currentPAYE').textContent = formatCurrency(currentPAYE);

      // Add pro-rata information if applicable
      if (isProrated) {
          const proRataNote = document.createElement('div');
          proRataNote.className = 'calc-row';
          proRataNote.style.marginTop = '10px';
          proRataNote.style.fontStyle = 'italic';
          proRataNote.style.color = '#6b7280';
          proRataNote.innerHTML = `<span class="calc-label">Note: Pro-rated at ${proratedPercentage.toFixed(2)}% for partial period</span>`;
          document.querySelector('.final-section .calc-row:last-child').after(proRataNote);
      }
  }

  function calculateAnnualPAYE(annualIncome) {
      // Simplified PAYE calculation - using 2024/2025 tax brackets
      if (annualIncome <= 237100) return annualIncome * 0.18;
      if (annualIncome <= 370500) return 42678 + (annualIncome - 237100) * 0.26;
      if (annualIncome <= 512800) return 79362 + (annualIncome - 370500) * 0.31;
      if (annualIncome <= 673000) return 123474 + (annualIncome - 512800) * 0.36;
      if (annualIncome <= 857900) return 181086 + (annualIncome - 673000) * 0.39;
      if (annualIncome <= 1817000) return 253197 + (annualIncome - 857900) * 0.41;
      return 646549 + (annualIncome - 1817000) * 0.45;
  }

  // Helper function to format currency consistently
  function formatCurrency(amount) {
      if (typeof amount !== 'number') {
          amount = parseFloat(amount) || 0;
      }
      return 'R ' + amount.toLocaleString('en-ZA', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
      });
  }

  // Finalize confirmation modal functions
  function showFinalizeConfirmation() {
    const modal = document.getElementById('finalizeConfirmationModal');
    const periodInfo = document.getElementById('finalizePeriodInfo');

    // Get period information
    const currentPeriodEndDate = document.querySelector('input[name="currentPeriodEndDate"]').value;
    const periodDate = new Date(currentPeriodEndDate);
    const formattedDate = periodDate.toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    periodInfo.textContent = formattedDate;
    modal.style.display = 'flex';
  }

  function hideFinalizeConfirmation() {
    const modal = document.getElementById('finalizeConfirmationModal');
    modal.style.display = 'none';
  }

  async function confirmFinalization() {
    // Hide modal
    hideFinalizeConfirmation();

    // Update button state
    const finalizeBtn = document.getElementById('finaliseButton');
    finalizeBtn.disabled = true;
    finalizeBtn.innerHTML = '<i class="ph ph-spinner ph-spin"></i> Checking...';

    const employeeId = '<%= employee._id %>';
    const periodEndDate = '<%= currentPeriod ? (currentPeriod.endDateBusiness || moment.utc(currentPeriod.endDate).format("YYYY-MM-DD")) : "" %>';
    const companyCode = '<%= company.companyCode %>';

    try {
      // Check finalization eligibility
      const response = await fetch(`/clients/${companyCode}/employeeProfile/${employeeId}/period/${periodEndDate}/can-finalize`);
      const data = await response.json();

      if (data.success && data.canFinalize) {
        // Validation passed, proceed with form submission
        finalizeBtn.innerHTML = '<i class="ph ph-check-circle"></i> Finalizing...';
        document.getElementById('finaliseForm').submit();
      } else {
        // Show validation errors
        finalizeBtn.disabled = false;
        finalizeBtn.innerHTML = '<i class="ph ph-check-circle"></i> Finalise Period';

        const errors = data.errors || ['Cannot finalize period'];
        showToast('error', 'Cannot Finalize', errors.join('; '));
      }
    } catch (error) {
      console.error('Error checking finalization:', error);
      finalizeBtn.disabled = false;
      finalizeBtn.innerHTML = '<i class="ph ph-check-circle"></i> Finalise Period';
      showToast('error', 'Error', 'Error checking finalization eligibility. Please try again.');
    }
  }

  // Close modal when clicking outside
  window.onclick = function(event) {
    const modal = document.getElementById('finalizeConfirmationModal');
    if (event.target === modal) {
      hideFinalizeConfirmation();
    }
  }
</script>

<!-- Finalize Confirmation Modal -->
<div id="finalizeConfirmationModal" class="finalize-modal" style="display: none;">
  <div class="finalize-modal-content">
    <div class="finalize-modal-header">
      <h3><i class="ph ph-warning-circle"></i> Confirm Period Finalization</h3>
    </div>

    <div class="finalize-modal-body">
      <p><strong>Are you sure you want to finalize this payroll period?</strong></p>
      <p>Period ending: <span id="finalizePeriodInfo"></span></p>

      <div class="finalize-warning">
        <i class="ph ph-info"></i>
        <div>
          <strong>Important:</strong> Once finalized, this period cannot be modified without unfinalizing it first.
          The PAYE calculation will be locked and used for future YTD calculations.
        </div>
      </div>
    </div>

    <div class="finalize-modal-actions">
      <button type="button" class="finalize-cancel-btn" onclick="hideFinalizeConfirmation()">
        <i class="ph ph-x"></i> Cancel
      </button>
      <button type="button" class="finalize-confirm-btn" onclick="confirmFinalization()">
        <i class="ph ph-check-circle"></i> Yes, Finalize Period
      </button>
    </div>
  </div>
</div>

<style>
/* Finalize Confirmation Modal Styles */
.finalize-modal {
  display: none;
  position: fixed;
  z-index: 10000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  align-items: center;
  justify-content: center;
}

.finalize-modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.finalize-modal-header {
  padding: 24px 24px 16px;
  border-bottom: 1px solid #e5e7eb;
}

.finalize-modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 8px;
}

.finalize-modal-header h3 i {
  color: #f59e0b;
  font-size: 20px;
}

.finalize-modal-body {
  padding: 20px 24px;
}

.finalize-modal-body p {
  margin: 0 0 12px 0;
  color: #374151;
  line-height: 1.5;
}

.finalize-modal-body p:last-of-type {
  margin-bottom: 20px;
}

#finalizePeriodInfo {
  font-weight: 600;
  color: #1f2937;
}

.finalize-warning {
  background: #fef3c7;
  border: 1px solid #fbbf24;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.finalize-warning i {
  color: #f59e0b;
  font-size: 18px;
  margin-top: 2px;
  flex-shrink: 0;
}

.finalize-warning div {
  color: #92400e;
  font-size: 14px;
  line-height: 1.4;
}

.finalize-modal-actions {
  padding: 16px 24px 24px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  border-top: 1px solid #e5e7eb;
}

.finalize-cancel-btn,
.finalize-confirm-btn {
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  border: none;
}

.finalize-cancel-btn {
  background: #f3f4f6;
  color: #374151;
}

.finalize-cancel-btn:hover {
  background: #e5e7eb;
}

.finalize-confirm-btn {
  background: #059669;
  color: white;
}

.finalize-confirm-btn:hover {
  background: #047857;
}

/* Mobile responsiveness */
@media (max-width: 640px) {
  .finalize-modal-content {
    margin: 20px;
    width: calc(100% - 40px);
  }

  .finalize-modal-actions {
    flex-direction: column;
  }

  .finalize-cancel-btn,
  .finalize-confirm-btn {
    width: 100%;
    justify-content: center;
  }
}
</style>

<!-- SURGICAL FIX: Frontend validation for firstPayrollPeriodEndDate vs DOA -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Auto-refresh mechanism for period regeneration
  const urlParams = new URLSearchParams(window.location.search);
  const wasRegenerated = urlParams.get('periodsRegenerated');

  if (wasRegenerated === 'true') {
    // Remove the parameter from URL without refreshing
    const newUrl = window.location.pathname + (urlParams.toString().replace(/[?&]periodsRegenerated=true/, '') ? '?' + urlParams.toString().replace(/[?&]periodsRegenerated=true/, '') : '');
    window.history.replaceState({}, '', newUrl);

    // Show a brief notification that periods were updated
    if (typeof showToast === 'function') {
      showToast('success', 'Periods Updated', 'Payroll periods have been regenerated with correct dates');
    }
  }

  // Force update Calculator card display with current period data
  const currentPeriod = <%- JSON.stringify(currentPeriod) %>;
  if (currentPeriod) {
    const selectMonthButton = document.getElementById('selectMonthButton');
    if (selectMonthButton) {
      const periodDisplay = selectMonthButton.querySelector('.period-display');
      if (periodDisplay) {
        // Get the correct end date from current period
        const endDateBusiness = currentPeriod.endDateBusiness || moment.utc(currentPeriod.endDate).format('YYYY-MM-DD');
        const formattedDate = moment(endDateBusiness).format('DD/MM/YYYY');
        const status = currentPeriod.isFinalized ? 'Finalized' : 'Pending';
        const statusClass = currentPeriod.isFinalized ? 'completed' : 'pending';

        // Update the display immediately
        periodDisplay.innerHTML = `
          ${formattedDate}
          <span class="status-indicator ${statusClass}">
            ${status}
          </span>
        `;

        console.log('Calculator card updated with period:', {
          endDateBusiness: endDateBusiness,
          formattedDate: formattedDate,
          isFinalized: currentPeriod.isFinalized,
          configuredFirstPeriodEnd: employee.payFrequency?.firstPayrollPeriodEndDate,
          employeeDOA: employee.doa
        });
      }
    }
  }

  // Check if we need to validate period display
  const employee = <%- JSON.stringify(employee) %>;
  const payFrequency = employee.payFrequency;

  if (payFrequency && payFrequency.firstPayrollPeriodEndDate && payFrequency.frequency === 'monthly') {
    const configuredDate = new Date(payFrequency.firstPayrollPeriodEndDate);
    const doaDate = new Date(employee.doa);
    const shouldUseDOA = doaDate > configuredDate;

    if (!shouldUseDOA) {
      // The configured date should be used - add a visual indicator if periods seem incorrect
      const periodsList = document.getElementById('periodsList');
      if (periodsList) {
        const firstPeriodItem = periodsList.querySelector('.period-item:last-child');
        if (firstPeriodItem) {
          const periodEndDate = firstPeriodItem.getAttribute('data-period');
          const configuredEndFormatted = configuredDate.toISOString().split('T')[0];

          if (periodEndDate !== configuredEndFormatted) {
            // Add a notice that periods may need regeneration
            const notice = document.createElement('div');
            notice.style.cssText = `
              background: #fef3cd;
              border: 1px solid #ffc107;
              color: #856404;
              padding: 10px;
              margin: 10px 0;
              border-radius: 4px;
              font-size: 12px;
              text-align: center;
            `;
            notice.innerHTML = `
              <i class="fa fa-info-circle"></i>
              Periods may need regeneration to reflect updated pay frequency settings.
              <br><small>Expected first period: ${configuredDate.toLocaleDateString('en-ZA')}</small>
              <br><button onclick="regeneratePeriods()" style="margin-top: 5px; padding: 3px 8px; font-size: 11px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer;">
                Regenerate Periods
              </button>
            `;

            const modalContent = document.querySelector('#monthModal .modal-content');
            if (modalContent) {
              modalContent.insertBefore(notice, periodsList);
            }
          }
        }
      }
    }
  }
});

// Function to regenerate periods
function regeneratePeriods() {
  if (!confirm('This will regenerate unfinalized periods to use the correct pay frequency settings. Finalized periods will be preserved. Continue?')) {
    return;
  }

  const employeeId = '<%= employee._id %>';
  const companyCode = '<%= companyCode %>';

  // Show loading state
  const button = event.target;
  const originalText = button.innerHTML;
  button.innerHTML = 'Regenerating...';
  button.disabled = true;

  fetch(`/clients/${companyCode}/employeeManagement/${employeeId}/regenerate-periods`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      // Show success message
      showSuccessToast(data.message || 'Periods regenerated successfully!');

      // Reload the page to show updated periods
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } else {
      throw new Error(data.message || 'Failed to regenerate periods');
    }
  })
  .catch(error => {
    console.error('Error regenerating periods:', error);
    showErrorToast(error.message || 'Failed to regenerate periods');

    // Restore button
    button.innerHTML = originalText;
    button.disabled = false;
  });
}
</script>
