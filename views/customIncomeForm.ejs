<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= customIncomeItem.name %> - <%= employee.firstName %> <%= employee.lastName %></title>

    <!-- External Libraries -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Stylesheets -->
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/regularinputs.css" />
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/addBasicSalary.css" />
    <link rel="stylesheet" href="/css/edit-employee-details.css" />
    <link rel="stylesheet" href="/css/employeeManagement.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/mobile-header.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />
    <link rel="stylesheet" href="/css/mobile-employee-profile.css" />
    <link rel="stylesheet" href="/css/mobile-nav.css" />
    <link rel="stylesheet" href="/css/notifications.css" />

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        .form-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .form-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.03);
        }

        .form-header {
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .form-header h1 {
            color: #1e293b;
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
        }

        .form-header p {
            color: #64748b;
            margin: 0.5rem 0 0 0;
            font-size: 0.9rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #1e293b;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 1rem;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            font-family: 'Inter', sans-serif;
            box-sizing: border-box;
            transition: border-color 0.2s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #6366f1;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem;
            background: rgba(99, 102, 241, 0.02);
            border-radius: 10px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .checkbox-group:hover {
            background: rgba(99, 102, 241, 0.05);
            border-color: rgba(99, 102, 241, 0.2);
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin: 0;
        }

        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid #e2e8f0;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            border: none;
            font-size: 0.9rem;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: #6366f1;
            color: white;
        }

        .btn-primary:hover {
            background: #5855eb;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #f8fafc;
            color: #64748b;
            border: 1px solid #e2e8f0;
        }

        .btn-secondary:hover {
            background: #f1f5f9;
            color: #475569;
        }

        .input-type-info {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .input-type-info h3 {
            color: #0c4a6e;
            margin: 0 0 0.5rem 0;
            font-size: 1rem;
        }

        .input-type-info p {
            color: #0369a1;
            margin: 0;
            font-size: 0.9rem;
        }

        .multi-select {
            min-height: 150px;
        }

        .multi-select option {
            padding: 0.5rem;
            margin: 0.125rem 0;
        }

        .multi-select option:checked {
            background: #6366f1;
            color: white;
        }

        .help-text {
            font-size: 0.8rem;
            color: #64748b;
            margin-top: 0.25rem;
        }
    </style>
</head>
<body>
  <div class="layout-wrapper">
    <div class="content-wrapper">
      <%- include('partials/header', { user: user, company: company }) %>
      <main class="main-container">

    <div class="form-container">
        <div class="form-card">
            <div class="form-header">
                <h1><%= customIncomeItem.name %></h1>
                <p>Input Type: <%= customIncomeItem.inputType.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()) %></p>
            </div>

            <!-- Input Type Information -->
            <div class="input-type-info">
                <h3>About this income type</h3>
                <p id="inputTypeDescription"></p>
            </div>

            <form id="customIncomeForm" method="POST" action="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/custom-income/<%= customIncomeItem._id %>">
                <input type="hidden" name="_csrf" value="<%= csrfToken %>" />
                <input type="hidden" name="customIncomeId" value="<%= customIncomeItem._id %>" />
                <input type="hidden" name="inputType" value="<%= customIncomeItem.inputType %>" />
                <!-- Align period with addBasicSalary.ejs -->
                <input type="hidden" name="relevantDate" value="<%= (currentPeriod && currentPeriod.endDate) ? (currentPeriod.endDateBusiness || moment.utc(currentPeriod.endDate).format('YYYY-MM-DD')) : moment.utc().format('YYYY-MM-DD') %>" />

                <!-- Dynamic form fields based on inputType -->
                <div id="dynamicFields"></div>

                <div class="form-actions">
                    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/regularInputs" class="btn btn-secondary">
                        <i class="ph ph-arrow-left"></i>
                        Cancel
                    </a>

                    <% if (isEditMode) { %>
                        <button type="button" id="removeButton" class="btn btn-danger">
                            <i class="ph ph-trash"></i>
                            Remove
                        </button>
                    <% } %>

                    <button type="submit" class="btn btn-primary">
                        <i class="ph ph-check"></i>
                        Save Income Item
                    </button>
                </div>
            </form>
        </main>
      </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const inputType = '<%= customIncomeItem.inputType %>';
            const customIncomeItem = <%- JSON.stringify(customIncomeItem) %>;

            // Set input type description and render appropriate fields
            renderFormFields(inputType, customIncomeItem);

            <% if (isEditMode) { %>
            // Handle Remove button click
            const removeButton = document.getElementById('removeButton');
            if (removeButton) {
                removeButton.addEventListener('click', function() {
                    const itemName = '<%= customIncomeItem.name %>';
                    const confirmMessage = `Are you sure you want to remove "${itemName}" from this payroll period?\n\nThis will only remove it from the current payroll and won't delete the custom income configuration.`;

                    if (confirm(confirmMessage)) {
                        // Create and submit a DELETE request
                        const deleteUrl = `/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/custom-income/<%= customIncomeItem._id %>`;

                        fetch(deleteUrl, {
                            method: 'DELETE',
                            headers: {
                                'Content-Type': 'application/json',
                                'CSRF-Token': document.querySelector('input[name="_csrf"]').value,
                            }
                        })
                        .then(response => {
                            if (response.ok) {
                                return response.json();
                            } else {
                                throw new Error('Failed to remove item');
                            }
                        })
                        .then(result => {
                            if (result.success) {
                                // Redirect to employee profile page
                                window.location.href = `/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>`;
                            } else {
                                alert('Error: ' + (result.message || 'Failed to remove item'));
                            }
                        })
                        .catch(error => {
                            console.error('Error removing item:', error);
                            alert('Error removing item: ' + error.message);
                        });
                    }
                });
            }
            <% } %>
        });

        function renderFormFields(inputType, customIncomeItem) {
            const dynamicFields = document.getElementById('dynamicFields');
            const description = document.getElementById('inputTypeDescription');
            let fieldsHtml = '';
            let descriptionText = '';

            switch (inputType) {
                case 'fixedAmount':
                    descriptionText = 'Enter a fixed amount that will be added to the employee\'s income each pay period.';
                    fieldsHtml = `
                        <div class="form-group">
                            <label for="amount">Amount *</label>
                            <input type="number" id="amount" name="amount" step="0.01" min="0" required
                                   value="${customIncomeItem.amount || ''}" />
                        </div>
                        ${customIncomeItem.enableProRata ? `
                        <div class="form-group">
                            <div class="checkbox-group">
                                <input type="checkbox" id="enableProRata" name="enableProRata"
                                       ${customIncomeItem.enableProRata ? 'checked' : ''} />
                                <label for="enableProRata">Enable Pro-Rata</label>
                            </div>
                            <div class="help-text">Can't be changed if used on a finalised payslip.</div>
                        </div>
                        ` : ''}
                    `;
                    break;

                case 'amountPerEmployee':
                    descriptionText = 'You will enter a different amount for this employee each pay period.';
                    fieldsHtml = `
                        <div class="form-group">
                            <label for="amount">Amount for this period</label>
                            <input type="number" id="amount" name="amount" step="0.01" min="0" />
                            <div class="help-text">Enter the amount for this specific pay period.</div>
                        </div>
                        ${customIncomeItem.enableProRata ? `
                        <div class="form-group">
                            <div class="checkbox-group">
                                <input type="checkbox" id="enableProRata" name="enableProRata"
                                       ${customIncomeItem.enableProRata ? 'checked' : ''} />
                                <label for="enableProRata">Enable Pro-Rata</label>
                            </div>
                        </div>
                        ` : ''}
                    `;
                    break;

                case 'differentEveryPayslip':
                case 'onceOffSpecificPayslips':
                    descriptionText = inputType === 'differentEveryPayslip'
                        ? 'The amount will be different on every payslip. You\'ll enter the amount each time.'
                        : 'This is a once-off payment for specific payslips only.';
                    fieldsHtml = `
                        <div class="form-group">
                            <label for="amount">Amount for this period</label>
                            <input type="number" id="amount" name="amount" step="0.01" min="0"
                                   value="${customIncomeItem.amount || ''}" />
                            <div class="help-text">Enter the amount for this pay period.</div>
                        </div>
                    `;
                    break;

                case 'customRateQuantity':
                    descriptionText = 'You will be prompted on every payslip for the Quantity';
                    fieldsHtml = `
                        <div class="info-message" style="background: rgba(99, 102, 241, 0.02); border: 1px solid rgba(99, 102, 241, 0.1); border-radius: 12px; padding: 1.5rem; margin: 1rem 0; text-align: center;">
                            <i class="ph ph-info" style="font-size: 1.5rem; color: #6366f1; margin-bottom: 0.5rem;"></i>
                            <p style="margin: 0; color: #1e293b; font-weight: 500;">This custom income item has been configured.</p>
                            <p style="margin: 0.5rem 0 0 0; color: #64748b; font-size: 0.9rem;">You will be prompted on every payslip for the Quantity</p>
                        </div>
                    `;
                    break;

                case 'percentageOfIncome':
                    descriptionText = 'Calculate as a percentage of selected income items.';
                    const incomeOptions = [
                        'basicSalary', 'basicHourlyPay', 'overtime', 'shortTime', 'sundayPay',
                        'sundayOvertime', 'publicHolidayWorked', 'publicHolidayNotWorked',
                        'annualLeavePay', 'sickLeavePay', 'familyResponsibilityPay',
                        'annualLeavePayExtra', 'unpaidLeave', 'customRates'
                    ];
                    const incomeLabels = {
                        'basicSalary': 'Basic Salary',
                        'basicHourlyPay': 'Basic Hourly Pay',
                        'overtime': 'Overtime',
                        'shortTime': 'Short Time',
                        'sundayPay': 'Sunday Pay',
                        'sundayOvertime': 'Sunday Overtime',
                        'publicHolidayWorked': 'Public Holiday - Worked',
                        'publicHolidayNotWorked': 'Public Holiday - Not Worked',
                        'annualLeavePay': 'Annual Leave Pay',
                        'sickLeavePay': 'Sick Leave Pay',
                        'familyResponsibilityPay': 'Family Responsibility Pay',
                        'annualLeavePayExtra': 'Annual Leave Pay Extra',
                        'unpaidLeave': 'Unpaid Leave',
                        'customRates': 'Custom Rates'
                    };

                    fieldsHtml = `
                        <div class="form-group">
                            <label for="percentage">Percentage</label>
                            <input type="number" id="percentage" name="percentage" step="0.01" min="0" max="100"
                                   value="${customIncomeItem.percentage || ''}" />
                            <div class="help-text">Enter the percentage (e.g., 10 for 10%).</div>
                        </div>
                        <div class="form-group">
                            <label for="incomeItems">Select Income Items *</label>
                            <select id="incomeItems" name="incomeItems" multiple class="multi-select" required>
                                ${incomeOptions.map(option => `
                                    <option value="${option}" ${customIncomeItem.incomeItems && customIncomeItem.incomeItems.includes(option) ? 'selected' : ''}>
                                        ${incomeLabels[option]}
                                    </option>
                                `).join('')}
                            </select>
                            <div class="help-text">Hold Ctrl (Cmd on Mac) to select multiple items.</div>
                        </div>
                    `;
                    break;

                case 'monthlyForNonMonthly':
                    descriptionText = 'Apply a monthly amount for employees who are not paid monthly.';
                    fieldsHtml = `
                        <div class="form-group">
                            <label for="monthlyAmount">Monthly Amount *</label>
                            <input type="number" id="monthlyAmount" name="monthlyAmount" step="0.01" min="0" required
                                   value="${customIncomeItem.monthlyAmount || ''}" />
                        </div>
                    `;
                    break;

                default:
                    descriptionText = 'Unknown input type.';
                    fieldsHtml = '<p>Error: Unknown input type.</p>';
            }

            description.textContent = descriptionText;
            dynamicFields.innerHTML = fieldsHtml;
        }

        function toggleCustomRateField() {
            const checkbox = document.getElementById('differentRateForEveryEmployee');
            const customRateGroup = document.getElementById('customRateGroup');

            if (checkbox.checked) {
                customRateGroup.style.display = 'none';
                document.getElementById('customRate').removeAttribute('required');
            } else {
                customRateGroup.style.display = 'block';
            }
        }

        // Enhanced form submission with loading overlay and success animation
        document.getElementById('customIncomeForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            // Prevent multiple submissions
            if (this.dataset.submitting === 'true') {
                return;
            }
            this.dataset.submitting = 'true';

            console.log('=== CUSTOM INCOME FORM SUBMISSION DEBUG ===');
            console.log('Form element:', this);
            console.log('Form action URL:', this.action);

            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;

            // Show loading state with overlay
            showLoadingState(submitButton);
            createLoadingOverlay();

            const formData = new FormData(this);
            const data = {};

            // Convert FormData to object
            console.log('Converting FormData to object...');
            for (let [key, value] of formData.entries()) {
                console.log(`FormData entry: ${key} = ${value}`);
                if (key === 'incomeItems') {
                    if (!data[key]) data[key] = [];
                    data[key].push(value);
                } else {
                    data[key] = value;
                }
            }

            console.log('Prepared data object:', data);

            // Prepare request details
            const csrfToken = document.querySelector('input[name="_csrf"]').value;
            console.log('CSRF Token:', csrfToken);

            const requestBody = JSON.stringify(data);
            console.log('JSON stringified body:', requestBody);
            console.log('Request body length:', requestBody.length);

            const requestHeaders = {
                'Content-Type': 'application/json',
                'CSRF-Token': csrfToken,
            };
            console.log('Request headers:', requestHeaders);

            const requestOptions = {
                method: 'POST',
                headers: requestHeaders,
                body: requestBody
            };
            console.log('Full request options:', requestOptions);

            // Submit form
            console.log('Sending fetch request to:', this.action);
            fetch(this.action, requestOptions)
            .then(response => {
                console.log('=== RESPONSE RECEIVED ===');
                console.log('Response status:', response.status);
                console.log('Response statusText:', response.statusText);
                console.log('Response headers:', response.headers);
                console.log('Response ok:', response.ok);
                console.log('Response type:', response.type);
                console.log('Response url:', response.url);

                // Clone the response to read it multiple times
                const responseClone = response.clone();

                // First, let's see what the raw response text looks like
                return responseClone.text().then(responseText => {
                    console.log('Raw response text:', responseText);
                    console.log('Response text length:', responseText.length);
                    console.log('Response starts with:', responseText.substring(0, 100));

                    // Check if response looks like HTML
                    if (responseText.trim().startsWith('<!DOCTYPE') || responseText.trim().startsWith('<html')) {
                        console.error('ERROR: Server returned HTML instead of JSON!');
                        console.error('Full HTML response:', responseText);
                        throw new Error('Server returned HTML instead of JSON. Check server logs for errors.');
                    }

                    // Try to parse as JSON
                    try {
                        const jsonData = JSON.parse(responseText);
                        console.log('Successfully parsed JSON:', jsonData);
                        return jsonData;
                    } catch (parseError) {
                        console.error('JSON Parse Error:', parseError);
                        console.error('Failed to parse response as JSON:', responseText);
                        throw new Error(`JSON Parse Error: ${parseError.message}. Response: ${responseText.substring(0, 200)}...`);
                    }
                });
            })
            .then(result => {
                console.log('=== PROCESSING RESULT ===');
                console.log('Parsed result:', result);

                if (result.success) {
                    console.log('Success! Processing redirect...');

                    // Show success state
                    showSuccessState(submitButton);
                    showToast(result.message || 'Custom income saved successfully!', 'success');

                    // Use the redirectUrl from the server response if provided
                    const redirectUrl = result.redirectUrl || `/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/regularInputs`;
                    console.log('Redirecting to:', redirectUrl);

                    // Redirect after delay to show success animation
                    setTimeout(() => {
                        window.location.href = redirectUrl;
                    }, 1500);
                } else {
                    console.error('Server returned error:', result.message);
                    showToast(result.message || 'Failed to save income item', 'error');
                    hideLoadingState(submitButton, originalText);
                    this.dataset.submitting = 'false';
                }
            })
            .catch(error => {
                console.error('=== FETCH ERROR ===');
                console.error('Error type:', error.constructor.name);
                console.error('Error message:', error.message);
                console.error('Error stack:', error.stack);
                console.error('Full error object:', error);

                showToast('Error saving income item: ' + error.message, 'error');
                hideLoadingState(submitButton, originalText);
                this.dataset.submitting = 'false';
            });
        });

        // Loading and success animation functions
        function showLoadingState(submitButton) {
            submitButton.disabled = true;
            submitButton.classList.add('loading');
            submitButton.innerHTML = '<span class="button-content" style="opacity: 0;"><i class="ph ph-check"></i> Save Income Item</span>';
        }

        function showSuccessState(submitButton) {
            const loadingOverlay = document.querySelector('.form-loading-overlay');
            if (loadingOverlay) {
                const loadingContent = loadingOverlay.querySelector('.loading-content');
                if (loadingContent) {
                    loadingContent.innerHTML = `
                        <div style="color: #10b981; font-size: 48px; margin-bottom: 1rem;">
                            <i class="ph ph-check-circle"></i>
                        </div>
                        <div class="loading-text" style="color: #10b981;">Success!</div>
                        <div class="loading-subtext">Custom income saved successfully</div>
                    `;
                }
            }

            submitButton.classList.remove('loading');
            submitButton.innerHTML = '<i class="ph ph-check"></i> Saved Successfully';
            submitButton.style.backgroundColor = '#10b981';
        }

        function hideLoadingState(submitButton, originalText) {
            const loadingOverlay = document.querySelector('.form-loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.classList.remove('show');
                setTimeout(() => loadingOverlay.remove(), 300);
            }

            submitButton.disabled = false;
            submitButton.classList.remove('loading');
            submitButton.innerHTML = originalText;
            submitButton.style.backgroundColor = '';
        }

        function createLoadingOverlay() {
            const existingOverlay = document.querySelector('.form-loading-overlay');
            if (existingOverlay) {
                existingOverlay.remove();
            }

            const overlay = document.createElement('div');
            overlay.className = 'form-loading-overlay';
            overlay.innerHTML = `
                <div class="loading-content">
                    <div class="loading-spinner"></div>
                    <div class="loading-text">Saving Custom Income...</div>
                    <div class="loading-subtext">Please don't refresh or navigate away</div>
                </div>
            `;

            document.body.appendChild(overlay);
            setTimeout(() => overlay.classList.add('show'), 10);
        }

        function showToast(message, type = 'info') {
            let toastContainer = document.querySelector('.toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.className = 'toast-container';
                document.body.appendChild(toastContainer);
            }

            const icons = {
                success: 'ph-check-circle',
                error: 'ph-x-circle',
                info: 'ph-info',
                warning: 'ph-warning'
            };

            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.innerHTML = `
                <i class="ph ${icons[type] || icons.info}"></i>
                <span>${message}</span>
            `;

            toastContainer.appendChild(toast);
            setTimeout(() => toast.classList.add('show'), 10);

            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    toast.remove();
                    if (toastContainer.children.length === 0) {
                        toastContainer.remove();
                    }
                }, 300);
            }, 4000);
        }
    </script>
</body>
</html>
