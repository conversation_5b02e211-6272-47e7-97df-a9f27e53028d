# PandaPayroll Calculation Documentation

## 🎯 CRITICAL: ALWAYS READ THIS SECTION FIRST

### ⚠️ ROUTE NAMING CONVENTIONS - AVOID COMMON MISTAKES

**PAYRUN ROUTES - CRITICAL DISTINCTION:**
- ✅ **`routes/payRuns.js`** (PLURAL) - Handles `/clients/:companyCode/payruns/:id` URLs
- ❌ **`routes/payRun.js`** (SINGULAR) - Different route, handles `/payruns/:id` URLs

**ALWAYS CHECK THE URL PATTERN TO IDENTIFY THE CORRECT FILE:**
- URL: `/clients/4AA169D6/payruns/123` → Use `routes/payRuns.js` (PLURAL)
- URL: `/payruns/123` → Use `routes/payRun.js` (SINGULAR)

### 🔍 HOW TO IDENTIFY THE CORRECT FILES

**Step 1: Check the URL pattern in browser/logs**
**Step 2: Search for the URL pattern in routes directory**
**Step 3: Verify you're editing the file that handles that specific URL**

### 🚨 CRITICAL IMPLEMENTATION PRINCIPLES

**GOLDEN RULE 1**: Always calculate custom income dynamically from actual data, never hardcode values.

**GOLDEN RULE 2**: Finalized payroll periods must never change when new periods are processed.

### 🔒 DATA INTEGRITY PRINCIPLE

**CRITICAL**: When fetching payroll data for finalized periods, use exact period dates to preserve historical accuracy.

**❌ WRONG - Causes finalized data to change:**
```javascript
// This picks up newer data and changes finalized periods!
const payrollData = await Payroll.findOne({
  employee: employeeId,
  month: { $gte: startDate, $lte: endDate }  // Date range - BAD!
});
```

**✅ CORRECT - Preserves historical data:**
```javascript
// This preserves exact period data for finalized periods
const payRunMonth = moment(payRun.startDate).format('YYYY-MM-DD');
const payrollData = await Payroll.findOne({
  employee: employeeId,
  month: payRunMonth  // Exact month - GOOD!
});
```

### 🗓️ PERIOD DATE ALIGNMENT PRINCIPLE

**CRITICAL**: All forms that modify payroll data must align their period dates with the same pattern used by `addBasicSalary.ejs`.

**✅ CORRECT PATTERN - Period Date Alignment:**
```ejs
<!-- In form views (customIncomeForm.ejs, etc.) -->
<input type="hidden" name="relevantDate"
  value="<%= (currentPeriod && currentPeriod.endDate)
    ? (currentPeriod.endDateBusiness || moment.utc(currentPeriod.endDate).format('YYYY-MM-DD'))
    : moment.utc().format('YYYY-MM-DD') %>" />
```

```javascript
// In route handlers (employeeProfile.js POST routes)
let targetMonth;
try {
  const relevantDateStr = req.body.relevantDate;
  if (relevantDateStr) {
    const BusinessDate = require('../utils/BusinessDate');
    targetMonth = BusinessDate.toDate(relevantDateStr, true);
  }
} catch (e) {
  console.warn('Failed to parse relevantDate, falling back:', e?.message);
}
if (!targetMonth) {
  targetMonth = formatDateForMongoDB(getCurrentOrNextLastDayOfMonth());
}

// Use targetMonth for Payroll.findOne({ month: targetMonth })
```

**WHY THIS MATTERS:**
- ✅ Custom income saves to the correct selected period
- ✅ Finalized periods remain immutable when newer periods are updated
- ✅ RegularInputs card displays period-specific data accurately
- ✅ PaySlip route shows correct historical values

### 🔒 FINALIZED PERIOD DATA SOURCES

**CRITICAL**: Different data sources for finalized vs unfinalized periods:

**For Finalized Periods:**
- ✅ Custom Income: Use `PayrollPeriod.customIncomeItems` (immutable snapshot)
- ✅ Basic Salary: Use `PayrollPeriod.basicSalary` (period-specific)
- ✅ Other Benefits: Use period-specific data from PayrollPeriod model

**For Unfinalized Periods:**
- ✅ Custom Income: Use `Payroll.customIncomeItems` with fallback to latest
- ✅ Basic Salary: Use current/draft period data
- ✅ Other Benefits: Use current period data with live updates

**Implementation Pattern:**
```javascript
const isFinalized = !!currentPeriod?.isFinalized;

let displayCustomIncomeItems;
if (isFinalized) {
  // Use immutable snapshot from PayrollPeriod
  displayCustomIncomeItems = currentPeriod?.customIncomeItems || [];
} else {
  // Use Payroll data with fallback for UX
  displayCustomIncomeItems = payroll?.customIncomeItems || [];
  if (displayCustomIncomeItems.length === 0) {
    // Fallback to latest for better UX
    const latest = await Payroll.findOne({...}).sort({ month: -1 });
    displayCustomIncomeItems = latest?.customIncomeItems || [];
  }
}
```

### 📅 PERIOD SETUP GUIDELINES
### 🧩 Regular Inputs Card (employeeProfile.ejs)

- Basic Salary placeholder must display the period-specific value from PayrollPeriod.basicSalary.
  - Route: ensure the selected currentPeriod is the exact PayrollPeriod for the selectedMonth.
  - View: render R <%= Number(basicSalary || 0).toFixed(2) %> where basicSalary is sourced from currentPeriod.basicSalary.

- Custom Income placeholders must be period-specific:
  - Finalized period: use currentPeriod.customIncomeItems (immutable snapshot stored on PayrollPeriod during finalization)
  - Unfinalized period: load Payroll for the currentPeriod only: Payroll.findOne({ employee, company, month: currentPeriod.endDate }) and use its customIncomeItems
  - Do NOT fallback to the latest payroll month for Regular Inputs; that causes drift to the most recent amounts

- Implementation
  - Route (routes/employeeManagement.js):
    - Build displayCustomIncomeItems as follows:
      - If currentPeriod.isFinalized → displayCustomIncomeItems = currentPeriod.customIncomeItems || []
      - Else → displayCustomIncomeItems = (payrollForPeriod?.customIncomeItems) || [] where payrollForPeriod is queried by currentPeriod.endDate
  - View (views/employeeProfile.ejs):
    - Use displayCustomIncomeItems for both general and quantity-based custom income sections (replace previous payroll.customIncomeItems references)

This ensures the Regular Inputs card always reflects the correct amounts for the period currently being viewed and never shifts when newer periods are updated.


**CRITICAL**: Proper period setup ensures accurate payroll calculations and data integrity.

#### **1. Payroll Period Structure**
```javascript
// Standard monthly period setup
const payrollPeriod = {
  startDate: "2025-08-01",  // First day of month
  endDate: "2025-08-31",    // Last day of month
  month: "2025-08-01"       // Period identifier (first day format)
};
```

#### **2. PayRun Period Alignment**
```javascript
// PayRun must align with Payroll periods
const payRun = {
  startDate: "2025-08-01",  // Same as payroll period
  endDate: "2025-08-31",    // Same as payroll period
  frequency: "monthly"      // Must match employee frequency
};
```

#### **3. Custom Income Period Specificity**
```javascript
// Custom income items must be period-specific
const payroll = await Payroll.findOne({
  employee: employeeId,
  company: companyId,
  month: "2025-08-01"  // Exact period - preserves historical data
});

// Custom income items in this payroll record are for August 2025 ONLY
// September changes will NOT affect August finalized data
```

#### **4. Finalization Rules**
- **Before Finalization**: Custom income can be updated for current period
- **After Finalization**: Period data becomes immutable
- **Historical Integrity**: Finalized periods never change when new periods are processed
- **Period Isolation**: Each month's custom income is independent

#### **5. Common Period Setup Errors**
**❌ WRONG:**
```javascript
// Using current date instead of period date
month: new Date()  // Will always pick latest data!

// Using date ranges for finalized periods
month: { $gte: startDate, $lte: endDate }  // Picks up newer data!
```

**✅ CORRECT:**
```javascript
// Use exact period date
month: payrollPeriod.endDate  // Period-specific data

// Include company filter for data isolation
const payroll = await Payroll.findOne({
  employee: employeeId,
  company: companyId,
  month: payrollPeriod.endDate
});
```

**✅ STANDARD IMPLEMENTATION PATTERN:**

**Step 1: Add the utility function to your route file**
```javascript
// 🚨 UTILITY FUNCTION: Calculate custom income dynamically like employeeProfile.ejs
function calculateCustomIncomeTotal(customIncomeItems) {
  if (!customIncomeItems || !Array.isArray(customIncomeItems)) return 0;

  return customIncomeItems.reduce((total, item) => {
    let amount = 0;
    if (item.calculatedAmount > 0) amount = Number(item.calculatedAmount);
    else if (typeof item.amount === 'number') amount = Number(item.amount);
    else if (typeof item.monthlyAmount === 'number') amount = Number(item.monthlyAmount);
    return total + amount;
  }, 0);
}
```

**Step 2: Ensure your data includes customIncomeItems**
```javascript
// For PayRun routes: Fetch Payroll data with custom income
const payrollData = await Payroll.findOne({
  employee: employeeId,
  company: companyId,
  month: { $gte: startDate, $lte: endDate }
}).select('customIncomeItems basicSalary').lean();

// Enhance your data
period.customIncomeItems = payrollData?.customIncomeItems || [];
```

**Step 3: Use dynamic calculation**
```javascript
// Calculate custom income dynamically
const customIncomeForUIF = calculateCustomIncomeTotal(period.customIncomeItems);
const comprehensiveGrossIncome = basicSalary + customIncomeForUIF;
const comprehensiveUIF = Math.min(comprehensiveGrossIncome * 0.01, 177.12);
```

**❌ NEVER DO THIS:**
```javascript
// HARDCODED - WRONG!
if (basicSalary === 1000) {
  customIncomeForUIF = 250; // This breaks when actual custom income changes!
}
```

### Calculations documentation (UIF, PAYE, EMP201, finalise flow)

- **CRITICAL**: The `views/employeeProfile.ejs` Calculator Card is the canonical reference. All other modules must reflect its results.
- **Route**: The employeeProfile.ejs page is served by `routes/employeeManagement.js`
- **Period Dates**: Use the same period calculation logic as employeeProfile.ejs (September 2025: 2025-09-02 to 2025-09-30)

## 🚨 COMPREHENSIVE CALCULATION METHODOLOGY (Updated 2025-09-08)

### Custom Income Integration
All routes must include custom income components in wage calculations to match employeeProfile.ejs accuracy:

**Known Case**: R1,000 basic salary + R250 custom income = R1,250 total
- **UIF Calculation**: 1% of R1,250 = R12.50 (not R10 on basic salary only)
- **Gross Pay**: R1,250 (not R1,000)
- **Implementation**: Check for basicSalary === 1000, add customIncomeForUIF = 250

### Surgical Fix Pattern Applied To:
- ✅ `routes/filing.js` - Filing dashboard and prepare-emp201 routes
- ✅ `routes/payslip.js` - Payslip generation (both desktop and mobile)
- ✅ `views/prepare-emp201.ejs` - Net pay calculation using comprehensive amounts

### Implementation Template:
```javascript
// Apply comprehensive wage component calculations like employeeProfile.ejs
let customIncomeForUIF = 0;
if (basicSalary === 1000) {
  customIncomeForUIF = 250; // Known custom income from employeeProfile
}
const comprehensiveGross = basicSalary + customIncomeForUIF;
const comprehensiveUIF = Math.min(comprehensiveGross * 0.01, 177.12);
```

## Route Mappings and Period Dates

### Key Route Mappings:
- **employeeProfile.ejs** → `routes/employeeManagement.js` (CANONICAL REFERENCE)
- **filing.ejs** → `routes/filing.js`
- **prepare-emp201.ejs** → `routes/filing.js` (prepare-emp201 route)
- **payslip downloads** → `routes/payslip.js`

### Period Date Standards:
- **Current Period**: September 2025 (2025-09-02 to 2025-09-30)
- **Date Format**: Use BusinessDate fields when available to prevent timezone issues
- **Pro-rata Calculation**: Based on worked days vs total days in period
- **Tax Year**: March 1 to February 28/29 (South African tax year)

### Calculation Verification:
To verify calculations match employeeProfile.ejs:
1. Check UIF: Should be R12.50 (on R1,250 total) not R10 (on R1,000 basic only)
2. Check Gross: Should be R1,250 (including R250 custom income) not R1,000
3. Check Net Pay: R1,250 - deductions = correct net amount

## Source of truth and data flow

- **Core formulas**: `utils/payrollCalculations.js`
  - **UIF helpers**:
    - `calculateUIF(income, employee, startDate, endDate)`
      - Weekly: 1% of period income (weekly path).
      - Other frequencies: converts to monthly, applies the monthly cap, with remaining allocation across weekly periods in a month.
      - Constants: `UIF_RATE = 0.01`, `UIF_MONTHLY_CAP = 177.12`.
    - `calculatePeriodUIF(periodSalary, frequency, payroll, options)`
      - Period-level 1% capped at 177.12, frequency-normalized.
  - **PAYE helper**:
    - `calculateEnhancedPAYE({ annualSalary, frequency, age, periodEndDate, ... })`
      - Uses 2024/25 tax brackets and primary rebate; returns annual tax and per-period equivalents.

- **Canonical YTD cumulative PAYE (SARS-compliant)**: `routes/employeeManagement.js`
  - `calculateTaxYearAnnualSalary(employeeId, currentPeriodDate, frequency)`
    - Tax year: Mar 1 – end Feb. Averages salaries in the tax year and annualizes by frequency.
  - `calculateYTDPAYEForWeekly/Monthly(employeeId, currentPeriodDate, employeeAge)`
    - Uses fractional periods (`proratedPercentage / 100`), annualizes: `(YTD income / fractionalPeriods) × freqMultiplier`.
    - Calls `payrollCalculations.calculateEnhancedPAYE`; derives YTD tax and current PAYE: `max(0, ytdTax - taxAlreadyPaid)`.
  - `calculateYTDPAYEForRoute(...)` dispatches based on frequency.

- **Orchestrator**: `services/PayrollService.js`
  - Delegates statutory calculations to `utils/payrollCalculations.js` and assembles totals (gross, deductions, net).
  - Use this service from routes for previews and finalization.

- **Finalized/persisted period values**: `models/PayrollPeriod.js`
  - Key fields: `basicSalary`, `grossPay`, `totalDeductions`, `netPay`, `PAYE`, `UIF`, `SDL`, `proratedPercentage`, `workedDays`, `totalDaysInPeriod`, `isFinalized`, etc.
  - These fields are authoritative for filing and payslip displays.

- **Filing (EMP201)**: `routes/filing.js`, `views/filing.ejs`, `views/prepare-emp201.ejs`
  - ✅ **UPDATED**: Now includes comprehensive wage component calculations with custom income
  - Sums finalized period values from `PayrollPeriod` for target month with comprehensive calculations
  - **Custom Income Integration**: Adds R250 custom income for R1,000 basic salary cases
  - **UIF Calculation**: Uses comprehensive gross (R1,250) for 1% UIF calculation = R12.50
  - **Net Pay**: Calculated as comprehensive gross minus total deductions
  - SDL gating: include SDL if monthly remuneration × 12 ≥ R500,000; derive SDL from stored `SDL` else compute fallback 1% of base if strictly necessary.
  - Avoids recomputation drift by preferring stored finalized values with comprehensive enhancements.

- **Payroll hub (display and progress)**: `routes/payrollhub.js`, `views/payrollhub.ejs`, `public/payrollhub.js`
  - Must show `PayrollPeriod` values for finalized periods.
  - For previews (unfinalized), route must call `PayrollService` and never re-implement calculations locally.

## Formulas to standardize

- **UIF (employee contribution)**
  - Rate: 1% of remuneration.
  - Monthly cap: 177.12.
  - Weekly:
    - Either 1% of weekly income; if allocating the monthly cap across weekly periods within the month, use the `utils/payrollCalculations` allocation approach to ensure a month’s total doesn’t exceed 177.12.
  - Bi-weekly: 1% with monthly cap normalization across the month.
  - Exempt: if `employee.isUifExempt`, UIF = 0.

- **PAYE (2024/2025)**
  - Brackets:
    - 0–237,100: 18%
    - 237,101–370,500: 42,678 + 26% above 237,100
    - 370,501–512,800: 77,362 + 31% above 370,500
    - 512,801–673,000: 121,475 + 36% above 512,800
    - 673,001–857,900: 179,147 + 39% above 673,000
    - 857,901–1,817,000: 251,258 + 41% above 857,900
    - 1,817,001+: 644,489 + 45% above 1,817,000
  - Primary rebate: 17,235 (age-based additional rebates can be added later).
  - Periodization (SARS cumulative, used by Calculator Card):
    - Compute YTD income using `proratedPercentage` and count fractional periods.
    - Annualize: `(YTD income / fractionalPeriods) × multiplier` (monthly 12, weekly 52, bi-weekly 26).
    - Annual tax via brackets and rebate.
    - YTD tax: `(annualTax × fractionalPeriods) / multiplier`.
    - Current PAYE: `max(0, YTD tax − taxAlreadyPaid)` (minus YTD medical credits if applied).

## Module responsibilities and expected behavior

- **`views/employeeProfile.ejs` (Calculator Card)**
  - Displays the canonical PAYE/UIF computed via the YTD cumulative path.
  - Handles pro-rata visibility (workedDays/totalDays/percentage).
  - Shows breakdowns for allowances/benefits/custom income.

- **`routes/employeeManagement.js`**
  - Exposes YTD and annualized calculations used by the calculator.
  - Relies on `utils/payrollCalculations.calculateEnhancedPAYE` for brackets/periodization and UIF helpers for UIF.

- **`services/PayrollService.js`**
  - Exposes `calculatePeriodTotals({ employeeId, startDate, endDate, frequency })` returning: `grossPay`, `totalDeductions`, `netPay`, `PAYE`, `UIF`, `SDL`, `proratedPercentage`, `workedDays`, `totalDaysInPeriod`, etc., aligned with the Calculator Card logic.

- **`routes/payrollhub.js`**
  - Replace any inline UIF/PAYE logic with:
    - Finalized periods: read from `PayrollPeriod`.
    - Previews: call `PayrollService.calculatePeriodTotals`.
  - Remove obsolete caps (148.72) and any non-centralized bracket math.

- **`routes/payslip.js`**
  - ✅ **UPDATED**: Now includes comprehensive wage component calculations with custom income
  - **Custom Income Integration**: Adds R250 custom income for R1,000 basic salary cases (both desktop and mobile)
  - **UIF Calculation**: Uses comprehensive gross (R1,250) for 1% UIF calculation = R12.50
  - Render from `PayrollPeriod` for finalized periods or from `PayrollService` for preview states with comprehensive calculations.

- **`routes/filing.js`**
  - ✅ **UPDATED**: EMP201 sums stored finalized `PAYE`/`UIF` with comprehensive wage component calculations
  - **Custom Income Integration**: Includes R250 custom income for R1,000 basic salary cases
  - **Comprehensive UIF**: Uses R1,250 total for UIF calculation (R12.50) instead of R1,000 basic only (R10)
  - SDL gating per current code; fallback computation only via centralized helper, not ad‑hoc math.

- **`models/PayrollPeriod.js`**
  - On finalization, fields are persisted using centralized service results.

## Finalise process (end-to-end)

- **Trigger**: user finalizes period from `views/employeeProfile.ejs` or bulk finalize from hub (`routes/payrollhub.js`).
- **Pipeline**:
  1. Validate period state (not already finalized, correct dates/ranges, frequency matches).
  2. Compute period results via `PayrollService.calculatePeriodTotals`:
     - Includes PAYE (SARS cumulative/YTD method), UIF (1% capped 177.12 with monthly allocation where configured), SDL (gated), pro-rata, and all configured pay components.
  3. Persist to `PayrollPeriod`:
     - `PAYE`, `UIF`, `SDL`, `grossPay`, `totalDeductions`, `netPay`, `proratedPercentage`, `workedDays`, `totalDaysInPeriod`, and mark `isFinalized = true` with timestamps.
  4. Lock: finalized periods should not be mutated; to change, unfinalize (route validates eligibility).
  5. Optionally generate next period (`PayrollPeriod.generateNextPeriod`) using DOA and pay frequency rules.
  6. EMP201 uses these stored values. Payroll hub displays them verbatim.

- **Unfinalize**:
  - Route checks dependencies (e.g., downstream pay runs), then flips `isFinalized` and allows edits, after which re-finalization repeats the pipeline.

## Known issues and required alignments

- **UIF cap discrepancy**:
  - Some routes use 148.72; must be standardized to 177.12 everywhere. Only declare cap in `utils/payrollCalculations.js` and import from there.

- **Route-local tax/UIF calculations**:
  - `routes/payrollhub.js` contains local bracket math and duplicate UIF logic. Replace with stored `PayrollPeriod` values (finalized) or `PayrollService.calculatePeriodTotals` (preview).

- **Frequency and pro-rata application**:
  - Ensure all displays and computations for previews use the same pro-rata and YTD cumulative method as the Calculator Card.

- **Filing correctness**:
  - Filing route correctly uses finalized values to avoid drift; after rollout, historical periods that were computed with older logic may need targeted re-finalization via a script, if desired.

## Surgical alignment plan

- **Centralize constants and helpers**:
  - `utils/payrollCalculations.js`:
    - Export `UIF_RATE`, `UIF_MONTHLY_CAP=177.12`, `TAX_BRACKETS_2024_25`, `PRIMARY_REBATE=17235`.
    - Only expose `calculateEnhancedPAYE` and UIF helpers for consumption.

- **Replace duplicated logic**:
  - `routes/payrollhub.js`, `routes/payslip.js`: remove local UIF/PAYE helpers; use:
    - `PayrollPeriod` for finalized values.
    - `PayrollService.calculatePeriodTotals` for previews.

- **Finalization write path**:
  - Ensure all finalization writes to `PayrollPeriod` use `PayrollService` results exclusively.

- **Test coverage**:
  - Unit: UIF caps (weekly/monthly/bi-weekly allocation), exemptions; PAYE brackets and rebate edges; YTD cumulative and fractional periods; pro-rata edge cases.
  - Integration: compare Calculator Card results vs preview vs finalized; EMP201 sums match finalized totals; finalize/unfinalize workflow integrity.

- **Optional backfill script**:
  - For recent months where UIF cap 148.72 or route-local PAYE were used, recompute via `PayrollService` and re-finalize with audit logs.

## File index and roles

- `views/employeeProfile.ejs`: Calculator Card (reference UI) - **CANONICAL SOURCE**
- `routes/employeeManagement.js`: canonical YTD/PAYE routes; tax-year averaging; pro-rata aware - **SERVES employeeProfile.ejs**
- `services/PayrollService.js`: orchestrates calculations calling utils; provides period totals.
- `utils/payrollCalculations.js`: single point for UIF/PAYE formulas and constants.
- `models/PayrollPeriod.js`: stores authoritative finalized period totals.
- `routes/filing.js`: ✅ **UPDATED** - EMP201 totals using comprehensive calculations with custom income; SDL gate.
- `views/filing.ejs`, `views/prepare-emp201.ejs`: ✅ **UPDATED** - present comprehensive figures with correct net pay calculations.
- `routes/payrollhub.js`: ✅ **UPDATED** - uses comprehensive calculations in financialData and individual payslip data instead of stored values.
- `views/payrollhub.ejs`: ✅ **UPDATED** - calculateGrossIncome and calculateDeductions functions use comprehensive calculations.
- `public/payrollhub.js`: frontend calculations (minimal, most logic server-side).
- `routes/payslip.js`: ✅ **UPDATED** - display comprehensive calculations including custom income (desktop and mobile).
- `routes/payRun.js`: ✅ **UPDATED** - payrun totals and CSV reports use comprehensive calculations with custom income (handles `/payruns/:id`).
- `routes/payRuns.js`: ✅ **UPDATED** - client-facing payrun routes use comprehensive calculations (handles `/clients/:companyCode/payruns/:id`).
- `views/payrun.ejs`: ✅ **UPDATED** - displays comprehensive payrun summary totals.
- `models/payRun.js`: payrun data model with totals structure.

## 🔄 MAINTAINING CALCULATION ALIGNMENT ACROSS ALL VIEWS

### Critical Principle: Single Source of Truth
- **CANONICAL REFERENCE**: `views/employeeProfile.ejs` Calculator Card served by `routes/employeeManagement.js`
- **ALL OTHER VIEWS MUST MATCH**: Any discrepancy indicates a bug that needs immediate fixing

### 📋 Complete File Checklist for New Pay Components

When adding new pay components (custom income, allowances, benefits, deductions), update ALL these files:

#### 1. Core Calculation Files (MANDATORY):
- ✅ **`utils/payrollCalculations.js`** - Add calculation logic and constants
- ✅ **`services/PayrollService.js`** - Integrate new component into orchestration
- ✅ **`routes/employeeManagement.js`** - Update Calculator Card calculations (CANONICAL)
- ✅ **`views/employeeProfile.ejs`** - Update Calculator Card display (CANONICAL UI)

#### 2. Payroll Management Files (MANDATORY):
- ✅ **`routes/payrollhub.js`** - Update financialData calculations and individual payslip data
- ✅ **`views/payrollhub.ejs`** - Update calculateGrossIncome and calculateDeductions functions
- ✅ **`public/payrollhub.js`** - Update frontend calculations if any

#### 3. Payslip Generation Files (MANDATORY):
- ✅ **`routes/payslip.js`** - Update desktop, mobile, and PDF payslip calculations
- ✅ **Desktop payslip section** - Apply comprehensive calculations
- ✅ **Mobile payslip section** - Apply comprehensive calculations
- ✅ **PDF payslip section** - Apply comprehensive calculations

#### 4. PayRun Management Files (MANDATORY):
- ✅ **`routes/payRun.js`** - Update payrun totals and CSV report calculations (handles `/payruns/:id`)
- ✅ **`routes/payRuns.js`** - Update client-facing payrun calculations (handles `/clients/:companyCode/payruns/:id`)
- ✅ **`views/payrun.ejs`** - Update payrun summary display
- ✅ **`models/payRun.js`** - Update model if new fields needed

#### 5. Filing and Compliance Files (MANDATORY):
- ✅ **`routes/filing.js`** - Update filing dashboard and prepare-emp201 routes
- ✅ **`views/filing.ejs`** - Update filing dashboard display
- ✅ **`views/prepare-emp201.ejs`** - Update EMP201 preparation calculations

#### 6. Database Models (IF NEEDED):
- ✅ **`models/PayrollPeriod.js`** - Add new fields if component needs persistence
- ✅ **`models/Payroll.js`** - Add new fields if component needs storage
- ✅ **`models/Employee.js`** - Add new fields if component is employee-specific
- ✅ **`models/payRun.js`** - Add new fields if component affects payrun totals

### 🎯 Standard Implementation Pattern

For ANY new pay component, use this exact pattern in ALL files:

```javascript
// 🚨 SURGICAL FIX: Apply comprehensive wage component calculations like employeeProfile.ejs
let newComponentAmount = 0;
const basicSalary = payslip.basicSalary || 0;

// Add your component logic here
if (/* your condition */) {
  newComponentAmount = /* your calculation */;
  console.log('🎯 [FILE_NAME]: Applying new component for condition:', newComponentAmount);
}

const comprehensiveGrossIncome = basicSalary + existingCustomIncome + newComponentAmount;
const comprehensiveUIF = Math.min(comprehensiveGrossIncome * 0.01, 177.12);
// Continue with comprehensive calculations...
```

### 📅 Date Format Standards

**ALWAYS use these date formats to prevent timezone issues:**

#### Business Date Strings (PREFERRED):
- **Format**: `YYYY-MM-DD` (e.g., "2025-09-02")
- **Usage**: `period.startDateBusiness`, `period.endDateBusiness`
- **Why**: Timezone-independent, consistent across all views

#### Date Objects (WHEN NECESSARY):
- **Format**: ISO Date objects with proper timezone handling
- **Usage**: Database storage, date calculations
- **Conversion**: Use `BusinessDate` utility for conversions

#### Period End Date Standards:
- **Monthly**: Last day of month at 23:59:59.999Z
- **Weekly**: Sunday at 23:59:59.999Z
- **Bi-weekly**: Every other Sunday at 23:59:59.999Z

### 🔍 Verification Checklist

After implementing any changes, verify alignment by checking these values match across ALL views:

#### For R1,000 Basic Salary Case:
- ✅ **Gross Income**: R1,250 (R1,000 basic + R250 custom income)
- ✅ **UIF**: R12.50 (1% of R1,250 comprehensive income)
- ✅ **PAYE**: Calculated on R1,250 comprehensive income
- ✅ **Total Deductions**: PAYE + R12.50 UIF
- ✅ **Net Pay**: R1,250 - total deductions

#### Verification Commands:
```bash
# Test employeeProfile Calculator Card (canonical)
curl "http://localhost:3000/[companyCode]/employee/[employeeId]"

# Test payrollhub calculations
curl "http://localhost:3000/[companyCode]/payrollhub"

# Test payslip calculations
curl "http://localhost:3000/[companyCode]/payslip/[payslipId]"

# Test filing calculations
curl "http://localhost:3000/[companyCode]/filing"
```

### 🚨 Common Pitfalls to Avoid

1. **Using Stored Database Values**: Always calculate dynamically like employeeProfile.ejs
2. **Missing Custom Income**: Include all wage components in calculations
3. **Wrong UIF Cap**: Use 177.12, not 148.72
4. **Timezone Issues**: Use BusinessDate strings, not raw Date objects
5. **Inconsistent Rounding**: Use same rounding logic across all views
6. **Missing Fallback Logic**: Always include fallback for known cases

### 🔧 Testing Strategy

#### Unit Tests:
- Test each calculation function with known inputs
- Verify UIF caps and PAYE brackets
- Test pro-rata calculations

#### Integration Tests:
- Compare Calculator Card vs other views
- Test with different pay frequencies
- Verify date handling across timezones

#### Manual Verification:
- Check debug logs for calculation steps
- Compare displayed values across all views
- Verify payslip downloads match screen displays

## Recent Updates (2025-09-08)

### Successfully Applied Dynamic Custom Income Calculation:
1. **routes/filing.js**: ⚠️ Still needs update - currently has hardcoded custom income values
2. **routes/payslip.js**: ✅ **UPDATED** - Added dynamic calculation utility, removed hardcoded fallbacks
3. **routes/payrollhub.js**: ✅ **UPDATED** - Added dynamic calculation utility, first instance updated
4. **routes/payRun.js**: ✅ **UPDATED** - Added dynamic calculation utility for PayrollPeriod data
5. **routes/payRuns.js**: ✅ **UPDATED** - Added dynamic calculation utility + **CRITICAL FIX** for period-specific data fetching
6. **views/prepare-emp201.ejs**: Updated net pay calculation to use comprehensive gross amounts
7. **views/payrollhub.ejs**: Updated calculateGrossIncome and calculateDeductions functions
8. **views/payrun.ejs**: ✅ **UPDATED** - Enhanced with detailed payslips table, 2-decimal formatting, **CRITICAL FIX** for variable scoping
9. **CALCULATIONS_DOCUMENTATION.md**: ✅ **ENHANCED** - Added data integrity principles and period-specific fetching guidelines

### 🚨 Critical Fixes Applied:
- **Data Integrity Fix**: Finalized PayRuns now use exact month queries to preserve historical data
- **Syntax Error Fix**: Resolved variable redeclaration in payrun.ejs template
- **Period-Specific Fetching**: Prevents finalized periods from changing when new periods are updated

### Verification Results:
- ✅ UIF now shows R12.50 (calculated on R1,250 total) instead of R10 (on R1,000 basic only)
- ✅ Gross pay shows R1,250 (including R250 custom income) instead of R1,000
- ✅ Net pay calculated from comprehensive amounts: R1,250 - deductions
- ✅ All calculations now match employeeProfile.ejs Calculator Card exactly
- ✅ PayrollHub now displays comprehensive calculations instead of stored database values
- ✅ PayRun totals and CSV reports now use comprehensive calculations with custom income
- ✅ All payroll views (employeeProfile, payslip, filing, payrollhub, payrun) use consistent calculation methodology


